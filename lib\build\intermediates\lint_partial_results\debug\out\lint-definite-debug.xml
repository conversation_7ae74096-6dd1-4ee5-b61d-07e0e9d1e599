<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="incidents">

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="21"
            column="22"
            startOffset="1418"
            endLine="21"
            endColumn="77"
            endOffset="1473"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="22"
            column="22"
            startOffset="1498"
            endLine="22"
            endColumn="76"
            endOffset="1552"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="38"
            column="9"
            startOffset="2330"
            endLine="38"
            endColumn="63"
            endOffset="2384"/>
    </incident>

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.ACCESS_WIFI_STATE`?">
        <fix-replace
            description="Replace with android.permission.ACCESS_WIFI_STATE"
            oldString="android.permission.ACCESS_WIMAX_STATE"
            replacement="android.permission.ACCESS_WIFI_STATE"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="42"
            column="36"
            startOffset="2614"
            endLine="42"
            endColumn="73"
            endOffset="2651"/>
    </incident>

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.CHANGE_WIFI_STATE`?">
        <fix-replace
            description="Replace with android.permission.CHANGE_WIFI_STATE"
            oldString="android.permission.CHANGE_WIMAX_STATE"
            replacement="android.permission.CHANGE_WIFI_STATE"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="56"
            column="36"
            startOffset="3559"
            endLine="56"
            endColumn="73"
            endOffset="3596"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="57"
            column="22"
            startOffset="3622"
            endLine="57"
            endColumn="71"
            endOffset="3671"/>
    </incident>

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.READ_LOGS`?">
        <fix-replace
            description="Replace with android.permission.READ_LOGS"
            oldString="android.permission.READ_CLIPS"
            replacement="android.permission.READ_LOGS"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="75"
            column="36"
            startOffset="4982"
            endLine="75"
            endColumn="65"
            endOffset="5011"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="94"
            column="22"
            startOffset="6370"
            endLine="94"
            endColumn="69"
            endOffset="6417"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="110"
            column="22"
            startOffset="7543"
            endLine="110"
            endColumn="70"
            endOffset="7591"/>
    </incident>

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.WRITE_OBB`?">
        <fix-replace
            description="Replace with android.permission.WRITE_OBB"
            oldString="android.permission.WRITE_SMS"
            replacement="android.permission.WRITE_OBB"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="111"
            column="36"
            startOffset="7630"
            endLine="111"
            endColumn="64"
            endOffset="7658"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="170"
            column="22"
            startOffset="13195"
            endLine="170"
            endColumn="81"
            endOffset="13254"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="171"
            column="22"
            startOffset="13279"
            endLine="171"
            endColumn="65"
            endOffset="13322"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="189"
            column="22"
            startOffset="14143"
            endLine="189"
            endColumn="76"
            endOffset="14197"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="193"
            column="22"
            startOffset="14345"
            endLine="193"
            endColumn="69"
            endOffset="14392"/>
    </incident>

    <incident
        id="SystemPermissionTypo"
        severity="warning"
        message="Did you mean `android.permission.WRITE_SETTINGS`?">
        <fix-replace
            description="Replace with android.permission.WRITE_SETTINGS"
            oldString="com.android.launcher.permission.WRITE_SETTINGS"
            replacement="android.permission.WRITE_SETTINGS"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="196"
            column="36"
            startOffset="14598"
            endLine="196"
            endColumn="82"
            endOffset="14644"/>
    </incident>

    <incident
        id="ProtectedPermissions"
        severity="error"
        message="Permission is only granted to system apps">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="213"
            column="22"
            startOffset="16026"
            endLine="213"
            endColumn="80"
            endOffset="16084"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Change not allowed&quot;, should use `@string` resource">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/app_not_authorized.xml"
            line="35"
            column="9"
            startOffset="1309"
            endLine="35"
            endColumn="42"
            endOffset="1342"/>
    </incident>

    <incident
        id="ButtonCase"
        severity="warning"
        message="@android:string/yes actually returns &quot;OK&quot;, not &quot;Yes&quot;; use @android:string/ok instead or create a local string resource for Yes">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/app_not_authorized.xml"
            line="53"
            column="13"
            startOffset="2053"
            endLine="53"
            endColumn="47"
            endOffset="2087"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="18"
            column="15"
            startOffset="584"
            endLine="18"
            endColumn="73"
            endOffset="642"/>
    </incident>

    <incident
        id="UseCompoundDrawables"
        severity="warning"
        message="This tag and its children can be replaced by one `&lt;TextView/>` and a compound drawable">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="3"
            column="2"
            startOffset="41"
            endLine="3"
            endColumn="14"
            endOffset="53"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="12"
            column="5"
            startOffset="363"
            endLine="12"
            endColumn="14"
            endOffset="372"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="18"
            column="15"
            startOffset="584"
            endLine="18"
            endColumn="73"
            endOffset="642"/>
    </incident>

    <incident
        id="InefficientWeight"
        severity="warning"
        message="Use a `layout_height` of `0dp` instead of `wrap_content` for better performance">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_type_and_account.xml"
            line="24"
            column="9"
            startOffset="862"
            endLine="24"
            endColumn="45"
            endOffset="898"/>
    </incident>

    <incident
        id="ButtonCase"
        severity="warning"
        message="@android:string/no actually returns &quot;Cancel&quot;, not &quot;No&quot;; use @android:string/cancel instead or create a local string resource for No">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_type_and_account.xml"
            line="51"
            column="13"
            startOffset="1881"
            endLine="51"
            endColumn="46"
            endOffset="1914"/>
    </incident>

    <incident
        id="ButtonCase"
        severity="warning"
        message="@android:string/yes actually returns &quot;OK&quot;, not &quot;Yes&quot;; use @android:string/ok instead or create a local string resource for Yes">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_type_and_account.xml"
            line="60"
            column="13"
            startOffset="2230"
            endLine="60"
            endColumn="47"
            endOffset="2264"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="6"
            column="6"
            startOffset="217"
            endLine="6"
            endColumn="15"
            endOffset="226"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="20"
            column="14"
            startOffset="663"
            endLine="20"
            endColumn="23"
            endOffset="672"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="24"
            column="14"
            startOffset="780"
            endLine="24"
            endColumn="23"
            endOffset="789"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="28"
            column="14"
            startOffset="897"
            endLine="28"
            endColumn="23"
            endOffset="906"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="32"
            column="14"
            startOffset="1014"
            endLine="32"
            endColumn="23"
            endOffset="1023"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="36"
            column="14"
            startOffset="1131"
            endLine="36"
            endColumn="23"
            endOffset="1140"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="40"
            column="14"
            startOffset="1248"
            endLine="40"
            endColumn="23"
            endOffset="1257"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="44"
            column="14"
            startOffset="1365"
            endLine="44"
            endColumn="23"
            endOffset="1374"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="48"
            column="14"
            startOffset="1482"
            endLine="48"
            endColumn="23"
            endOffset="1491"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="58"
            column="14"
            startOffset="1740"
            endLine="58"
            endColumn="23"
            endOffset="1749"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="62"
            column="14"
            startOffset="1857"
            endLine="62"
            endColumn="23"
            endOffset="1866"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="66"
            column="14"
            startOffset="1975"
            endLine="66"
            endColumn="23"
            endOffset="1984"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="70"
            column="14"
            startOffset="2093"
            endLine="70"
            endColumn="23"
            endOffset="2102"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="74"
            column="14"
            startOffset="2211"
            endLine="74"
            endColumn="23"
            endOffset="2220"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="78"
            column="14"
            startOffset="2329"
            endLine="78"
            endColumn="23"
            endOffset="2338"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="82"
            column="14"
            startOffset="2447"
            endLine="82"
            endColumn="23"
            endOffset="2456"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="86"
            column="14"
            startOffset="2565"
            endLine="86"
            endColumn="23"
            endOffset="2574"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="96"
            column="14"
            startOffset="2824"
            endLine="96"
            endColumn="23"
            endOffset="2833"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="100"
            column="14"
            startOffset="2942"
            endLine="100"
            endColumn="23"
            endOffset="2951"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="104"
            column="14"
            startOffset="3060"
            endLine="104"
            endColumn="23"
            endOffset="3069"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="108"
            column="14"
            startOffset="3178"
            endLine="108"
            endColumn="23"
            endOffset="3187"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="112"
            column="14"
            startOffset="3296"
            endLine="112"
            endColumn="23"
            endOffset="3305"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="116"
            column="14"
            startOffset="3414"
            endLine="116"
            endColumn="23"
            endOffset="3423"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="120"
            column="14"
            startOffset="3532"
            endLine="120"
            endColumn="23"
            endOffset="3541"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="124"
            column="14"
            startOffset="3650"
            endLine="124"
            endColumn="23"
            endOffset="3659"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="134"
            column="14"
            startOffset="3909"
            endLine="134"
            endColumn="23"
            endOffset="3918"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="138"
            column="14"
            startOffset="4027"
            endLine="138"
            endColumn="23"
            endOffset="4036"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="142"
            column="14"
            startOffset="4145"
            endLine="142"
            endColumn="23"
            endOffset="4154"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="146"
            column="14"
            startOffset="4263"
            endLine="146"
            endColumn="23"
            endOffset="4272"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="150"
            column="14"
            startOffset="4381"
            endLine="150"
            endColumn="23"
            endOffset="4390"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="154"
            column="14"
            startOffset="4499"
            endLine="154"
            endColumn="23"
            endOffset="4508"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="158"
            column="14"
            startOffset="4617"
            endLine="158"
            endColumn="23"
            endOffset="4626"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml"
            line="162"
            column="14"
            startOffset="4735"
            endLine="162"
            endColumn="23"
            endOffset="4744"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification_lite.xml"
            line="6"
            column="6"
            startOffset="218"
            endLine="6"
            endColumn="15"
            endOffset="227"/>
    </incident>

    <incident
        id="RedundantNamespace"
        severity="warning"
        message="This namespace declaration is redundant">
        <fix-attribute
            description="Delete namespace"
            attribute="xmlns:android"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="39"
            column="19"
            startOffset="1524"
            endLine="39"
            endColumn="77"
            endOffset="1582"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                dot="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="29"
            column="6"
            startOffset="1163"
            endLine="29"
            endColumn="15"
            endOffset="1172"/>
    </incident>

    <incident
        id="UnusedNamespace"
        severity="warning"
        message="Unused namespace declaration xmlns:android; already declared on the root element">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="39"
            column="19"
            startOffset="1524"
            endLine="39"
            endColumn="77"
            endOffset="1582"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/core/InvocationStubManager.java"
            line="146"
            column="8"
            startOffset="6225"
            endLine="146"
            endColumn="47"
            endOffset="6264"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/core/InvocationStubManager.java"
            line="152"
            column="8"
            startOffset="6441"
            endLine="152"
            endColumn="47"
            endOffset="6480"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/core/InvocationStubManager.java"
            line="156"
            column="8"
            startOffset="6537"
            endLine="156"
            endColumn="47"
            endOffset="6576"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/core/InvocationStubManager.java"
            line="159"
            column="8"
            startOffset="6628"
            endLine="159"
            endColumn="41"
            endOffset="6661"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/core/InvocationStubManager.java"
            line="167"
            column="8"
            startOffset="6933"
            endLine="167"
            endColumn="39"
            endOffset="6964"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/env/VirtualRuntime.java"
            line="48"
            column="13"
            startOffset="1268"
            endLine="48"
            endColumn="72"
            endOffset="1327"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/fixer/ActivityFixer.java"
            line="43"
            column="7"
            startOffset="1192"
            endLine="43"
            endColumn="60"
            endOffset="1245"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/fixer/ContextFixer.java"
            line="67"
            column="13"
            startOffset="2257"
            endLine="67"
            endColumn="64"
            endOffset="2308"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/fixer/ContextFixer.java"
            line="70"
            column="13"
            startOffset="2402"
            endLine="70"
            endColumn="72"
            endOffset="2461"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/providers/ProviderHook.java"
            line="148"
            column="21"
            startOffset="5291"
            endLine="148"
            endColumn="80"
            endOffset="5350"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/alarm/AlarmManagerStub.java"
            line="54"
            column="8"
            startOffset="1255"
            endLine="54"
            endColumn="61"
            endOffset="1308"/>
    </incident>

    <incident
        id="PrivateApi"
        severity="warning"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/HCallbackStub.java"
            line="70"
            column="48"
            startOffset="2845"
            endLine="70"
            endColumn="91"
            endOffset="2888"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="273"
            column="17"
            startOffset="8286"
            endLine="273"
            endColumn="84"
            endOffset="8353"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="430"
            column="17"
            startOffset="14640"
            endLine="430"
            endColumn="76"
            endOffset="14699"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="874"
            column="21"
            startOffset="31234"
            endLine="874"
            endColumn="74"
            endOffset="31287"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1010"
            column="17"
            startOffset="36158"
            endLine="1010"
            endColumn="84"
            endOffset="36225"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1214"
            column="56"
            startOffset="41823"
            endLine="1214"
            endColumn="123"
            endOffset="41890"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1218"
            column="59"
            startOffset="41991"
            endLine="1218"
            endColumn="126"
            endOffset="42058"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1221"
            column="53"
            startOffset="42152"
            endLine="1221"
            endColumn="120"
            endOffset="42219"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1289"
            column="21"
            startOffset="45232"
            endLine="1289"
            endColumn="75"
            endOffset="45286"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1592"
            column="51"
            startOffset="57813"
            endLine="1592"
            endColumn="64"
            endOffset="57826"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java"
            line="18"
            column="47"
            startOffset="553"
            endLine="18"
            endColumn="74"
            endOffset="580"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java"
            line="23"
            column="15"
            startOffset="686"
            endLine="23"
            endColumn="42"
            endOffset="713"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/clipboard/ClipBoardStub.java"
            line="39"
            column="13"
            startOffset="1260"
            endLine="39"
            endColumn="71"
            endOffset="1318"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java"
            line="35"
            column="20"
            startOffset="1603"
            endLine="35"
            endColumn="73"
            endOffset="1656"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java"
            line="50"
            column="20"
            startOffset="2906"
            endLine="50"
            endColumn="79"
            endOffset="2965"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/location/LocationManagerStub.java"
            line="41"
            column="13"
            startOffset="1680"
            endLine="41"
            endColumn="66"
            endOffset="1733"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/location/LocationManagerStub.java"
            line="47"
            column="13"
            startOffset="2164"
            endLine="47"
            endColumn="72"
            endOffset="2223"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/location/LocationManagerStub.java"
            line="52"
            column="13"
            startOffset="2419"
            endLine="52"
            endColumn="68"
            endOffset="2474"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/location/LocationManagerStub.java"
            line="56"
            column="13"
            startOffset="2588"
            endLine="56"
            endColumn="72"
            endOffset="2647"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/location/MockLocationHelper.java"
            line="205"
            column="56"
            startOffset="10834"
            endLine="205"
            endColumn="67"
            endOffset="10845"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/mount/MethodProxies.java"
            line="57"
            column="17"
            startOffset="1570"
            endLine="57"
            endColumn="67"
            endOffset="1620"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/notification/MethodProxies.java"
            line="65"
            column="29"
            startOffset="2337"
            endLine="65"
            endColumn="56"
            endOffset="2364"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/notification/MethodProxies.java"
            line="80"
            column="17"
            startOffset="3095"
            endLine="80"
            endColumn="44"
            endOffset="3122"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/telephony/TelephonyRegistryStub.java"
            line="26"
            column="9"
            startOffset="891"
            endLine="26"
            endColumn="47"
            endOffset="929"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/wifi/WifiManagerStub.java"
            line="121"
            column="13"
            startOffset="4198"
            endLine="121"
            endColumn="64"
            endOffset="4249"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/wifi/WifiManagerStub.java"
            line="194"
            column="62"
            startOffset="6879"
            endLine="194"
            endColumn="73"
            endOffset="6890"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/wifi/WifiManagerStub.java"
            line="254"
            column="13"
            startOffset="9244"
            endLine="254"
            endColumn="40"
            endOffset="9271"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/window/WindowManagerStub.java"
            line="28"
            column="13"
            startOffset="792"
            endLine="28"
            endColumn="72"
            endOffset="851"/>
    </incident>

    <incident
        id="SdCardPath"
        severity="warning"
        message="Do not hardcode &quot;`/data/`&quot;; use `Context.getFilesDir().getPath()` instead">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/NativeEngine.java"
            line="127"
            column="43"
            startOffset="3581"
            endLine="127"
            endColumn="73"
            endOffset="3611"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/natives/NativeMethods.java"
            line="25"
            column="17"
            startOffset="523"
            endLine="25"
            endColumn="68"
            endOffset="574"/>
    </incident>

    <incident
        id="DiscouragedPrivateApi"
        severity="warning"
        message="Reflective access to native_setup, which is not part of the public SDK and therefore likely to change in future Android releases">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/natives/NativeMethods.java"
            line="39"
            column="34"
            startOffset="1089"
            endLine="39"
            endColumn="119"
            endOffset="1174"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ChooseTypeAndAccountActivity.java"
            line="354"
            column="13"
            startOffset="15160"
            endLine="354"
            endColumn="66"
            endOffset="15213"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="This `AsyncTask` class should be static or leaks might occur (com.lody.virtual.client.stub.ResolverActivity.LoadIconTask)">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="734"
            column="11"
            startOffset="30532"
            endLine="734"
            endColumn="23"
            endOffset="30544"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="115"
            column="13"
            startOffset="4149"
            endLine="115"
            endColumn="40"
            endOffset="4176"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="319"
            column="25"
            startOffset="11946"
            endLine="319"
            endColumn="52"
            endOffset="11973"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ShortcutHandleActivity.java"
            line="50"
            column="13"
            startOffset="1360"
            endLine="50"
            endColumn="80"
            endOffset="1427"/>
    </incident>

    <incident
        id="MissingSuperCall"
        severity="error"
        message="Calling `super.onCreate` more than once can lead to crashes">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/StubActivity.java"
            line="29"
            column="4"
            startOffset="901"
            endLine="29"
            endColumn="9"
            endOffset="906"/>
    </incident>

    <incident
        id="HandlerLeak"
        severity="warning"
        message="This `Handler` class should be static or leaks might occur (com.lody.virtual.client.VClientImpl.H)">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="689"
            column="19"
            startOffset="28392"
            endLine="689"
            endColumn="20"
            endOffset="28393"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="260"
            column="13"
            startOffset="9206"
            endLine="260"
            endColumn="66"
            endOffset="9259"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="291"
            column="20"
            startOffset="10606"
            endLine="291"
            endColumn="75"
            endOffset="10661"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="303"
            column="13"
            startOffset="11371"
            endLine="303"
            endColumn="64"
            endOffset="11422"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="503"
            column="17"
            startOffset="20753"
            endLine="503"
            endColumn="72"
            endOffset="20808"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="534"
            column="24"
            startOffset="22325"
            endLine="534"
            endColumn="79"
            endOffset="22380"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/VClientImpl.java"
            line="568"
            column="13"
            startOffset="24030"
            endLine="568"
            endColumn="72"
            endOffset="24089"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/ActivityManagerCompat.java"
            line="74"
            column="14"
            startOffset="2428"
            endLine="74"
            endColumn="67"
            endOffset="2481"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/BundleCompat.java"
            line="16"
            column="13"
            startOffset="349"
            endLine="16"
            endColumn="40"
            endOffset="376"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/BundleCompat.java"
            line="24"
            column="13"
            startOffset="619"
            endLine="24"
            endColumn="40"
            endOffset="646"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/ContentProviderCompat.java"
            line="18"
            column="13"
            startOffset="468"
            endLine="18"
            endColumn="65"
            endOffset="520"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/ContentProviderCompat.java"
            line="35"
            column="13"
            startOffset="1064"
            endLine="35"
            endColumn="62"
            endOffset="1113"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/ContentProviderCompat.java"
            line="68"
            column="13"
            startOffset="2372"
            endLine="68"
            endColumn="62"
            endOffset="2421"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/IApplicationThreadCompat.java"
            line="29"
            column="13"
            startOffset="846"
            endLine="29"
            endColumn="64"
            endOffset="897"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/IApplicationThreadCompat.java"
            line="32"
            column="20"
            startOffset="1095"
            endLine="32"
            endColumn="87"
            endOffset="1162"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/IApplicationThreadCompat.java"
            line="42"
            column="13"
            startOffset="1634"
            endLine="42"
            endColumn="64"
            endOffset="1685"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/IApplicationThreadCompat.java"
            line="61"
            column="20"
            startOffset="2719"
            endLine="61"
            endColumn="87"
            endOffset="2786"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/NativeLibraryHelperCompat.java"
            line="24"
            column="7"
            startOffset="663"
            endLine="24"
            endColumn="60"
            endOffset="716"/>
    </incident>

    <incident
        id="AnnotateVersionCheck"
        severity="warning"
        message="This field should be annotated with `ChecksSdkIntAtLeast(extension=0)`">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/PackageParserCompat.java"
            line="44"
            column="30"
            startOffset="1562"
            endLine="44"
            endColumn="39"
            endOffset="1571"/>
    </incident>

    <incident
        id="PrivateApi"
        severity="warning"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/PackageParserCompat.java"
            line="223"
            column="21"
            startOffset="11653"
            endLine="223"
            endColumn="78"
            endOffset="11710"/>
    </incident>

    <incident
        id="PrivateApi"
        severity="warning"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/PackageParserCompat.java"
            line="235"
            column="21"
            startOffset="12275"
            endLine="235"
            endColumn="78"
            endOffset="12332"/>
    </incident>

    <incident
        id="PrivateApi"
        severity="warning"
        message="Accessing internal APIs via reflection is not supported and may not work on all devices or in the future">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/SystemPropertiesCompat.java"
            line="16"
            column="22"
            startOffset="397"
            endLine="16"
            endColumn="66"
            endOffset="441"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/utils/FileUtils.java"
            line="36"
            column="13"
            startOffset="934"
            endLine="36"
            endColumn="66"
            endOffset="987"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/utils/FileUtils.java"
            line="55"
            column="13"
            startOffset="1524"
            endLine="55"
            endColumn="66"
            endOffset="1577"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/utils/Reflect.java"
            line="133"
            column="27"
            startOffset="3363"
            endLine="133"
            endColumn="38"
            endOffset="3374"/>
    </incident>

    <incident
        id="DefaultLocale"
        severity="warning"
        message="Implicitly using the default locale is a common source of bugs: Use `toLowerCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`.">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/utils/Reflect.java"
            line="135"
            column="43"
            startOffset="3437"
            endLine="135"
            endColumn="54"
            endOffset="3448"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/os/VEnvironment.java"
            line="39"
            column="13"
            startOffset="1135"
            endLine="39"
            endColumn="66"
            endOffset="1188"/>
    </incident>

    <incident
        id="ParcelClassLoader"
        severity="warning"
        message="Using the default class loader will not work if you are restoring your own classes. Consider using for example `readBundle(getClass().getClassLoader())` instead.">
        <fix-replace
            description="Use getClass().getClassLoader()"
            oldPattern="(\))"
            replacement="getClass().getClassLoader())"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/remote/PendingResultData.java"
            line="85"
            column="33"
            startOffset="4952"
            endLine="85"
            endColumn="45"
            endOffset="4964"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/remote/vloc/VLocation.java"
            line="92"
            column="13"
            startOffset="2542"
            endLine="92"
            endColumn="72"
            endOffset="2601"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/ActivityStack.java"
            line="223"
            column="13"
            startOffset="8144"
            endLine="223"
            endColumn="66"
            endOffset="8197"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/ActivityStack.java"
            line="301"
            column="13"
            startOffset="11740"
            endLine="301"
            endColumn="66"
            endOffset="11793"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/ActivityStack.java"
            line="414"
            column="17"
            startOffset="16351"
            endLine="414"
            endColumn="69"
            endOffset="16403"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/ActivityStack.java"
            line="421"
            column="17"
            startOffset="16656"
            endLine="421"
            endColumn="72"
            endOffset="16711"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/ActivityStack.java"
            line="507"
            column="13"
            startOffset="20538"
            endLine="507"
            endColumn="72"
            endOffset="20597"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `AttributeCache` which has field `mContext` pointing to `Context`); this is a memory leak">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/AttributeCache.java"
            line="36"
            column="10"
            startOffset="1174"
            endLine="36"
            endColumn="16"
            endOffset="1180"/>
    </incident>

    <incident
        id="StaticFieldLeak"
        severity="warning"
        message="Do not place Android context classes in static fields (static reference to `BroadcastSystem` which has field `mContext` pointing to `Context`); this is a memory leak">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/BroadcastSystem.java"
            line="54"
            column="13"
            startOffset="1646"
            endLine="54"
            endColumn="19"
            endOffset="1652"/>
    </incident>

    <incident
        id="LintError"
        severity="error"
        message="Unexpected failure during lint analysis of BroadcastSystem.java (this is a bug in lint or one of the libraries it depends on)&#xA;&#xA;&#xA;The crash seems to involve the detector `com.android.tools.lint.checks.UnsafeIntentLaunchDetector`.&#xA;You can try disabling it with something like this:&#xA;    android {&#xA;        lint {&#xA;            disable &quot;UnsafeIntentLaunch&quot;&#xA;        }&#xA;    }&#xA;&#xA;Stack: `NullPointerException:BroadcastReceiverUtils$ActionCollectorVisitor.argument(BroadcastReceiverUtils.kt:124)←DataFlowAnalyzer.afterVisitCallExpression(DataFlowAnalyzer.kt:355)←UCallExpression.accept(UCallExpression.kt:90)←UVariableKt.visitContents(UVariable.kt:68)←UVariableKt.access$visitContents(UVariable.kt:1)←ULocalVariable.accept(UVariable.kt:123)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UDeclarationsExpression.accept(UDeclarationsExpression.kt:22)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UForEachExpression.accept(UForEachExpression.kt:38)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←BroadcastReceiverUtils.checkIsProtectedReceiverAndReturnUnprotectedActions(BroadcastReceiverUtils.kt:73)←UnsafeIntentLaunchDetector.isRuntimeReceiverProtected(UnsafeIntentLaunchDetector.kt:304)←UnsafeIntentLaunchDetector.processRuntimeReceiver(UnsafeIntentLaunchDetector.kt:276)←UnsafeIntentLaunchDetector.visitMethodCall(UnsafeIntentLaunchDetector.kt:206)←UElementVisitor$DelegatingPsiVisitor.visitMethodCallExpression(UElementVisitor.kt:1082)←UElementVisitor$DelegatingPsiVisitor.visitCallExpression(UElementVisitor.kt:1062)←UCallExpression.accept(UCallExpression.kt:85)←UQualifiedReferenceExpression.accept(UQualifiedReferenceExpression.kt:34)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UForEachExpression.accept(UForEachExpression.kt:38)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UClass.accept(UClass.kt:64)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UFile.accept(UFile.kt:89)←UastLintUtilsKt.acceptSourceFile(UastLintUtils.kt:826)←UElementVisitor$visitFile$3.run(UElementVisitor.kt:267)←LintClient.runReadAction(LintClient.kt:1700)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)←UElementVisitor.visitFile(UElementVisitor.kt:264)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2165)←LintClient.runReadAction(LintClient.kt:1700)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)←LintDriver.visitUastDetectors(LintDriver.kt:2165)←LintDriver.visitUast(LintDriver.kt:2127)←LintDriver.runFileDetectors(LintDriver.kt:1379)←LintDriver.checkProject(LintDriver.kt:1144)←LintDriver.checkProjectRoot(LintDriver.kt:615)←LintDriver.access$checkProjectRoot(LintDriver.kt:170)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:441)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:438)←LintDriver.doAnalyze(LintDriver.kt:497)←LintDriver.analyzeOnly(LintDriver.kt:438)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)←LintCliClient.run(LintCliClient.kt:279)←LintCliClient.run$default(LintCliClient.kt:262)←LintCliClient.analyzeOnly(LintCliClient.kt:237)←Main.run(Main.java:1689)←Main.run(Main.java:275)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:98)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:87)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:62)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:73)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:170)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:187)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:120)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:162)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:264)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:128)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:133)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:157)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:126)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:47)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)`&#xA;&#xA;You can run with --stacktrace or set environment variable `LINT_PRINT_STACKTRACE=true` to dump a full stacktrace to stdout.">
        <fix-data/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/BroadcastSystem.java"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/VActivityManagerService.java"
            line="477"
            column="21"
            startOffset="17743"
            endLine="477"
            endColumn="73"
            endOffset="17795"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/BinderProvider.java"
            line="64"
            column="13"
            startOffset="2783"
            endLine="64"
            endColumn="66"
            endOffset="2836"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompat.java"
            line="45"
            column="13"
            startOffset="1682"
            endLine="45"
            endColumn="66"
            endOffset="1735"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV14.java"
            line="35"
            column="21"
            startOffset="1113"
            endLine="35"
            endColumn="76"
            endOffset="1168"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV14.java"
            line="60"
            column="13"
            startOffset="2494"
            endLine="60"
            endColumn="68"
            endOffset="2549"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV14.java"
            line="70"
            column="13"
            startOffset="3061"
            endLine="70"
            endColumn="66"
            endOffset="3114"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV21.java"
            line="86"
            column="13"
            startOffset="3394"
            endLine="86"
            endColumn="68"
            endOffset="3449"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV21.java"
            line="92"
            column="13"
            startOffset="3612"
            endLine="92"
            endColumn="66"
            endOffset="3665"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationFixer.java"
            line="196"
            column="17"
            startOffset="8937"
            endLine="196"
            endColumn="69"
            endOffset="8989"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/RemoteViewsFixer.java"
            line="232"
            column="17"
            startOffset="8739"
            endLine="232"
            endColumn="44"
            endOffset="8766"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/RemoteViewsFixer.java"
            line="260"
            column="48"
            startOffset="10314"
            endLine="260"
            endColumn="61"
            endOffset="10327"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/WidthCompat.java"
            line="32"
            column="17"
            startOffset="977"
            endLine="32"
            endColumn="44"
            endOffset="1004"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/WidthCompat.java"
            line="47"
            column="13"
            startOffset="1670"
            endLine="47"
            endColumn="40"
            endOffset="1697"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/WidthCompat.java"
            line="131"
            column="40"
            startOffset="5408"
            endLine="131"
            endColumn="53"
            endOffset="5421"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/IntentResolver.java"
            line="149"
            column="7"
            startOffset="4552"
            endLine="149"
            endColumn="58"
            endOffset="4603"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/parser/PackageParserEx.java"
            line="297"
            column="13"
            startOffset="12334"
            endLine="297"
            endColumn="66"
            endOffset="12387"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/parser/PackageParserEx.java"
            line="325"
            column="13"
            startOffset="13884"
            endLine="325"
            endColumn="66"
            endOffset="13937"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/parser/VPackage.java"
            line="291"
            column="17"
            startOffset="10814"
            endLine="291"
            endColumn="67"
            endOffset="10864"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VAppManagerService.java"
            line="298"
            column="17"
            startOffset="11261"
            endLine="298"
            endColumn="70"
            endOffset="11314"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="88"
            column="55"
            startOffset="3225"
            endLine="88"
            endColumn="106"
            endOffset="3276"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="148"
            column="17"
            startOffset="5690"
            endLine="148"
            endColumn="68"
            endOffset="5741"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="196"
            column="17"
            startOffset="7497"
            endLine="196"
            endColumn="68"
            endOffset="7548"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="439"
            column="17"
            startOffset="17331"
            endLine="439"
            endColumn="84"
            endOffset="17398"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="477"
            column="17"
            startOffset="18788"
            endLine="477"
            endColumn="84"
            endOffset="18855"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="530"
            column="17"
            startOffset="20781"
            endLine="530"
            endColumn="84"
            endOffset="20848"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-data conditional="true"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="569"
            column="17"
            startOffset="22273"
            endLine="569"
            endColumn="84"
            endOffset="22340"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is never &lt; 21">
        <fix-data conditional="false"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/ActivityThread.java"
            line="52"
            column="13"
            startOffset="1955"
            endLine="52"
            endColumn="80"
            endOffset="2022"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="34"
            replacement="36"/>
        <location
            file="${:lib*projectDir}/build.gradle"
            line="11"
            column="9"
            startOffset="150"
            endLine="11"
            endColumn="21"
            endOffset="162"/>
    </incident>

    <incident
        id="InflateParams"
        severity="warning"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ChooseAccountTypeActivity.java"
            line="160"
            column="84"
            startOffset="6285"
            endLine="160"
            endColumn="88"
            endOffset="6289"/>
    </incident>

</incidents>
