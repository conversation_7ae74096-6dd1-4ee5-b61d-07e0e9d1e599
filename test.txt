PS D:\桌面> adb logcat -s "AndroidRuntime:E"
--------- beginning of crash
--------- beginning of system
08-03 12:07:08.506 14005 14005 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:07:08.506 14005 14005 E AndroidRuntime: Process: io.virtualapp, PID: 14005
08-03 12:07:08.506 14005 14005 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:07:08.506 14005 14005 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:11:26.476 18363 18363 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:11:26.476 18363 18363 E AndroidRuntime: Process: io.virtualapp:p0, PID: 18363
08-03 12:11:26.476 18363 18363 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:11:26.476 18363 18363 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:11:31.741 18465 18465 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:11:31.741 18465 18465 E AndroidRuntime: Process: io.virtualapp:p0, PID: 18465
08-03 12:11:31.741 18465 18465 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:11:31.741 18465 18465 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:11:31.930 18491 18491 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:11:31.930 18491 18491 E AndroidRuntime: Process: io.virtualapp:p0, PID: 18491
08-03 12:11:31.930 18491 18491 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:11:31.930 18491 18491 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:12:16.518 18515 18515 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:12:16.518 18515 18515 E AndroidRuntime: Process: io.virtualapp:p0, PID: 18515
08-03 12:12:16.518 18515 18515 E AndroidRuntime: java.lang.RuntimeException: Unable to start activity ComponentInfo{io.virtualapp/com.lody.virtual.client.stub.StubActivity$C0}: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Resources$Theme android.content.res.Resources.newTheme()' on a null object reference
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3539)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3699)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:85)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:135)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:95)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2135)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:12:16.518 18515 18515 E AndroidRuntime: Caused by: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Resources$Theme android.content.res.Resources.newTheme()' on a null object reference
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ContextImpl.initializeTheme(ContextImpl.java:404)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ContextImpl.getTheme(ContextImpl.java:396)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.view.ContextThemeWrapper.initializeTheme(ContextThemeWrapper.java:211)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.view.ContextThemeWrapper.setTheme(ContextThemeWrapper.java:147)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.policy.PhoneWindow.generateDecor(PhoneWindow.java:2354)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.policy.PhoneWindow.originalInstallDecor(PhoneWindow.java:2742)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.policy.PhoneWindow.installDecor(PhoneWindow.java:2734)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at com.android.internal.policy.PhoneWindow.getDecorView(PhoneWindow.java:2120)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityTransitionState.setEnterActivityOptions(ActivityTransitionState.java:171)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.Activity.performCreate(Activity.java:8155)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.Activity.performCreate(Activity.java:8113)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.Instrumentation.callActivityOnCreate(Instrumentation.java:1310)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3512)
08-03 12:12:16.518 18515 18515 E AndroidRuntime:        ... 11 more
08-03 12:12:19.199 19642 19642 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:12:19.199 19642 19642 E AndroidRuntime: Process: io.virtualapp:p0, PID: 19642
08-03 12:12:19.199 19642 19642 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:12:19.199 19642 19642 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
--------- beginning of kernel
08-03 12:12:37.374 19760 19760 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:12:37.374 19760 19760 E AndroidRuntime: Process: io.virtualapp:p0, PID: 19760
08-03 12:12:37.374 19760 19760 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:12:37.374 19760 19760 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:12:37.570 19785 19785 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:12:37.570 19785 19785 E AndroidRuntime: Process: io.virtualapp:p0, PID: 19785
08-03 12:12:37.570 19785 19785 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:12:37.570 19785 19785 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:13:05.551 20506 20506 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:13:05.551 20506 20506 E AndroidRuntime: Process: io.virtualapp:p0, PID: 20506
08-03 12:13:05.551 20506 20506 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:13:05.551 20506 20506 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:13:22.862 20630 20630 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:13:22.862 20630 20630 E AndroidRuntime: Process: io.virtualapp:p0, PID: 20630
08-03 12:13:22.862 20630 20630 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:13:22.862 20630 20630 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:13:23.042 20653 20653 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:13:23.042 20653 20653 E AndroidRuntime: Process: io.virtualapp:p0, PID: 20653
08-03 12:13:23.042 20653 20653 E AndroidRuntime: java.lang.NullPointerException: Attempt to invoke virtual method 'android.content.res.Configuration android.content.res.Resources.getConfiguration()' on a null object reference
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.app.ActivityThread.updateLocaleListFromAppContext(ActivityThread.java:6582)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6831)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:13:23.042 20653 20653 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
--------- beginning of main
08-03 12:17:49.382 20676 20676 E AndroidRuntime: FATAL EXCEPTION: main
08-03 12:17:49.382 20676 20676 E AndroidRuntime: Process: io.virtualapp:p0, PID: 20676
08-03 12:17:49.382 20676 20676 E AndroidRuntime: java.lang.RuntimeException: Unable to start activity ComponentInfo{io.virtualapp/com.lody.virtual.client.stub.StubActivity$C0}: java.lang.NullPointerException: Attempt to invoke virtual method 'void android.content.res.Resources.addLoaders(android.content.res.loader.ResourcesLoader[])' on a null object reference
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3539)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.ActivityThread.handleLaunchActivity(ActivityThread.java:3699)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.servertransaction.LaunchActivityItem.execute(LaunchActivityItem.java:85)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.servertransaction.TransactionExecutor.executeCallbacks(TransactionExecutor.java:135)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.servertransaction.TransactionExecutor.execute(TransactionExecutor.java:95)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2135)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.os.Looper.loop(Looper.java:236)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at java.lang.reflect.Method.invoke(Native Method)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 12:17:49.382 20676 20676 E AndroidRuntime: Caused by: java.lang.NullPointerException: Attempt to invoke virtual method 'void android.content.res.Resources.addLoaders(android.content.res.loader.ResourcesLoader[])' on a null object reference
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        at android.app.ActivityThread.performLaunchActivity(ActivityThread.java:3487)
08-03 12:17:49.382 20676 20676 E AndroidRuntime:        ... 11 more