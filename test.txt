08-03 10:52:15.095  2871  5421 W System.err: java.lang.NoSuchFieldException: No field packageName in class Landroid/view/RemoteAnimationTarget; (declaration of 'android.view.RemoteAnimationTarget' appears in /system/framework/framework.jar!classes3.dex)
08-03 10:52:15.101  2871  5421 W System.err:    at java.lang.Class.getDeclaredField(Native Method)
08-03 10:52:15.101  2871  5421 W System.err:    at miui.util.ReflectionUtils.findFieldRecursiveImpl(ReflectionUtils.java:143)
08-03 10:52:15.101  2871  5421 W System.err:    at miui.util.ReflectionUtils.findField(ReflectionUtils.java:111)
08-03 10:52:15.101  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationTargetCompat.<init>(RemoteAnimationTargetCompat.java:69)
08-03 10:52:15.101  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationTargetCompat.wrap(RemoteAnimationTargetCompat.java:99)
08-03 10:52:15.101  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationAdapterCompat$1.onAnimationStart(RemoteAnimationAdapterCompat.java:50)
08-03 10:52:15.101  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationAdapterCompat$1.onAnimationStart(RemoteAnimationAdapterCompat.java:68)
08-03 10:52:15.101  2871  5421 W System.err:    at android.view.IRemoteAnimationRunner$Stub.onTransact(IRemoteAnimationRunner.java:108)
08-03 10:52:15.102  2871  5421 W System.err:    at android.os.Binder.execTransactInternal(Binder.java:1159)
08-03 10:52:15.102  2871  5421 W System.err:    at android.os.Binder.execTransact(Binder.java:1123)
08-03 10:52:15.102  2871  5421 W System.err: java.lang.NoSuchFieldException: android.view.RemoteAnimationTarget#packageName
08-03 10:52:15.102  2871  5421 W System.err:    at miui.util.ReflectionUtils.findField(ReflectionUtils.java:105)
08-03 10:52:15.102  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationTargetCompat.<init>(RemoteAnimationTargetCompat.java:69)
08-03 10:52:15.102  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationTargetCompat.wrap(RemoteAnimationTargetCompat.java:99)
08-03 10:52:15.102  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationAdapterCompat$1.onAnimationStart(RemoteAnimationAdapterCompat.java:50)
08-03 10:52:15.102  2871  5421 W System.err:    at com.android.systemui.shared.recents.system.RemoteAnimationAdapterCompat$1.onAnimationStart(RemoteAnimationAdapterCompat.java:68)
08-03 10:52:15.102  2871  5421 W System.err:    at android.view.IRemoteAnimationRunner$Stub.onTransact(IRemoteAnimationRunner.java:108)
08-03 10:52:15.102  2871  5421 W System.err:    at android.os.Binder.execTransactInternal(Binder.java:1159)
08-03 10:52:15.102  2871  5421 W System.err:    at android.os.Binder.execTransact(Binder.java:1123)
08-03 10:52:15.742  8189  8189 W System.err: SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
08-03 10:52:15.742  8189  8189 W System.err: SLF4J: Defaulting to no-operation (NOP) logger implementation
08-03 10:52:15.742  8189  8189 W System.err: SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.
08-03 10:52:16.260  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.collectCertificates [class android.content.pm.PackageParser$Package, int]
08-03 10:52:16.260  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.260  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.260  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.260  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.260  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.260  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.261  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.261  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserNougat.<clinit>(PackageParserNougat.java:12)
08-03 10:52:16.261  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:225)
08-03 10:52:16.261  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.261  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.261  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.261  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.261  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.261  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.261  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.262  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.262  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.262  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.270  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generatePackageInfo [class android.content.pm.PackageParser$Package, class [I, int, long, long, class android.util.ArraySet, class android.content.pm.PackageUserState]
08-03 10:52:16.270  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.270  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.270  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.270  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.271  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.271  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.271  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.271  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserLollipop22.<clinit>(PackageParserLollipop22.java:20)
08-03 10:52:16.271  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:227)
08-03 10:52:16.271  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.271  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.271  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.271  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.271  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.271  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.271  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.272  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.272  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.272  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.275  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generatePackageInfo [class android.content.pm.PackageParser$Package, class [I, int, long, long, class java.util.HashSet, class android.content.pm.PackageUserState]
08-03 10:52:16.275  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.275  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.275  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.275  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.275  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.275  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.275  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.275  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserLollipop.<clinit>(PackageParserLollipop.java:20)
08-03 10:52:16.275  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:228)
08-03 10:52:16.275  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.275  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.276  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.276  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.276  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.276  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.276  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.276  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.276  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.276  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.280  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generatePackageInfo [class android.content.pm.PackageParser$Package, class [I, int, long, long, class java.util.HashSet, class android.content.pm.PackageUserState]
08-03 10:52:16.280  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.281  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.281  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.281  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.281  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.281  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.281  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.281  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean17.<clinit>(PackageParserJellyBean17.java:21)
08-03 10:52:16.281  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:229)
08-03 10:52:16.281  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.281  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.281  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.281  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.281  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.281  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.282  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.282  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.282  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.282  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.282  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.287  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateActivityInfo [class android.content.pm.PackageParser$Activity, int, boolean, int, int]
08-03 10:52:16.287  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.287  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.287  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.287  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.287  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.287  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.287  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.287  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean.<clinit>(PackageParserJellyBean.java:21)
08-03 10:52:16.287  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:230)
08-03 10:52:16.287  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.288  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.288  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.288  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.288  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.288  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.288  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.288  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.288  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.288  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.289  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateApplicationInfo [class android.content.pm.PackageParser$Package, int, boolean, int]
08-03 10:52:16.289  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.289  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.289  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.289  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.289  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.289  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.289  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.289  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean.<clinit>(PackageParserJellyBean.java:21)
08-03 10:52:16.289  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:230)
08-03 10:52:16.289  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.289  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.289  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.290  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.290  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.290  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.290  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.290  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.290  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.290  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.291  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generatePackageInfo [class android.content.pm.PackageParser$Package, class [I, int, long, long, class java.util.HashSet]
08-03 10:52:16.291  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.291  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.291  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.291  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.291  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.291  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.291  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.291  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean.<clinit>(PackageParserJellyBean.java:21)
08-03 10:52:16.291  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:230)
08-03 10:52:16.291  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.292  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.292  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.292  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.292  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.292  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.292  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.292  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.292  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.292  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.293  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateProviderInfo [class android.content.pm.PackageParser$Provider, int, boolean, int, int]
08-03 10:52:16.293  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.293  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.293  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.293  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.293  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.293  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.293  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.293  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean.<clinit>(PackageParserJellyBean.java:21)
08-03 10:52:16.293  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:230)
08-03 10:52:16.294  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.294  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.294  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.294  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.294  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.294  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.294  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.294  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.294  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.294  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.295  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateServiceInfo [class android.content.pm.PackageParser$Service, int, boolean, int, int]
08-03 10:52:16.295  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.295  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.296  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.296  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.296  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.296  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.296  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.296  8220  8220 W System.err:    at mirror.android.content.pm.PackageParserJellyBean.<clinit>(PackageParserJellyBean.java:21)
08-03 10:52:16.296  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:230)
08-03 10:52:16.296  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.296  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.296  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.296  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.296  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.296  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.296  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.296  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.296  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.296  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.301  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateActivityInfo [class android.content.pm.PackageParser$Activity, int]
08-03 10:52:16.301  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.301  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.301  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.301  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.301  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.301  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.301  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.301  8220  8220 W System.err:    at mirror.android.content.pm.PackageParser.<clinit>(PackageParser.java:33)
08-03 10:52:16.301  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:231)
08-03 10:52:16.301  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.301  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.301  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.301  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.301  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.301  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.302  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.302  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.302  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.302  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.302  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.302  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.302  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.302  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.302  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.302  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.303  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateApplicationInfo [class android.content.pm.PackageParser$Package, int]
08-03 10:52:16.303  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.303  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.303  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.303  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.303  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.303  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.303  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.303  8220  8220 W System.err:    at mirror.android.content.pm.PackageParser.<clinit>(PackageParser.java:33)
08-03 10:52:16.303  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:231)
08-03 10:52:16.303  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.303  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.303  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.304  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.304  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.304  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.304  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.304  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.304  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.304  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.305  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generatePackageInfo [class android.content.pm.PackageParser$Package, class [I, int, long, long]
08-03 10:52:16.305  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.305  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.305  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.305  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.305  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.305  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.305  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.305  8220  8220 W System.err:    at mirror.android.content.pm.PackageParser.<clinit>(PackageParser.java:33)
08-03 10:52:16.306  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:231)
08-03 10:52:16.306  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.306  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.306  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.306  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.306  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.306  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.306  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.306  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.306  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.306  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.307  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateProviderInfo [class android.content.pm.PackageParser$Provider, int]
08-03 10:52:16.307  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.307  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.307  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.307  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.307  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.307  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.307  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.307  8220  8220 W System.err:    at mirror.android.content.pm.PackageParser.<clinit>(PackageParser.java:33)
08-03 10:52:16.307  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:231)
08-03 10:52:16.307  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.307  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.307  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.307  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.307  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.307  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.307  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.307  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.308  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.308  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.308  8220  8220 W System.err: java.lang.NoSuchMethodException: android.content.pm.PackageParser.generateServiceInfo [class android.content.pm.PackageParser$Service, int]
08-03 10:52:16.309  8220  8220 W System.err:    at java.lang.Class.getMethod(Class.java:2072)
08-03 10:52:16.309  8220  8220 W System.err:    at java.lang.Class.getDeclaredMethod(Class.java:2050)
08-03 10:52:16.309  8220  8220 W System.err:    at mirror.RefStaticMethod.<init>(RefStaticMethod.java:61)
08-03 10:52:16.309  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance0(Native Method)
08-03 10:52:16.309  8220  8220 W System.err:    at java.lang.reflect.Constructor.newInstance(Constructor.java:343)
08-03 10:52:16.309  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:46)
08-03 10:52:16.309  8220  8220 W System.err:    at mirror.RefClass.load(RefClass.java:32)
08-03 10:52:16.309  8220  8220 W System.err:    at mirror.android.content.pm.PackageParser.<clinit>(PackageParser.java:33)
08-03 10:52:16.309  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.initMirrorSystem(InvocationStubManager.java:231)
08-03 10:52:16.309  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.injectInternal(InvocationStubManager.java:116)
08-03 10:52:16.309  8220  8220 W System.err:    at com.lody.virtual.client.core.InvocationStubManager.init(InvocationStubManager.java:105)
08-03 10:52:16.309  8220  8220 W System.err:    at com.lody.virtual.client.core.VirtualCore.startup(VirtualCore.java:198)
08-03 10:52:16.309  8220  8220 W System.err:    at io.virtualapp.VApp.attachBaseContext(VApp.java:36)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.309  8220  8220 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.309  8220  8220 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.309  8220  8220 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.309  8220  8220 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.309  8220  8220 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.309  8220  8220 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:16.852  8252  8252 W System.err: android.content.pm.PackageManager$NameNotFoundException
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:207)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ApplicationPackageManager.getPackageInfo(ApplicationPackageManager.java:179)
08-03 10:52:16.853  8252  8252 W System.err:    at com.miui.internal.util.PackageHelper.getPackageInfo(PackageHelper.java:107)
08-03 10:52:16.853  8252  8252 W System.err:    at com.miui.internal.util.PackageHelper.getApkPath(PackageHelper.java:37)
08-03 10:52:16.853  8252  8252 W System.err:    at com.miui.internal.component.module.ModuleLoader.loadModules(ModuleLoader.java:78)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.module.ModuleManager.loadModules(ModuleManager.java:153)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.module.ModuleManager.loadModules(ModuleManager.java:96)
08-03 10:52:16.853  8252  8252 W System.err:    at com.miui.internal.component.ComponentManager.loadModules(ComponentManager.java:48)
08-03 10:52:16.853  8252  8252 W System.err:    at com.miui.internal.component.ComponentManager.load(ComponentManager.java:43)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.core.SdkManager.loadComponents(SdkManager.java:156)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.core.SdkManager.start(SdkManager.java:139)
08-03 10:52:16.853  8252  8252 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.external.Application.startSdk(Application.java:103)
08-03 10:52:16.853  8252  8252 W System.err:    at miui.external.Application.attachBaseContext(Application.java:165)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.Application.attach(Application.java:353)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.Instrumentation.newApplication(Instrumentation.java:1160)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.LoadedApk.makeApplication(LoadedApk.java:1240)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ActivityThread.handleBindApplication(ActivityThread.java:6946)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ActivityThread.access$1500(ActivityThread.java:257)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1982)
08-03 10:52:16.853  8252  8252 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:16.853  8252  8252 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:16.853  8252  8252 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:16.853  8252  8252 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:16.853  8252  8252 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:16.853  8252  8252 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:18.184  8284  8284 W System.err: java.io.FileNotFoundException: /mnt/vendor/persist/alarm/data: open failed: EACCES (Permission denied)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:492)
08-03 10:52:18.185  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:160)
08-03 10:52:18.185  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:115)
08-03 10:52:18.185  8284  8284 W System.err:    at java.io.FileReader.<init>(FileReader.java:58)
08-03 10:52:18.185  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmUtils.readPowerOffAlarmFile(PowerOffAlarmUtils.java:89)
08-03 10:52:18.185  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmPersistData.<init>(PowerOffAlarmPersistData.java:29)
08-03 10:52:18.185  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmBroadcastReceiver.onReceive(PowerOffAlarmBroadcastReceiver.java:59)
08-03 10:52:18.185  8284  8284 W System.err:    at android.app.ActivityThread.handleReceiver(ActivityThread.java:4130)
08-03 10:52:18.185  8284  8284 W System.err:    at android.app.ActivityThread.access$1600(ActivityThread.java:257)
08-03 10:52:18.185  8284  8284 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1993)
08-03 10:52:18.185  8284  8284 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:18.185  8284  8284 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:18.185  8284  8284 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:18.185  8284  8284 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:18.185  8284  8284 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:18.185  8284  8284 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:18.185  8284  8284 W System.err: Caused by: android.system.ErrnoException: open failed: EACCES (Permission denied)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.Linux.open(Native Method)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.185  8284  8284 W System.err:    at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7911)
08-03 10:52:18.185  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:478)
08-03 10:52:18.185  8284  8284 W System.err:    ... 15 more
08-03 10:52:18.254  8284  8284 W System.err: java.io.FileNotFoundException: /mnt/vendor/persist/alarm/data: open failed: EACCES (Permission denied)
08-03 10:52:18.254  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:492)
08-03 10:52:18.254  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:160)
08-03 10:52:18.254  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:115)
08-03 10:52:18.254  8284  8284 W System.err:    at java.io.FileReader.<init>(FileReader.java:58)
08-03 10:52:18.254  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmUtils.readPowerOffAlarmFile(PowerOffAlarmUtils.java:89)
08-03 10:52:18.254  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmPersistData.<init>(PowerOffAlarmPersistData.java:29)
08-03 10:52:18.254  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmBroadcastReceiver.onReceive(PowerOffAlarmBroadcastReceiver.java:59)
08-03 10:52:18.254  8284  8284 W System.err:    at android.app.ActivityThread.handleReceiver(ActivityThread.java:4130)
08-03 10:52:18.255  8284  8284 W System.err:    at android.app.ActivityThread.access$1600(ActivityThread.java:257)
08-03 10:52:18.255  8284  8284 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1993)
08-03 10:52:18.255  8284  8284 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:18.255  8284  8284 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:18.255  8284  8284 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:18.255  8284  8284 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:18.255  8284  8284 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:18.255  8284  8284 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:18.255  8284  8284 W System.err: Caused by: android.system.ErrnoException: open failed: EACCES (Permission denied)
08-03 10:52:18.255  8284  8284 W System.err:    at libcore.io.Linux.open(Native Method)
08-03 10:52:18.255  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.255  8284  8284 W System.err:    at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
08-03 10:52:18.255  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.255  8284  8284 W System.err:    at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7911)
08-03 10:52:18.255  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:478)
08-03 10:52:18.255  8284  8284 W System.err:    ... 15 more
08-03 10:52:18.266  8284  8284 W System.err: java.io.FileNotFoundException: /mnt/vendor/persist/alarm/data: open failed: EACCES (Permission denied)
08-03 10:52:18.266  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:492)
08-03 10:52:18.266  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:160)
08-03 10:52:18.266  8284  8284 W System.err:    at java.io.FileInputStream.<init>(FileInputStream.java:115)
08-03 10:52:18.266  8284  8284 W System.err:    at java.io.FileReader.<init>(FileReader.java:58)
08-03 10:52:18.266  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmUtils.readPowerOffAlarmFile(PowerOffAlarmUtils.java:89)
08-03 10:52:18.266  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmPersistData.<init>(PowerOffAlarmPersistData.java:29)
08-03 10:52:18.266  8284  8284 W System.err:    at com.qualcomm.qti.poweroffalarm.PowerOffAlarmBroadcastReceiver.onReceive(PowerOffAlarmBroadcastReceiver.java:59)
08-03 10:52:18.266  8284  8284 W System.err:    at android.app.ActivityThread.handleReceiver(ActivityThread.java:4130)
08-03 10:52:18.266  8284  8284 W System.err:    at android.app.ActivityThread.access$1600(ActivityThread.java:257)
08-03 10:52:18.266  8284  8284 W System.err:    at android.app.ActivityThread$H.handleMessage(ActivityThread.java:1993)
08-03 10:52:18.266  8284  8284 W System.err:    at android.os.Handler.dispatchMessage(Handler.java:106)
08-03 10:52:18.266  8284  8284 W System.err:    at android.os.Looper.loop(Looper.java:236)
08-03 10:52:18.266  8284  8284 W System.err:    at android.app.ActivityThread.main(ActivityThread.java:8037)
08-03 10:52:18.266  8284  8284 W System.err:    at java.lang.reflect.Method.invoke(Native Method)
08-03 10:52:18.266  8284  8284 W System.err:    at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:656)
08-03 10:52:18.266  8284  8284 W System.err:    at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:967)
08-03 10:52:18.267  8284  8284 W System.err: Caused by: android.system.ErrnoException: open failed: EACCES (Permission denied)
08-03 10:52:18.267  8284  8284 W System.err:    at libcore.io.Linux.open(Native Method)
08-03 10:52:18.267  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.267  8284  8284 W System.err:    at libcore.io.BlockGuardOs.open(BlockGuardOs.java:254)
08-03 10:52:18.267  8284  8284 W System.err:    at libcore.io.ForwardingOs.open(ForwardingOs.java:166)
08-03 10:52:18.267  8284  8284 W System.err:    at android.app.ActivityThread$AndroidOs.open(ActivityThread.java:7911)
08-03 10:52:18.267  8284  8284 W System.err:    at libcore.io.IoBridge.open(IoBridge.java:478)
08-03 10:52:18.267  8284  8284 W System.err:    ... 15 more