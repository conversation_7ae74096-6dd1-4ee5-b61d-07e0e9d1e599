Android NDK: WARNING: Ignoring unknown import directory: :D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    
Android NDK: WARNING:D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/Android.mk:fb: LOCAL_LDLIBS is always ignored for static libraries    
Android NDK: WARNING:D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\jni\Android.mk:va++: non-system libraries in linker flags: -latomic    
Android NDK:     This is likely to result in incorrect builds. Try using LOCAL_STATIC_LIBRARIES    
Android NDK:     or LOCAL_SHARED_LIBRARIES instead to list the library dependencies of the    
Android NDK:     current module    
[armeabi-v7a] Clean          : android_support [armeabi-v7a]
[armeabi-v7a] Clean          : c++_shared [armeabi-v7a]
[armeabi-v7a] Clean          : c++_static [armeabi-v7a]
[armeabi-v7a] Clean          : c++abi [armeabi-v7a]
[armeabi-v7a] Clean          : fb [armeabi-v7a]
[armeabi-v7a] Clean          : unwind [armeabi-v7a]
[armeabi-v7a] Clean          : va++ [armeabi-v7a]
