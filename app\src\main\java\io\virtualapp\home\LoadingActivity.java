package io.virtualapp.home;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.RemoteException;
import android.widget.ImageView;
import android.widget.TextView;

import com.lody.virtual.client.core.VirtualCore;
import com.lody.virtual.client.ipc.VActivityManager;

import java.util.Locale;

import io.virtualapp.R;
import io.virtualapp.abs.ui.VActivity;
import io.virtualapp.abs.ui.VUiKit;
import io.virtualapp.home.models.PackageAppData;
import io.virtualapp.home.repo.PackageAppDataStorage;
import io.virtualapp.widgets.EatBeansView;

/**
 * <AUTHOR>
 */

public class LoadingActivity extends VActivity {

    private static final String PKG_NAME_ARGUMENT = "MODEL_ARGUMENT";
    private static final String KEY_INTENT = "KEY_INTENT";
    private static final String KEY_USER = "KEY_USER";
    private PackageAppData appModel;
    private EatBeansView loadingView;
    private Runnable timeoutRunnable;

    public static void launch(Context context, String packageName, int userId) {
        Intent intent = VirtualCore.get().getLaunchIntent(packageName, userId);
        if (intent != null) {
            Intent loadingPageIntent = new Intent(context, LoadingActivity.class);
            loadingPageIntent.putExtra(PKG_NAME_ARGUMENT, packageName);
            loadingPageIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            loadingPageIntent.putExtra(KEY_INTENT, intent);
            loadingPageIntent.putExtra(KEY_USER, userId);
            context.startActivity(loadingPageIntent);
        }
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_loading);
        loadingView = (EatBeansView) findViewById(R.id.loading_anim);
        int userId = getIntent().getIntExtra(KEY_USER, -1);
        String pkg = getIntent().getStringExtra(PKG_NAME_ARGUMENT);
        appModel = PackageAppDataStorage.get().acquire(pkg);
        ImageView iconView = (ImageView) findViewById(R.id.app_icon);
        iconView.setImageDrawable(appModel.icon);
        TextView nameView = (TextView) findViewById(R.id.app_name);
        nameView.setText(String.format(Locale.ENGLISH, "Opening %s...", appModel.name));
        Intent intent = getIntent().getParcelableExtra(KEY_INTENT);
        if (intent == null) {
            android.util.Log.e("LoadingActivity", "Intent is null, finishing activity");
            finish();
            return;
        }
        
        // 设置超时机制：10秒后如果还没有启动成功就强制关闭
        timeoutRunnable = () -> {
            android.util.Log.w("LoadingActivity", "App launch timeout for " + pkg + ", finishing loading activity");
            android.widget.Toast.makeText(LoadingActivity.this, 
                "应用启动超时: " + appModel.name, 
                android.widget.Toast.LENGTH_SHORT).show();
            finish();
        };
        loadingView.postDelayed(timeoutRunnable, 10000);
        
        VirtualCore.get().setUiCallback(intent, mUiCallback);
        VUiKit.defer().when(() -> {
            android.util.Log.d("LoadingActivity", "Starting app launch process for " + pkg + " (userId: " + userId + ")");
            
            if (!appModel.fastOpen) {
                try {
                    android.util.Log.d("LoadingActivity", "Pre-optimizing " + pkg);
                    VirtualCore.get().preOpt(appModel.packageName);
                    android.util.Log.d("LoadingActivity", "Pre-optimization completed for " + pkg);
                } catch (Exception e) {
                    android.util.Log.e("LoadingActivity", "Pre-optimization failed for " + pkg, e);
                }
            }
            
            try {
                android.util.Log.d("LoadingActivity", "Calling VActivityManager.startActivity for " + pkg);
                int result = VActivityManager.get().startActivity(intent, userId);
                android.util.Log.d("LoadingActivity", "VActivityManager.startActivity returned: " + result + " for " + pkg);
                
                if (result != 0) {
                    android.util.Log.e("LoadingActivity", "Failed to start activity for " + pkg + ", result code: " + result);
                    runOnUiThread(() -> {
                        android.widget.Toast.makeText(LoadingActivity.this, 
                            "启动应用失败: " + appModel.name + " (错误代码: " + result + ")", 
                            android.widget.Toast.LENGTH_LONG).show();
                        finish();
                    });
                }
            } catch (Exception e) {
                android.util.Log.e("LoadingActivity", "Exception during app launch for " + pkg, e);
                runOnUiThread(() -> {
                    android.widget.Toast.makeText(LoadingActivity.this, 
                        "启动应用时发生错误: " + e.getMessage(), 
                        android.widget.Toast.LENGTH_LONG).show();
                    finish();
                });
            }
        });

    }

    private final VirtualCore.UiCallback mUiCallback = new VirtualCore.UiCallback() {

        @Override
        public void onAppOpened(String packageName, int userId) throws RemoteException {
            android.util.Log.d("LoadingActivity", "App opened successfully: " + packageName + " (userId: " + userId + ")");
            // 取消超时任务
            if (timeoutRunnable != null && loadingView != null) {
                loadingView.removeCallbacks(timeoutRunnable);
            }
            finish();
        }
    };

    @Override
    protected void onResume() {
        super.onResume();
        loadingView.startAnim();
    }

    @Override
    protected void onPause() {
        super.onPause();
        loadingView.stopAnim();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理超时任务，防止内存泄漏
        if (timeoutRunnable != null && loadingView != null) {
            loadingView.removeCallbacks(timeoutRunnable);
        }
    }
}
