<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="incidents">
                    <map id="0">
                        <entry
                            name="incidentClass"
                            string="com.lody.virtual.client.stub.AmsTask.Response"/>
                        <location id="secondaryLocation"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/AmsTask.java"
                            line="141"
                            column="17"
                            startOffset="5259"
                            endLine="141"
                            endColumn="48"
                            endOffset="5290"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/AmsTask.java"
                            line="137"
                            column="29"
                            startOffset="5037"
                            endLine="137"
                            endColumn="61"
                            endOffset="5069"/>
                    </map>
                    <map id="1">
                        <entry
                            name="incidentClass"
                            string="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"/>
                        <location id="secondaryLocation"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ChooseTypeAndAccountActivity.java"
                            line="331"
                            column="17"
                            startOffset="14245"
                            endLine="331"
                            endColumn="68"
                            endOffset="14296"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ChooseTypeAndAccountActivity.java"
                            line="325"
                            column="35"
                            startOffset="13878"
                            endLine="326"
                            endColumn="47"
                            endOffset="13960"/>
                    </map>
                    <map id="2">
                        <entry
                            name="incidentClass"
                            string="com.lody.virtual.client.stub.ShortcutHandleActivity"/>
                        <location id="secondaryLocation"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ShortcutHandleActivity.java"
                            line="63"
                            column="13"
                            startOffset="1862"
                            endLine="63"
                            endColumn="40"
                            endOffset="1889"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ShortcutHandleActivity.java"
                            line="34"
                            column="32"
                            startOffset="925"
                            endLine="34"
                            endColumn="61"
                            endOffset="954"/>
                    </map>
                    <map id="3">
                        <entry
                            name="incidentClass"
                            string="com.lody.virtual.server.am.VActivityManagerService"/>
                        <location id="secondaryLocation"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/VActivityManagerService.java"
                            line="1075"
                            column="35"
                            startOffset="39452"
                            endLine="1075"
                            endColumn="89"
                            endOffset="39506"
                            message="The unsafe intent is launched here."/>
                        <location id="location"
                            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/VActivityManagerService.java"
                            line="1061"
                            column="35"
                            startOffset="38802"
                            endLine="1061"
                            endColumn="80"
                            endOffset="38847"/>
                    </map>
            </map>
            <map id="unprotected">
                <entry
                    name="com.lody.virtual.client.stub.ShortcutHandleActivity"
                    boolean="true"/>
                <entry
                    name="com.lody.virtual.client.stub.ChooserActivity"
                    boolean="true"/>
                <entry
                    name="com.lody.virtual.client.stub.ResolverActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="_ChecksSdkIntAtLeast">
        <entry
            name="com.lody.virtual.helper.compat.PackageParserCompat#API_LEVEL"
            string="extension=0"/>
    </map>
    <map id="NotificationPermission">
        <entry
            name="source"
            boolean="true"/>
    </map>
    <map id="UnusedResources">
        <location id="R.dimen.match_parent"
            file="${:lib*debug*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="3"
            column="12"
            startOffset="49"
            endLine="3"
            endColumn="31"
            endOffset="68"/>
        <entry
            name="model"
            string="dimen[standard_notification_panel_width(D),notification_panel_width(U),notification_max_height(U),notification_mid_height(U),notification_side_padding(U),notification_padding(U),match_parent(D),notification_min_height(U)],id[btn_6(D),icon(U),description(U),btn_5(D),btn_4(D),btn_3(D),btn_9(D),btn_8(D),btn_7(D),btn_20(D),btn_21(D),btn_22(D),btn_2(D),btn_23(D),btn_1(D),btn_24(D),btn_25(D),btn_26(D),btn_27(D),btn_28(D),btn_29(D),button_bar(D),im_main(U),btn_30(D),btn_31(D),btn_10(D),btn_32(D),btn_11(D),btn_12(D),account_row_text(U),btn_13(D),btn_14(D),text1(U),btn_15(D),text2(U),btn_16(D),btn_17(D),btn_18(D),btn_19(D),account_row_icon(U)],integer[config_maxResolverActivityColumns(U)],layout[choose_type_and_account(U),choose_account_row(U),resolve_list_item(U),choose_account_type(U),custom_notification_lite(U),custom_notification(U),app_not_authorized(D)],string[noApplications(U),owner_name(U),virtual_installer(D),choose(U),engine_process_name(U),add_account_button_label(U),choose_empty(D)],style[notification_button(U),notification_layout(U),VATheme(U),notification(R),VAAlertTheme(U)];36^40^3f,3f^42,40^42;;;"/>
        <location id="R.string.choose_empty"
            file="${:lib*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="7"
            column="13"
            startOffset="272"
            endLine="7"
            endColumn="32"
            endOffset="291"/>
        <location id="R.dimen.standard_notification_panel_width"
            file="${:lib*debug*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="4"
            column="12"
            startOffset="93"
            endLine="4"
            endColumn="52"
            endOffset="133"/>
        <location id="R.layout.app_not_authorized"
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/app_not_authorized.xml"
            line="20"
            column="1"
            startOffset="666"
            endLine="56"
            endColumn="16"
            endOffset="2178"/>
        <location id="R.string.virtual_installer"
            file="${:lib*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="114"
            endLine="4"
            endColumn="37"
            endOffset="138"/>
    </map>

</incidents>
