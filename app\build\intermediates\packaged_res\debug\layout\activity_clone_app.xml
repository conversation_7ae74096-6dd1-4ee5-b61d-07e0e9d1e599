<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/clone_app_tool_bar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/clone_app_tab_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:tabMode="scrollable" />

    </androidx.appcompat.widget.Toolbar>

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/clone_app_view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />
</LinearLayout>