1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="io.virtualapp"
4    android:versionCode="24"
5    android:versionName="1.2.5" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:4:5-66
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:5:5-76
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:5:22-74
13    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
13-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-88
13-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-85
14    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
14-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-77
14-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-74
15    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
15-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-78
15-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-75
16    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
16-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-78
16-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:22-75
17    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
17-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-80
17-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:22-77
18    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
18-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:5-81
18-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:22-78
19    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
19-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:5-95
19-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:22-92
20    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
20-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:5-117
20-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:22-114
21    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
21-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:5-106
21-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:22-103
22    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
22-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-107
22-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:22-104
23    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
23-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:5-86
23-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:22-83
24    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
24-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:5-76
24-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:22-73
25    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
25-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-82
25-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:22-79
26    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" />
26-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:5-83
26-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:22-80
27    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
27-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:5-80
27-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:22-77
28    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
28-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:5-79
28-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:22-76
29    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
29-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:5-97
29-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:22-94
30    <uses-permission android:name="android.permission.ACCOUNT_MANAGER" />
30-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:5-27:47
30-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-58
31    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS" />
31-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:28:5-30:47
31-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-62
32    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
32-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:5-74
32-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:31:22-71
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
33-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:5-81
33-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:33:22-78
34    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
34-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:5-79
34-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:34:22-76
35    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
35-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:5-89
35-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:35:22-86
36    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
36-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:36:5-38:39
36-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:37:9-63
37    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
37-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:5-79
37-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:39:22-76
38    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
38-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:5-76
38-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:40:22-73
39    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
39-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:5-77
39-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:41:22-74
40    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
40-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:5-80
40-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:42:22-77
41    <uses-permission android:name="android.permission.BIND_APPWIDGET" />
41-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:43:5-45:47
41-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:44:9-57
42    <uses-permission android:name="android.permission.BLUETOOTH" />
42-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:5-68
42-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:46:22-65
43    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
43-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:5-74
43-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:47:22-71
44    <uses-permission android:name="android.permission.BODY_SENSORS" />
44-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:5-71
44-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:48:22-68
45    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
45-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:5-75
45-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:49:22-72
46    <uses-permission android:name="android.permission.CALL_PHONE" />
46-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:5-69
46-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:50:22-66
47    <uses-permission android:name="android.permission.CAMERA" />
47-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:5-65
47-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:51:22-62
48    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
48-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:5-79
48-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:52:22-76
49    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
49-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:5-86
49-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:53:22-83
50    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
50-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:5-76
50-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:54:22-73
51    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
51-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:5-77
51-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:55:22-74
52    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
52-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:5-74
52-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:56:22-71
53    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
53-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:5-75
53-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:57:22-72
54    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
54-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:5-88
54-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:58:22-85
55    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
55-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:5-76
55-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:59:22-73
56    <uses-permission android:name="android.permission.FLASHLIGHT" />
56-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:5-69
56-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:60:22-66
57    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
57-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:5-71
57-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:61:22-68
58    <uses-permission android:name="android.permission.GET_CLIPS" />
58-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:5-68
58-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:62:22-65
59    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
59-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:5-75
59-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:63:22-72
60    <uses-permission android:name="android.permission.GET_TASKS" />
60-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:5-68
60-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:64:22-65
61    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
61-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:5-84
61-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:65:22-81
62    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
62-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:5-74
62-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:66:22-71
63    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
63-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:5-80
63-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:67:22-77
64    <uses-permission android:name="android.permission.NFC" />
64-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:5-62
64-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:68:22-59
65    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
65-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:5-78
65-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:69:22-75
66    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
66-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:5-81
66-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:70:22-78
67    <uses-permission android:name="android.permission.READ_CALENDAR" />
67-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:5-72
67-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:71:22-69
68    <uses-permission android:name="android.permission.READ_CALL_LOG" />
68-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:5-72
68-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:72:22-69
69    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
69-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:5-79
69-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:73:22-76
70    <uses-permission android:name="android.permission.READ_CLIPS" />
70-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:5-69
70-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:74:22-66
71    <uses-permission android:name="android.permission.READ_CONTACTS" />
71-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:5-72
71-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:75:22-69
72    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
72-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:5-80
72-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:76:22-77
73    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
73-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:5-80
73-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:77:22-77
74    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
74-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:5-75
74-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:78:22-72
75    <uses-permission android:name="android.permission.READ_PROFILE" />
75-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:5-71
75-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:79:22-68
76    <uses-permission android:name="android.permission.READ_SMS" />
76-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:5-67
76-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:80:22-64
77    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
77-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:5-77
77-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:81:22-74
78    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
78-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:5-77
78-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:82:22-74
79    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
79-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:5-74
79-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:83:22-71
80    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
80-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:5-79
80-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:84:22-76
81    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
81-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:5-81
81-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:85:22-78
82    <uses-permission android:name="android.permission.RECEIVE_MMS" />
82-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:5-70
82-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:86:22-67
83    <uses-permission android:name="android.permission.RECEIVE_SMS" />
83-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:5-70
83-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:87:22-67
84    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
84-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:5-75
84-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:88:22-72
85    <uses-permission android:name="android.permission.RECORD_AUDIO" />
85-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:5-71
85-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:89:22-68
86    <uses-permission android:name="android.permission.REORDER_TASKS" />
86-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:5-72
86-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:90:22-69
87    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
87-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:5-75
87-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:91:22-72
88    <uses-permission android:name="android.permission.SEND_SMS" />
88-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:5-67
88-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:92:22-64
89    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
89-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:5-72
89-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:93:22-69
90    <uses-permission android:name="android.permission.SET_WALLPAPER" />
90-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:5-72
90-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:94:22-69
91    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
91-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:5-78
91-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:95:22-75
92    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
92-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:5-80
92-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:96:22-77
93    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
93-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:5-81
93-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:97:22-78
94    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
94-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:5-78
94-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:98:22-75
95    <uses-permission android:name="android.permission.TRANSMIT_IR" />
95-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:5-70
95-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:99:22-67
96    <uses-permission android:name="android.permission.USE_SIP" />
96-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:5-66
96-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:100:22-63
97    <uses-permission android:name="android.permission.VIBRATE" />
97-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:5-66
97-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:101:22-63
98    <uses-permission android:name="android.permission.WAKE_LOCK" />
98-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:5-68
98-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:102:22-65
99    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
99-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:5-73
99-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:103:22-70
100    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:5-73
100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:104:22-70
101    <uses-permission android:name="android.permission.WRITE_CLIPS" />
101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:5-70
101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:105:22-67
102    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:5-73
102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:106:22-70
103    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:5-81
103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:107:22-78
104    <uses-permission android:name="android.permission.WRITE_PROFILE" />
104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:5-72
104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:108:22-69
105    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:5-73
105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:109:22-70
106    <uses-permission android:name="android.permission.WRITE_SMS" />
106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:5-68
106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:110:22-65
107    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:5-78
107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:111:22-75
108    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:5-78
108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:112:22-75
109    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:5-80
109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:113:22-77
110    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:5-74
110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:114:22-71
111    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:5-78
111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:115:22-75
112    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:5-93
112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:116:22-90
113    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:5-94
113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:117:22-91
114    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:5-88
114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:118:22-85
115    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:5-90
115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:119:22-87
116    <uses-permission android:name="com.android.vending.BILLING" />
116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:5-67
116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:120:22-64
117    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:5-73
117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:121:22-70
118    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:5-86
118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:122:22-83
119    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:5-82
119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:123:22-79
120    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:5-94
120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:124:22-91
121    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:5-92
121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:125:22-89
122    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:5-92
122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:126:22-89
123    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:5-107
123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:127:22-104
124    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:5-104
124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:128:22-101
125    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adsense" />
125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:5-100
125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:129:22-97
126    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adwords" />
126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:5-100
126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:130:22-97
127    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ah" />
127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:5-95
127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:131:22-92
128    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.android" />
128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:5-100
128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:132:22-97
129    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.androidsecure" />
129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:5-106
129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:133:22-103
130    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.blogger" />
130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:5-100
130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:134:22-97
131    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cl" />
131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:5-95
131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:135:22-92
132    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cp" />
132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:5-95
132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:136:22-92
133    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.dodgeball" />
133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:5-102
133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:137:22-99
134    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.finance" />
134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:5-100
134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:138:22-97
135    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.gbase" />
135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:5-98
135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:139:22-95
136    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.grandcentral" />
136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:5-105
136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:140:22-102
137    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.groups2" />
137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:141:5-100
137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:141:22-97
138    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.health" />
138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:142:5-99
138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:142:22-96
139    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ig" />
139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:5-95
139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:143:22-92
140    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.jotspot" />
140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:5-100
140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:144:22-97
141    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.knol" />
141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:5-97
141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:145:22-94
142    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.lh2" />
142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:5-96
142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:146:22-93
143    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.local" />
143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:5-98
143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:147:22-95
144    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mail" />
144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:5-97
144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:148:22-94
145    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mobile" />
145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:5-99
145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:149:22-96
146    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.news" />
146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:5-97
146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:150:22-94
147    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.notebook" />
147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:5-101
147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:151:22-98
148    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.orkut" />
148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:5-98
148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:152:22-95
149    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.print" />
149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:5-98
149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:153:22-95
150    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierra" />
150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:5-99
150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:154:22-96
151    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierraqa" />
151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:5-101
151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:155:22-98
152    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierrasandbox" />
152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:5-106
152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:156:22-103
153    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sitemaps" />
153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:5-101
153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:157:22-98
154    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speech" />
154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:5-99
154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:158:22-96
155    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speechpersonalization" />
155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:5-114
155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:159:22-111
156    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.talk" />
156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:5-97
156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:160:22-94
157    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wifi" />
157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:5-97
157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:161:22-94
158    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wise" />
158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:5-97
158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:162:22-94
159    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.writely" />
159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:5-100
159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:163:22-97
160    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.youtube" />
160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:5-100
160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:164:22-97
161    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:5-92
161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:165:22-89
162    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:5-98
162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:166:22-95
163    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:5-94
163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:167:22-91
164    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:5-95
164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:168:22-92
165    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:5-84
165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:169:22-81
166    <uses-permission android:name="android.permission.READ_LOGS" />
166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:5-68
166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:170:22-65
167    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />
167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:171:5-173:47
167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:172:9-59
168    <uses-permission android:name="android.permission.DELETE_PACKAGES" />
168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:174:5-176:47
168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:175:9-58
169    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:177:5-179:47
169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:178:9-62
170    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:180:5-182:47
170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:181:9-62
171    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:183:5-185:47
171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:184:9-66
172    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:5-74
172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:186:22-71
173    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:5-75
173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:187:22-72
174    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:5-79
174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:188:22-76
175    <uses-permission android:name="android.permission.DEVICE_POWER" />
175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:189:5-191:47
175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:190:9-55
176    <uses-permission android:name="android.permission.BATTERY_STATS" />
176-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:5-72
176-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:192:22-69
177    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
177-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:5-82
177-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:193:22-79
178    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
178-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:5-85
178-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:194:22-82
179    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
179-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:5-86
179-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:195:22-83
180    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
180-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:5-86
180-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:196:22-83
181    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
181-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:5-86
181-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:197:22-83
182    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
182-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:198:5-89
182-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:198:22-86
183    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
183-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:199:5-93
183-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:199:22-90
184    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
184-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:200:5-80
184-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:200:22-77
185    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
185-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:201:5-85
185-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:201:22-82
186    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
186-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:202:5-86
186-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:202:22-83
187    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
187-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:203:5-78
187-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:203:22-75
188    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
188-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:204:5-81
188-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:204:22-78
189    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
189-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:205:5-84
189-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:205:22-81
190    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
190-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:206:5-82
190-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:206:22-79
191    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
191-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:207:5-82
191-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:207:22-79
192    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
192-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:208:5-79
192-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:208:22-76
193    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
193-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:209:5-82
193-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:209:22-79
194    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
194-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:210:5-92
194-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:210:22-89
195    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
195-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:211:5-91
195-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:211:22-88
196    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" /> <!-- Required for Android 11+ to query other apps -->
196-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:212:5-83
196-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:212:22-80
197    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
197-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:215:5-217:53
197-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:216:9-61
198    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS" />
198-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:218:5-220:47
198-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:219:9-61
199
200    <uses-feature android:name="android.hardware.camera" />
200-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:222:5-60
200-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:222:19-57
201    <uses-feature
201-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:223:5-225:36
202        android:name="android.hardware.camera.autofocus"
202-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:224:9-57
203        android:required="false" />
203-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:225:9-33
204
205    <permission
205-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
206        android:name="io.virtualapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
206-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
207        android:protectionLevel="signature" />
207-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
208
209    <uses-permission android:name="io.virtualapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
209-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
209-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
210
211    <application
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:6:5-55:19
212        android:name="io.virtualapp.VApp"
212-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:7:9-29
213        android:allowBackup="true"
213-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:8:9-35
214        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
214-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b3d22ab099177b3472464321ad2c41c\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
215        android:debuggable="true"
216        android:extractNativeLibs="true"
217        android:icon="@mipmap/ic_launcher"
217-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:9:9-43
218        android:label="@string/app_name"
218-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:10:9-41
219        android:theme="@style/AppTheme" >
219-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:11:9-40
220        <meta-data
220-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:12:9-14:66
221            android:name="TencentMapSDK"
221-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:13:13-41
222            android:value="4HPBZ-2QWC6-H47SR-M6PZY-MTZB5-N2F4F" />
222-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:14:13-64
223
224        <activity
224-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:16:9-25:20
225            android:name="io.virtualapp.splash.SplashActivity"
225-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:17:13-50
226            android:exported="true"
226-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:20:13-36
227            android:screenOrientation="portrait"
227-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:18:13-49
228            android:theme="@style/AppTheme" >
228-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:19:13-44
229            <intent-filter>
229-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:21:13-24:29
230                <action android:name="android.intent.action.MAIN" />
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:22:17-68
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:22:25-66
231
232                <category android:name="android.intent.category.LAUNCHER" />
232-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:23:17-76
232-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:23:27-74
233            </intent-filter>
234        </activity>
235        <activity
235-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:27:9-30:45
236            android:name="io.virtualapp.home.HomeActivity"
236-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:28:13-46
237            android:screenOrientation="portrait"
237-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:29:13-49
238            android:theme="@style/UITheme" />
238-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:30:13-43
239        <activity
239-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:32:9-35:45
240            android:name="io.virtualapp.home.ListAppActivity"
240-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:33:13-49
241            android:screenOrientation="portrait"
241-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:34:13-49
242            android:theme="@style/UITheme" />
242-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:35:13-43
243        <activity
243-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:37:9-43:54
244            android:name="io.virtualapp.home.LoadingActivity"
244-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:38:13-49
245            android:excludeFromRecents="true"
245-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:39:13-46
246            android:noHistory="true"
246-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:40:13-37
247            android:screenOrientation="portrait"
247-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:41:13-49
248            android:taskAffinity="va.task.loading"
248-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:42:13-51
249            android:theme="@style/TransparentTheme" />
249-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:43:13-52
250        <activity
250-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:44:9-48:15
251            android:name="io.virtualapp.home.location.VirtualLocationSettings"
251-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:45:13-66
252            android:screenOrientation="portrait"
252-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:46:13-49
253            android:theme="@style/UITheme" />
253-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:47:13-43
254        <activity
254-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:50:9-54:15
255            android:name="io.virtualapp.home.location.MarkerActivity"
255-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:51:13-57
256            android:screenOrientation="portrait"
256-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:52:13-49
257            android:theme="@style/UITheme" />
257-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\app\src\main\AndroidManifest.xml:53:13-43
258
259        <service
259-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:228:9-230:61
260            android:name="com.lody.virtual.client.stub.DaemonService"
260-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:229:13-70
261            android:process="@string/engine_process_name" />
261-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:230:13-58
262        <service
262-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:231:9-233:61
263            android:name="com.lody.virtual.client.stub.DaemonService$InnerService"
263-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:232:13-83
264            android:process="@string/engine_process_name" />
264-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:233:13-58
265
266        <activity
266-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:235:9-239:75
267            android:name="com.lody.virtual.client.stub.ShortcutHandleActivity"
267-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:236:13-79
268            android:exported="true"
268-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:237:13-36
269            android:process="@string/engine_process_name"
269-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:238:13-58
270            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
270-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:239:13-72
271        <activity
271-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:240:9-242:61
272            android:name="com.lody.virtual.client.stub.StubPendingActivity"
272-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:241:13-76
273            android:process="@string/engine_process_name" />
273-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:242:13-58
274
275        <service
275-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:244:9-246:61
276            android:name="com.lody.virtual.client.stub.StubPendingService"
276-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:245:13-75
277            android:process="@string/engine_process_name" />
277-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:246:13-58
278
279        <receiver
279-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:248:9-250:61
280            android:name="com.lody.virtual.client.stub.StubPendingReceiver"
280-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:249:13-76
281            android:process="@string/engine_process_name" />
281-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:250:13-58
282
283        <service
283-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:252:9-255:61
284            android:name="com.lody.virtual.client.stub.StubJob"
284-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:253:13-64
285            android:permission="android.permission.BIND_JOB_SERVICE"
285-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:254:13-69
286            android:process="@string/engine_process_name" />
286-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:255:13-58
287
288        <activity
288-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:257:9-263:52
289            android:name="com.lody.virtual.client.stub.ChooseAccountTypeActivity"
289-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:258:13-82
290            android:configChanges="keyboard|keyboardHidden|orientation"
290-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:259:13-72
291            android:excludeFromRecents="true"
291-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:260:13-46
292            android:exported="false"
292-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:261:13-37
293            android:process="@string/engine_process_name"
293-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:262:13-58
294            android:screenOrientation="portrait" />
294-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:263:13-49
295        <activity
295-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:264:9-270:52
296            android:name="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"
296-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:265:13-85
297            android:configChanges="keyboard|keyboardHidden|orientation"
297-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:266:13-72
298            android:excludeFromRecents="true"
298-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:267:13-46
299            android:exported="false"
299-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:268:13-37
300            android:process="@string/engine_process_name"
300-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:269:13-58
301            android:screenOrientation="portrait" />
301-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:270:13-49
302        <activity
302-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:271:9-279:51
303            android:name="com.lody.virtual.client.stub.ChooserActivity"
303-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:272:13-72
304            android:configChanges="keyboard|keyboardHidden|orientation"
304-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:273:13-72
305            android:excludeFromRecents="true"
305-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:274:13-46
306            android:exported="true"
306-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:275:13-36
307            android:finishOnCloseSystemDialogs="true"
307-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:276:13-54
308            android:process="@string/engine_process_name"
308-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:277:13-58
309            android:screenOrientation="portrait"
309-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:278:13-49
310            android:theme="@style/VAAlertTheme" />
310-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:279:13-48
311        <activity
311-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:280:9-288:51
312            android:name="com.lody.virtual.client.stub.ResolverActivity"
312-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:281:13-73
313            android:configChanges="keyboard|keyboardHidden|orientation"
313-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:282:13-72
314            android:excludeFromRecents="true"
314-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:283:13-46
315            android:exported="true"
315-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:284:13-36
316            android:finishOnCloseSystemDialogs="true"
316-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:285:13-54
317            android:process="@string/engine_process_name"
317-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:286:13-58
318            android:screenOrientation="portrait"
318-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:287:13-49
319            android:theme="@style/VAAlertTheme" />
319-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:288:13-48
320
321        <provider
321-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:290:9-294:61
322            android:name="com.lody.virtual.server.BinderProvider"
322-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:291:13-66
323            android:authorities="io.virtualapp.virtual.service.BinderProvider"
323-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:292:13-82
324            android:exported="false"
324-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:293:13-37
325            android:process="@string/engine_process_name" />
325-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:294:13-58
326
327        <activity
327-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:296:9-301:46
328            android:name="com.lody.virtual.client.stub.StubActivity$C0"
328-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:297:13-72
329            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
329-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:298:13-170
330            android:process=":p0"
330-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:299:13-34
331            android:taskAffinity="com.lody.virtual.vt"
331-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:300:13-55
332            android:theme="@style/VATheme" />
332-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:301:13-43
333        <activity
333-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:302:9-307:46
334            android:name="com.lody.virtual.client.stub.StubActivity$C1"
334-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:303:13-72
335            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
335-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:304:13-170
336            android:process=":p1"
336-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:305:13-34
337            android:taskAffinity="com.lody.virtual.vt"
337-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:306:13-55
338            android:theme="@style/VATheme" />
338-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:307:13-43
339        <activity
339-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:308:9-313:46
340            android:name="com.lody.virtual.client.stub.StubActivity$C2"
340-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:309:13-72
341            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
341-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:310:13-170
342            android:process=":p2"
342-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:311:13-34
343            android:taskAffinity="com.lody.virtual.vt"
343-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:312:13-55
344            android:theme="@style/VATheme" />
344-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:313:13-43
345        <activity
345-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:314:9-319:46
346            android:name="com.lody.virtual.client.stub.StubActivity$C3"
346-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:315:13-72
347            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
347-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:316:13-170
348            android:process=":p3"
348-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:317:13-34
349            android:taskAffinity="com.lody.virtual.vt"
349-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:318:13-55
350            android:theme="@style/VATheme" />
350-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:319:13-43
351        <activity
351-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:320:9-325:46
352            android:name="com.lody.virtual.client.stub.StubActivity$C4"
352-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:321:13-72
353            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
353-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:322:13-170
354            android:process=":p4"
354-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:323:13-34
355            android:taskAffinity="com.lody.virtual.vt"
355-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:324:13-55
356            android:theme="@style/VATheme" />
356-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:325:13-43
357        <activity
357-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:326:9-331:46
358            android:name="com.lody.virtual.client.stub.StubActivity$C5"
358-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:327:13-72
359            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
359-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:328:13-170
360            android:process=":p5"
360-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:329:13-34
361            android:taskAffinity="com.lody.virtual.vt"
361-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:330:13-55
362            android:theme="@style/VATheme" />
362-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:331:13-43
363        <activity
363-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:332:9-337:46
364            android:name="com.lody.virtual.client.stub.StubActivity$C6"
364-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:333:13-72
365            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
365-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:334:13-170
366            android:process=":p6"
366-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:335:13-34
367            android:taskAffinity="com.lody.virtual.vt"
367-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:336:13-55
368            android:theme="@style/VATheme" />
368-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:337:13-43
369        <activity
369-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:338:9-343:46
370            android:name="com.lody.virtual.client.stub.StubActivity$C7"
370-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:339:13-72
371            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
371-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:340:13-170
372            android:process=":p7"
372-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:341:13-34
373            android:taskAffinity="com.lody.virtual.vt"
373-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:342:13-55
374            android:theme="@style/VATheme" />
374-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:343:13-43
375        <activity
375-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:344:9-349:46
376            android:name="com.lody.virtual.client.stub.StubActivity$C8"
376-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:345:13-72
377            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
377-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:346:13-170
378            android:process=":p8"
378-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:347:13-34
379            android:taskAffinity="com.lody.virtual.vt"
379-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:348:13-55
380            android:theme="@style/VATheme" />
380-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:349:13-43
381        <activity
381-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:350:9-355:46
382            android:name="com.lody.virtual.client.stub.StubActivity$C9"
382-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:351:13-72
383            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
383-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:352:13-170
384            android:process=":p9"
384-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:353:13-34
385            android:taskAffinity="com.lody.virtual.vt"
385-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:354:13-55
386            android:theme="@style/VATheme" />
386-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:355:13-43
387        <activity
387-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:356:9-361:46
388            android:name="com.lody.virtual.client.stub.StubActivity$C10"
388-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:357:13-73
389            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
389-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:358:13-170
390            android:process=":p10"
390-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:359:13-35
391            android:taskAffinity="com.lody.virtual.vt"
391-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:360:13-55
392            android:theme="@style/VATheme" />
392-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:361:13-43
393        <activity
393-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:362:9-367:46
394            android:name="com.lody.virtual.client.stub.StubActivity$C11"
394-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:363:13-73
395            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
395-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:364:13-170
396            android:process=":p11"
396-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:365:13-35
397            android:taskAffinity="com.lody.virtual.vt"
397-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:366:13-55
398            android:theme="@style/VATheme" />
398-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:367:13-43
399        <activity
399-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:368:9-373:46
400            android:name="com.lody.virtual.client.stub.StubActivity$C12"
400-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:369:13-73
401            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
401-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:370:13-170
402            android:process=":p12"
402-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:371:13-35
403            android:taskAffinity="com.lody.virtual.vt"
403-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:372:13-55
404            android:theme="@style/VATheme" />
404-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:373:13-43
405        <activity
405-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:374:9-379:46
406            android:name="com.lody.virtual.client.stub.StubActivity$C13"
406-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:375:13-73
407            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
407-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:376:13-170
408            android:process=":p13"
408-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:377:13-35
409            android:taskAffinity="com.lody.virtual.vt"
409-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:378:13-55
410            android:theme="@style/VATheme" />
410-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:379:13-43
411        <activity
411-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:380:9-385:46
412            android:name="com.lody.virtual.client.stub.StubActivity$C14"
412-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:381:13-73
413            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
413-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:382:13-170
414            android:process=":p14"
414-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:383:13-35
415            android:taskAffinity="com.lody.virtual.vt"
415-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:384:13-55
416            android:theme="@style/VATheme" />
416-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:385:13-43
417        <activity
417-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:386:9-391:46
418            android:name="com.lody.virtual.client.stub.StubActivity$C15"
418-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:387:13-73
419            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
419-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:388:13-170
420            android:process=":p15"
420-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:389:13-35
421            android:taskAffinity="com.lody.virtual.vt"
421-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:390:13-55
422            android:theme="@style/VATheme" />
422-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:391:13-43
423        <activity
423-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:392:9-397:46
424            android:name="com.lody.virtual.client.stub.StubActivity$C16"
424-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:393:13-73
425            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
425-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:394:13-170
426            android:process=":p16"
426-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:395:13-35
427            android:taskAffinity="com.lody.virtual.vt"
427-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:396:13-55
428            android:theme="@style/VATheme" />
428-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:397:13-43
429        <activity
429-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:398:9-403:46
430            android:name="com.lody.virtual.client.stub.StubActivity$C17"
430-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:399:13-73
431            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
431-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:400:13-170
432            android:process=":p17"
432-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:401:13-35
433            android:taskAffinity="com.lody.virtual.vt"
433-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:402:13-55
434            android:theme="@style/VATheme" />
434-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:403:13-43
435        <activity
435-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:404:9-409:46
436            android:name="com.lody.virtual.client.stub.StubActivity$C18"
436-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:405:13-73
437            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
437-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:406:13-170
438            android:process=":p18"
438-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:407:13-35
439            android:taskAffinity="com.lody.virtual.vt"
439-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:408:13-55
440            android:theme="@style/VATheme" />
440-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:409:13-43
441        <activity
441-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:410:9-415:46
442            android:name="com.lody.virtual.client.stub.StubActivity$C19"
442-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:411:13-73
443            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
443-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:412:13-170
444            android:process=":p19"
444-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:413:13-35
445            android:taskAffinity="com.lody.virtual.vt"
445-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:414:13-55
446            android:theme="@style/VATheme" />
446-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:415:13-43
447        <activity
447-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:416:9-421:46
448            android:name="com.lody.virtual.client.stub.StubActivity$C20"
448-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:417:13-73
449            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
449-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:418:13-170
450            android:process=":p20"
450-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:419:13-35
451            android:taskAffinity="com.lody.virtual.vt"
451-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:420:13-55
452            android:theme="@style/VATheme" />
452-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:421:13-43
453        <activity
453-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:422:9-427:46
454            android:name="com.lody.virtual.client.stub.StubActivity$C21"
454-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:423:13-73
455            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
455-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:424:13-170
456            android:process=":p21"
456-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:425:13-35
457            android:taskAffinity="com.lody.virtual.vt"
457-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:426:13-55
458            android:theme="@style/VATheme" />
458-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:427:13-43
459        <activity
459-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:428:9-433:46
460            android:name="com.lody.virtual.client.stub.StubActivity$C22"
460-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:429:13-73
461            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
461-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:430:13-170
462            android:process=":p22"
462-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:431:13-35
463            android:taskAffinity="com.lody.virtual.vt"
463-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:432:13-55
464            android:theme="@style/VATheme" />
464-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:433:13-43
465        <activity
465-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:434:9-439:46
466            android:name="com.lody.virtual.client.stub.StubActivity$C23"
466-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:435:13-73
467            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
467-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:436:13-170
468            android:process=":p23"
468-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:437:13-35
469            android:taskAffinity="com.lody.virtual.vt"
469-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:438:13-55
470            android:theme="@style/VATheme" />
470-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:439:13-43
471        <activity
471-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:440:9-445:46
472            android:name="com.lody.virtual.client.stub.StubActivity$C24"
472-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:441:13-73
473            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
473-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:442:13-170
474            android:process=":p24"
474-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:443:13-35
475            android:taskAffinity="com.lody.virtual.vt"
475-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:444:13-55
476            android:theme="@style/VATheme" />
476-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:445:13-43
477        <activity
477-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:446:9-451:46
478            android:name="com.lody.virtual.client.stub.StubActivity$C25"
478-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:447:13-73
479            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
479-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:448:13-170
480            android:process=":p25"
480-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:449:13-35
481            android:taskAffinity="com.lody.virtual.vt"
481-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:450:13-55
482            android:theme="@style/VATheme" />
482-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:451:13-43
483        <activity
483-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:452:9-457:46
484            android:name="com.lody.virtual.client.stub.StubActivity$C26"
484-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:453:13-73
485            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
485-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:454:13-170
486            android:process=":p26"
486-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:455:13-35
487            android:taskAffinity="com.lody.virtual.vt"
487-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:456:13-55
488            android:theme="@style/VATheme" />
488-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:457:13-43
489        <activity
489-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:458:9-463:46
490            android:name="com.lody.virtual.client.stub.StubActivity$C27"
490-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:459:13-73
491            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
491-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:460:13-170
492            android:process=":p27"
492-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:461:13-35
493            android:taskAffinity="com.lody.virtual.vt"
493-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:462:13-55
494            android:theme="@style/VATheme" />
494-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:463:13-43
495        <activity
495-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:464:9-469:46
496            android:name="com.lody.virtual.client.stub.StubActivity$C28"
496-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:465:13-73
497            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
497-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:466:13-170
498            android:process=":p28"
498-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:467:13-35
499            android:taskAffinity="com.lody.virtual.vt"
499-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:468:13-55
500            android:theme="@style/VATheme" />
500-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:469:13-43
501        <activity
501-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:470:9-475:46
502            android:name="com.lody.virtual.client.stub.StubActivity$C29"
502-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:471:13-73
503            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
503-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:472:13-170
504            android:process=":p29"
504-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:473:13-35
505            android:taskAffinity="com.lody.virtual.vt"
505-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:474:13-55
506            android:theme="@style/VATheme" />
506-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:475:13-43
507        <activity
507-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:476:9-481:46
508            android:name="com.lody.virtual.client.stub.StubActivity$C30"
508-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:477:13-73
509            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
509-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:478:13-170
510            android:process=":p30"
510-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:479:13-35
511            android:taskAffinity="com.lody.virtual.vt"
511-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:480:13-55
512            android:theme="@style/VATheme" />
512-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:481:13-43
513        <activity
513-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:482:9-487:46
514            android:name="com.lody.virtual.client.stub.StubActivity$C31"
514-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:483:13-73
515            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
515-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:484:13-170
516            android:process=":p31"
516-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:485:13-35
517            android:taskAffinity="com.lody.virtual.vt"
517-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:486:13-55
518            android:theme="@style/VATheme" />
518-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:487:13-43
519        <activity
519-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:488:9-493:46
520            android:name="com.lody.virtual.client.stub.StubActivity$C32"
520-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:489:13-73
521            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
521-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:490:13-170
522            android:process=":p32"
522-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:491:13-35
523            android:taskAffinity="com.lody.virtual.vt"
523-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:492:13-55
524            android:theme="@style/VATheme" />
524-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:493:13-43
525        <activity
525-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:494:9-499:46
526            android:name="com.lody.virtual.client.stub.StubActivity$C33"
526-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:495:13-73
527            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
527-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:496:13-170
528            android:process=":p33"
528-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:497:13-35
529            android:taskAffinity="com.lody.virtual.vt"
529-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:498:13-55
530            android:theme="@style/VATheme" />
530-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:499:13-43
531        <activity
531-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:500:9-505:46
532            android:name="com.lody.virtual.client.stub.StubActivity$C34"
532-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:501:13-73
533            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
533-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:502:13-170
534            android:process=":p34"
534-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:503:13-35
535            android:taskAffinity="com.lody.virtual.vt"
535-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:504:13-55
536            android:theme="@style/VATheme" />
536-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:505:13-43
537        <activity
537-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:506:9-511:46
538            android:name="com.lody.virtual.client.stub.StubActivity$C35"
538-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:507:13-73
539            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
539-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:508:13-170
540            android:process=":p35"
540-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:509:13-35
541            android:taskAffinity="com.lody.virtual.vt"
541-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:510:13-55
542            android:theme="@style/VATheme" />
542-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:511:13-43
543        <activity
543-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:512:9-517:46
544            android:name="com.lody.virtual.client.stub.StubActivity$C36"
544-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:513:13-73
545            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
545-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:514:13-170
546            android:process=":p36"
546-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:515:13-35
547            android:taskAffinity="com.lody.virtual.vt"
547-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:516:13-55
548            android:theme="@style/VATheme" />
548-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:517:13-43
549        <activity
549-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:518:9-523:46
550            android:name="com.lody.virtual.client.stub.StubActivity$C37"
550-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:519:13-73
551            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
551-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:520:13-170
552            android:process=":p37"
552-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:521:13-35
553            android:taskAffinity="com.lody.virtual.vt"
553-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:522:13-55
554            android:theme="@style/VATheme" />
554-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:523:13-43
555        <activity
555-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:524:9-529:46
556            android:name="com.lody.virtual.client.stub.StubActivity$C38"
556-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:525:13-73
557            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
557-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:526:13-170
558            android:process=":p38"
558-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:527:13-35
559            android:taskAffinity="com.lody.virtual.vt"
559-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:528:13-55
560            android:theme="@style/VATheme" />
560-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:529:13-43
561        <activity
561-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:530:9-535:46
562            android:name="com.lody.virtual.client.stub.StubActivity$C39"
562-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:531:13-73
563            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
563-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:532:13-170
564            android:process=":p39"
564-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:533:13-35
565            android:taskAffinity="com.lody.virtual.vt"
565-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:534:13-55
566            android:theme="@style/VATheme" />
566-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:535:13-43
567        <activity
567-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:536:9-541:46
568            android:name="com.lody.virtual.client.stub.StubActivity$C40"
568-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:537:13-73
569            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
569-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:538:13-170
570            android:process=":p40"
570-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:539:13-35
571            android:taskAffinity="com.lody.virtual.vt"
571-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:540:13-55
572            android:theme="@style/VATheme" />
572-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:541:13-43
573        <activity
573-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:542:9-547:46
574            android:name="com.lody.virtual.client.stub.StubActivity$C41"
574-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:543:13-73
575            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
575-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:544:13-170
576            android:process=":p41"
576-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:545:13-35
577            android:taskAffinity="com.lody.virtual.vt"
577-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:546:13-55
578            android:theme="@style/VATheme" />
578-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:547:13-43
579        <activity
579-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:548:9-553:46
580            android:name="com.lody.virtual.client.stub.StubActivity$C42"
580-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:549:13-73
581            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
581-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:550:13-170
582            android:process=":p42"
582-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:551:13-35
583            android:taskAffinity="com.lody.virtual.vt"
583-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:552:13-55
584            android:theme="@style/VATheme" />
584-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:553:13-43
585        <activity
585-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:554:9-559:46
586            android:name="com.lody.virtual.client.stub.StubActivity$C43"
586-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:555:13-73
587            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
587-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:556:13-170
588            android:process=":p43"
588-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:557:13-35
589            android:taskAffinity="com.lody.virtual.vt"
589-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:558:13-55
590            android:theme="@style/VATheme" />
590-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:559:13-43
591        <activity
591-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:560:9-565:46
592            android:name="com.lody.virtual.client.stub.StubActivity$C44"
592-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:561:13-73
593            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
593-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:562:13-170
594            android:process=":p44"
594-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:563:13-35
595            android:taskAffinity="com.lody.virtual.vt"
595-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:564:13-55
596            android:theme="@style/VATheme" />
596-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:565:13-43
597        <activity
597-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:566:9-571:46
598            android:name="com.lody.virtual.client.stub.StubActivity$C45"
598-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:567:13-73
599            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
599-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:568:13-170
600            android:process=":p45"
600-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:569:13-35
601            android:taskAffinity="com.lody.virtual.vt"
601-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:570:13-55
602            android:theme="@style/VATheme" />
602-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:571:13-43
603        <activity
603-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:572:9-577:46
604            android:name="com.lody.virtual.client.stub.StubActivity$C46"
604-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:573:13-73
605            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
605-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:574:13-170
606            android:process=":p46"
606-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:575:13-35
607            android:taskAffinity="com.lody.virtual.vt"
607-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:576:13-55
608            android:theme="@style/VATheme" />
608-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:577:13-43
609        <activity
609-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:578:9-583:46
610            android:name="com.lody.virtual.client.stub.StubActivity$C47"
610-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:579:13-73
611            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
611-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:580:13-170
612            android:process=":p47"
612-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:581:13-35
613            android:taskAffinity="com.lody.virtual.vt"
613-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:582:13-55
614            android:theme="@style/VATheme" />
614-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:583:13-43
615        <activity
615-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:584:9-589:46
616            android:name="com.lody.virtual.client.stub.StubActivity$C48"
616-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:585:13-73
617            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
617-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:586:13-170
618            android:process=":p48"
618-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:587:13-35
619            android:taskAffinity="com.lody.virtual.vt"
619-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:588:13-55
620            android:theme="@style/VATheme" />
620-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:589:13-43
621        <activity
621-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:590:9-595:46
622            android:name="com.lody.virtual.client.stub.StubActivity$C49"
622-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:591:13-73
623            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
623-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:592:13-170
624            android:process=":p49"
624-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:593:13-35
625            android:taskAffinity="com.lody.virtual.vt"
625-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:594:13-55
626            android:theme="@style/VATheme" />
626-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:595:13-43
627        <activity
627-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:596:9-601:59
628            android:name="com.lody.virtual.client.stub.StubDialog$C0"
628-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:597:13-70
629            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
629-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:598:13-170
630            android:process=":p0"
630-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:599:13-34
631            android:taskAffinity="com.lody.virtual.vt"
631-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:600:13-55
632            android:theme="@android:style/Theme.Dialog" />
632-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:601:13-56
633        <activity
633-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:602:9-607:59
634            android:name="com.lody.virtual.client.stub.StubDialog$C1"
634-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:603:13-70
635            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
635-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:604:13-170
636            android:process=":p1"
636-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:605:13-34
637            android:taskAffinity="com.lody.virtual.vt"
637-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:606:13-55
638            android:theme="@android:style/Theme.Dialog" />
638-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:607:13-56
639        <activity
639-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:608:9-613:59
640            android:name="com.lody.virtual.client.stub.StubDialog$C2"
640-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:609:13-70
641            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
641-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:610:13-170
642            android:process=":p2"
642-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:611:13-34
643            android:taskAffinity="com.lody.virtual.vt"
643-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:612:13-55
644            android:theme="@android:style/Theme.Dialog" />
644-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:613:13-56
645        <activity
645-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:614:9-619:59
646            android:name="com.lody.virtual.client.stub.StubDialog$C3"
646-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:615:13-70
647            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
647-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:616:13-170
648            android:process=":p3"
648-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:617:13-34
649            android:taskAffinity="com.lody.virtual.vt"
649-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:618:13-55
650            android:theme="@android:style/Theme.Dialog" />
650-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:619:13-56
651        <activity
651-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:620:9-625:59
652            android:name="com.lody.virtual.client.stub.StubDialog$C4"
652-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:621:13-70
653            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
653-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:622:13-170
654            android:process=":p4"
654-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:623:13-34
655            android:taskAffinity="com.lody.virtual.vt"
655-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:624:13-55
656            android:theme="@android:style/Theme.Dialog" />
656-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:625:13-56
657        <activity
657-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:626:9-631:59
658            android:name="com.lody.virtual.client.stub.StubDialog$C5"
658-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:627:13-70
659            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
659-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:628:13-170
660            android:process=":p5"
660-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:629:13-34
661            android:taskAffinity="com.lody.virtual.vt"
661-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:630:13-55
662            android:theme="@android:style/Theme.Dialog" />
662-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:631:13-56
663        <activity
663-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:632:9-637:59
664            android:name="com.lody.virtual.client.stub.StubDialog$C6"
664-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:633:13-70
665            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
665-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:634:13-170
666            android:process=":p6"
666-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:635:13-34
667            android:taskAffinity="com.lody.virtual.vt"
667-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:636:13-55
668            android:theme="@android:style/Theme.Dialog" />
668-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:637:13-56
669        <activity
669-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:638:9-643:59
670            android:name="com.lody.virtual.client.stub.StubDialog$C7"
670-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:639:13-70
671            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
671-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:640:13-170
672            android:process=":p7"
672-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:641:13-34
673            android:taskAffinity="com.lody.virtual.vt"
673-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:642:13-55
674            android:theme="@android:style/Theme.Dialog" />
674-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:643:13-56
675        <activity
675-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:644:9-649:59
676            android:name="com.lody.virtual.client.stub.StubDialog$C8"
676-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:645:13-70
677            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
677-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:646:13-170
678            android:process=":p8"
678-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:647:13-34
679            android:taskAffinity="com.lody.virtual.vt"
679-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:648:13-55
680            android:theme="@android:style/Theme.Dialog" />
680-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:649:13-56
681        <activity
681-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:650:9-655:59
682            android:name="com.lody.virtual.client.stub.StubDialog$C9"
682-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:651:13-70
683            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
683-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:652:13-170
684            android:process=":p9"
684-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:653:13-34
685            android:taskAffinity="com.lody.virtual.vt"
685-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:654:13-55
686            android:theme="@android:style/Theme.Dialog" />
686-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:655:13-56
687        <activity
687-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:656:9-661:59
688            android:name="com.lody.virtual.client.stub.StubDialog$C10"
688-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:657:13-71
689            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
689-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:658:13-170
690            android:process=":p10"
690-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:659:13-35
691            android:taskAffinity="com.lody.virtual.vt"
691-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:660:13-55
692            android:theme="@android:style/Theme.Dialog" />
692-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:661:13-56
693        <activity
693-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:662:9-667:59
694            android:name="com.lody.virtual.client.stub.StubDialog$C11"
694-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:663:13-71
695            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
695-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:664:13-170
696            android:process=":p11"
696-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:665:13-35
697            android:taskAffinity="com.lody.virtual.vt"
697-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:666:13-55
698            android:theme="@android:style/Theme.Dialog" />
698-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:667:13-56
699        <activity
699-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:668:9-673:59
700            android:name="com.lody.virtual.client.stub.StubDialog$C12"
700-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:669:13-71
701            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
701-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:670:13-170
702            android:process=":p12"
702-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:671:13-35
703            android:taskAffinity="com.lody.virtual.vt"
703-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:672:13-55
704            android:theme="@android:style/Theme.Dialog" />
704-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:673:13-56
705        <activity
705-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:674:9-679:59
706            android:name="com.lody.virtual.client.stub.StubDialog$C13"
706-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:675:13-71
707            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
707-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:676:13-170
708            android:process=":p13"
708-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:677:13-35
709            android:taskAffinity="com.lody.virtual.vt"
709-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:678:13-55
710            android:theme="@android:style/Theme.Dialog" />
710-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:679:13-56
711        <activity
711-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:680:9-685:59
712            android:name="com.lody.virtual.client.stub.StubDialog$C14"
712-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:681:13-71
713            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
713-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:682:13-170
714            android:process=":p14"
714-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:683:13-35
715            android:taskAffinity="com.lody.virtual.vt"
715-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:684:13-55
716            android:theme="@android:style/Theme.Dialog" />
716-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:685:13-56
717        <activity
717-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:686:9-691:59
718            android:name="com.lody.virtual.client.stub.StubDialog$C15"
718-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:687:13-71
719            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
719-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:688:13-170
720            android:process=":p15"
720-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:689:13-35
721            android:taskAffinity="com.lody.virtual.vt"
721-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:690:13-55
722            android:theme="@android:style/Theme.Dialog" />
722-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:691:13-56
723        <activity
723-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:692:9-697:59
724            android:name="com.lody.virtual.client.stub.StubDialog$C16"
724-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:693:13-71
725            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
725-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:694:13-170
726            android:process=":p16"
726-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:695:13-35
727            android:taskAffinity="com.lody.virtual.vt"
727-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:696:13-55
728            android:theme="@android:style/Theme.Dialog" />
728-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:697:13-56
729        <activity
729-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:698:9-703:59
730            android:name="com.lody.virtual.client.stub.StubDialog$C17"
730-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:699:13-71
731            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
731-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:700:13-170
732            android:process=":p17"
732-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:701:13-35
733            android:taskAffinity="com.lody.virtual.vt"
733-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:702:13-55
734            android:theme="@android:style/Theme.Dialog" />
734-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:703:13-56
735        <activity
735-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:704:9-709:59
736            android:name="com.lody.virtual.client.stub.StubDialog$C18"
736-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:705:13-71
737            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
737-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:706:13-170
738            android:process=":p18"
738-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:707:13-35
739            android:taskAffinity="com.lody.virtual.vt"
739-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:708:13-55
740            android:theme="@android:style/Theme.Dialog" />
740-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:709:13-56
741        <activity
741-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:710:9-715:59
742            android:name="com.lody.virtual.client.stub.StubDialog$C19"
742-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:711:13-71
743            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
743-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:712:13-170
744            android:process=":p19"
744-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:713:13-35
745            android:taskAffinity="com.lody.virtual.vt"
745-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:714:13-55
746            android:theme="@android:style/Theme.Dialog" />
746-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:715:13-56
747        <activity
747-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:716:9-721:59
748            android:name="com.lody.virtual.client.stub.StubDialog$C20"
748-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:717:13-71
749            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
749-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:718:13-170
750            android:process=":p20"
750-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:719:13-35
751            android:taskAffinity="com.lody.virtual.vt"
751-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:720:13-55
752            android:theme="@android:style/Theme.Dialog" />
752-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:721:13-56
753        <activity
753-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:722:9-727:59
754            android:name="com.lody.virtual.client.stub.StubDialog$C21"
754-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:723:13-71
755            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
755-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:724:13-170
756            android:process=":p21"
756-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:725:13-35
757            android:taskAffinity="com.lody.virtual.vt"
757-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:726:13-55
758            android:theme="@android:style/Theme.Dialog" />
758-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:727:13-56
759        <activity
759-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:728:9-733:59
760            android:name="com.lody.virtual.client.stub.StubDialog$C22"
760-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:729:13-71
761            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
761-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:730:13-170
762            android:process=":p22"
762-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:731:13-35
763            android:taskAffinity="com.lody.virtual.vt"
763-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:732:13-55
764            android:theme="@android:style/Theme.Dialog" />
764-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:733:13-56
765        <activity
765-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:734:9-739:59
766            android:name="com.lody.virtual.client.stub.StubDialog$C23"
766-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:735:13-71
767            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
767-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:736:13-170
768            android:process=":p23"
768-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:737:13-35
769            android:taskAffinity="com.lody.virtual.vt"
769-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:738:13-55
770            android:theme="@android:style/Theme.Dialog" />
770-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:739:13-56
771        <activity
771-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:740:9-745:59
772            android:name="com.lody.virtual.client.stub.StubDialog$C24"
772-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:741:13-71
773            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
773-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:742:13-170
774            android:process=":p24"
774-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:743:13-35
775            android:taskAffinity="com.lody.virtual.vt"
775-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:744:13-55
776            android:theme="@android:style/Theme.Dialog" />
776-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:745:13-56
777        <activity
777-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:746:9-751:59
778            android:name="com.lody.virtual.client.stub.StubDialog$C25"
778-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:747:13-71
779            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
779-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:748:13-170
780            android:process=":p25"
780-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:749:13-35
781            android:taskAffinity="com.lody.virtual.vt"
781-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:750:13-55
782            android:theme="@android:style/Theme.Dialog" />
782-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:751:13-56
783        <activity
783-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:752:9-757:59
784            android:name="com.lody.virtual.client.stub.StubDialog$C26"
784-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:753:13-71
785            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
785-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:754:13-170
786            android:process=":p26"
786-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:755:13-35
787            android:taskAffinity="com.lody.virtual.vt"
787-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:756:13-55
788            android:theme="@android:style/Theme.Dialog" />
788-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:757:13-56
789        <activity
789-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:758:9-763:59
790            android:name="com.lody.virtual.client.stub.StubDialog$C27"
790-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:759:13-71
791            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
791-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:760:13-170
792            android:process=":p27"
792-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:761:13-35
793            android:taskAffinity="com.lody.virtual.vt"
793-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:762:13-55
794            android:theme="@android:style/Theme.Dialog" />
794-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:763:13-56
795        <activity
795-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:764:9-769:59
796            android:name="com.lody.virtual.client.stub.StubDialog$C28"
796-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:765:13-71
797            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
797-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:766:13-170
798            android:process=":p28"
798-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:767:13-35
799            android:taskAffinity="com.lody.virtual.vt"
799-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:768:13-55
800            android:theme="@android:style/Theme.Dialog" />
800-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:769:13-56
801        <activity
801-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:770:9-775:59
802            android:name="com.lody.virtual.client.stub.StubDialog$C29"
802-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:771:13-71
803            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
803-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:772:13-170
804            android:process=":p29"
804-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:773:13-35
805            android:taskAffinity="com.lody.virtual.vt"
805-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:774:13-55
806            android:theme="@android:style/Theme.Dialog" />
806-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:775:13-56
807        <activity
807-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:776:9-781:59
808            android:name="com.lody.virtual.client.stub.StubDialog$C30"
808-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:777:13-71
809            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
809-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:778:13-170
810            android:process=":p30"
810-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:779:13-35
811            android:taskAffinity="com.lody.virtual.vt"
811-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:780:13-55
812            android:theme="@android:style/Theme.Dialog" />
812-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:781:13-56
813        <activity
813-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:782:9-787:59
814            android:name="com.lody.virtual.client.stub.StubDialog$C31"
814-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:783:13-71
815            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
815-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:784:13-170
816            android:process=":p31"
816-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:785:13-35
817            android:taskAffinity="com.lody.virtual.vt"
817-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:786:13-55
818            android:theme="@android:style/Theme.Dialog" />
818-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:787:13-56
819        <activity
819-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:788:9-793:59
820            android:name="com.lody.virtual.client.stub.StubDialog$C32"
820-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:789:13-71
821            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
821-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:790:13-170
822            android:process=":p32"
822-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:791:13-35
823            android:taskAffinity="com.lody.virtual.vt"
823-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:792:13-55
824            android:theme="@android:style/Theme.Dialog" />
824-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:793:13-56
825        <activity
825-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:794:9-799:59
826            android:name="com.lody.virtual.client.stub.StubDialog$C33"
826-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:795:13-71
827            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
827-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:796:13-170
828            android:process=":p33"
828-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:797:13-35
829            android:taskAffinity="com.lody.virtual.vt"
829-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:798:13-55
830            android:theme="@android:style/Theme.Dialog" />
830-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:799:13-56
831        <activity
831-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:800:9-805:59
832            android:name="com.lody.virtual.client.stub.StubDialog$C34"
832-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:801:13-71
833            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
833-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:802:13-170
834            android:process=":p34"
834-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:803:13-35
835            android:taskAffinity="com.lody.virtual.vt"
835-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:804:13-55
836            android:theme="@android:style/Theme.Dialog" />
836-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:805:13-56
837        <activity
837-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:806:9-811:59
838            android:name="com.lody.virtual.client.stub.StubDialog$C35"
838-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:807:13-71
839            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
839-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:808:13-170
840            android:process=":p35"
840-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:809:13-35
841            android:taskAffinity="com.lody.virtual.vt"
841-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:810:13-55
842            android:theme="@android:style/Theme.Dialog" />
842-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:811:13-56
843        <activity
843-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:812:9-817:59
844            android:name="com.lody.virtual.client.stub.StubDialog$C36"
844-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:813:13-71
845            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
845-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:814:13-170
846            android:process=":p36"
846-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:815:13-35
847            android:taskAffinity="com.lody.virtual.vt"
847-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:816:13-55
848            android:theme="@android:style/Theme.Dialog" />
848-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:817:13-56
849        <activity
849-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:818:9-823:59
850            android:name="com.lody.virtual.client.stub.StubDialog$C37"
850-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:819:13-71
851            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
851-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:820:13-170
852            android:process=":p37"
852-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:821:13-35
853            android:taskAffinity="com.lody.virtual.vt"
853-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:822:13-55
854            android:theme="@android:style/Theme.Dialog" />
854-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:823:13-56
855        <activity
855-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:824:9-829:59
856            android:name="com.lody.virtual.client.stub.StubDialog$C38"
856-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:825:13-71
857            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
857-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:826:13-170
858            android:process=":p38"
858-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:827:13-35
859            android:taskAffinity="com.lody.virtual.vt"
859-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:828:13-55
860            android:theme="@android:style/Theme.Dialog" />
860-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:829:13-56
861        <activity
861-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:830:9-835:59
862            android:name="com.lody.virtual.client.stub.StubDialog$C39"
862-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:831:13-71
863            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
863-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:832:13-170
864            android:process=":p39"
864-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:833:13-35
865            android:taskAffinity="com.lody.virtual.vt"
865-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:834:13-55
866            android:theme="@android:style/Theme.Dialog" />
866-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:835:13-56
867        <activity
867-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:836:9-841:59
868            android:name="com.lody.virtual.client.stub.StubDialog$C40"
868-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:837:13-71
869            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
869-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:838:13-170
870            android:process=":p40"
870-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:839:13-35
871            android:taskAffinity="com.lody.virtual.vt"
871-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:840:13-55
872            android:theme="@android:style/Theme.Dialog" />
872-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:841:13-56
873        <activity
873-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:842:9-847:59
874            android:name="com.lody.virtual.client.stub.StubDialog$C41"
874-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:843:13-71
875            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
875-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:844:13-170
876            android:process=":p41"
876-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:845:13-35
877            android:taskAffinity="com.lody.virtual.vt"
877-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:846:13-55
878            android:theme="@android:style/Theme.Dialog" />
878-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:847:13-56
879        <activity
879-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:848:9-853:59
880            android:name="com.lody.virtual.client.stub.StubDialog$C42"
880-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:849:13-71
881            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
881-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:850:13-170
882            android:process=":p42"
882-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:851:13-35
883            android:taskAffinity="com.lody.virtual.vt"
883-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:852:13-55
884            android:theme="@android:style/Theme.Dialog" />
884-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:853:13-56
885        <activity
885-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:854:9-859:59
886            android:name="com.lody.virtual.client.stub.StubDialog$C43"
886-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:855:13-71
887            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
887-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:856:13-170
888            android:process=":p43"
888-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:857:13-35
889            android:taskAffinity="com.lody.virtual.vt"
889-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:858:13-55
890            android:theme="@android:style/Theme.Dialog" />
890-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:859:13-56
891        <activity
891-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:860:9-865:59
892            android:name="com.lody.virtual.client.stub.StubDialog$C44"
892-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:861:13-71
893            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
893-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:862:13-170
894            android:process=":p44"
894-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:863:13-35
895            android:taskAffinity="com.lody.virtual.vt"
895-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:864:13-55
896            android:theme="@android:style/Theme.Dialog" />
896-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:865:13-56
897        <activity
897-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:866:9-871:59
898            android:name="com.lody.virtual.client.stub.StubDialog$C45"
898-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:867:13-71
899            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
899-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:868:13-170
900            android:process=":p45"
900-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:869:13-35
901            android:taskAffinity="com.lody.virtual.vt"
901-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:870:13-55
902            android:theme="@android:style/Theme.Dialog" />
902-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:871:13-56
903        <activity
903-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:872:9-877:59
904            android:name="com.lody.virtual.client.stub.StubDialog$C46"
904-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:873:13-71
905            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
905-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:874:13-170
906            android:process=":p46"
906-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:875:13-35
907            android:taskAffinity="com.lody.virtual.vt"
907-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:876:13-55
908            android:theme="@android:style/Theme.Dialog" />
908-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:877:13-56
909        <activity
909-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:878:9-883:59
910            android:name="com.lody.virtual.client.stub.StubDialog$C47"
910-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:879:13-71
911            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
911-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:880:13-170
912            android:process=":p47"
912-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:881:13-35
913            android:taskAffinity="com.lody.virtual.vt"
913-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:882:13-55
914            android:theme="@android:style/Theme.Dialog" />
914-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:883:13-56
915        <activity
915-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:884:9-889:59
916            android:name="com.lody.virtual.client.stub.StubDialog$C48"
916-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:885:13-71
917            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
917-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:886:13-170
918            android:process=":p48"
918-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:887:13-35
919            android:taskAffinity="com.lody.virtual.vt"
919-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:888:13-55
920            android:theme="@android:style/Theme.Dialog" />
920-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:889:13-56
921        <activity
921-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:890:9-895:59
922            android:name="com.lody.virtual.client.stub.StubDialog$C49"
922-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:891:13-71
923            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
923-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:892:13-170
924            android:process=":p49"
924-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:893:13-35
925            android:taskAffinity="com.lody.virtual.vt"
925-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:894:13-55
926            android:theme="@android:style/Theme.Dialog" />
926-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:895:13-56
927
928        <provider
928-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:897:9-901:37
929            android:name="com.lody.virtual.client.stub.StubContentProvider$C0"
929-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:898:13-79
930            android:authorities="io.virtualapp.virtual_stub_0"
930-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:899:13-66
931            android:exported="false"
931-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:900:13-37
932            android:process=":p0" />
932-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:901:13-34
933        <provider
933-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:902:9-906:37
934            android:name="com.lody.virtual.client.stub.StubContentProvider$C1"
934-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:903:13-79
935            android:authorities="io.virtualapp.virtual_stub_1"
935-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:904:13-66
936            android:exported="false"
936-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:905:13-37
937            android:process=":p1" />
937-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:906:13-34
938        <provider
938-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:907:9-911:37
939            android:name="com.lody.virtual.client.stub.StubContentProvider$C2"
939-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:908:13-79
940            android:authorities="io.virtualapp.virtual_stub_2"
940-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:909:13-66
941            android:exported="false"
941-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:910:13-37
942            android:process=":p2" />
942-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:911:13-34
943        <provider
943-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:912:9-916:37
944            android:name="com.lody.virtual.client.stub.StubContentProvider$C3"
944-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:913:13-79
945            android:authorities="io.virtualapp.virtual_stub_3"
945-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:914:13-66
946            android:exported="false"
946-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:915:13-37
947            android:process=":p3" />
947-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:916:13-34
948        <provider
948-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:917:9-921:37
949            android:name="com.lody.virtual.client.stub.StubContentProvider$C4"
949-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:918:13-79
950            android:authorities="io.virtualapp.virtual_stub_4"
950-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:919:13-66
951            android:exported="false"
951-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:920:13-37
952            android:process=":p4" />
952-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:921:13-34
953        <provider
953-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:922:9-926:37
954            android:name="com.lody.virtual.client.stub.StubContentProvider$C5"
954-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:923:13-79
955            android:authorities="io.virtualapp.virtual_stub_5"
955-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:924:13-66
956            android:exported="false"
956-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:925:13-37
957            android:process=":p5" />
957-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:926:13-34
958        <provider
958-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:927:9-931:37
959            android:name="com.lody.virtual.client.stub.StubContentProvider$C6"
959-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:928:13-79
960            android:authorities="io.virtualapp.virtual_stub_6"
960-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:929:13-66
961            android:exported="false"
961-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:930:13-37
962            android:process=":p6" />
962-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:931:13-34
963        <provider
963-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:932:9-936:37
964            android:name="com.lody.virtual.client.stub.StubContentProvider$C7"
964-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:933:13-79
965            android:authorities="io.virtualapp.virtual_stub_7"
965-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:934:13-66
966            android:exported="false"
966-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:935:13-37
967            android:process=":p7" />
967-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:936:13-34
968        <provider
968-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:937:9-941:37
969            android:name="com.lody.virtual.client.stub.StubContentProvider$C8"
969-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:938:13-79
970            android:authorities="io.virtualapp.virtual_stub_8"
970-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:939:13-66
971            android:exported="false"
971-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:940:13-37
972            android:process=":p8" />
972-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:941:13-34
973        <provider
973-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:942:9-946:37
974            android:name="com.lody.virtual.client.stub.StubContentProvider$C9"
974-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:943:13-79
975            android:authorities="io.virtualapp.virtual_stub_9"
975-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:944:13-66
976            android:exported="false"
976-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:945:13-37
977            android:process=":p9" />
977-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:946:13-34
978        <provider
978-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:947:9-951:38
979            android:name="com.lody.virtual.client.stub.StubContentProvider$C10"
979-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:948:13-80
980            android:authorities="io.virtualapp.virtual_stub_10"
980-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:949:13-67
981            android:exported="false"
981-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:950:13-37
982            android:process=":p10" />
982-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:951:13-35
983        <provider
983-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:952:9-956:38
984            android:name="com.lody.virtual.client.stub.StubContentProvider$C11"
984-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:953:13-80
985            android:authorities="io.virtualapp.virtual_stub_11"
985-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:954:13-67
986            android:exported="false"
986-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:955:13-37
987            android:process=":p11" />
987-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:956:13-35
988        <provider
988-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:957:9-961:38
989            android:name="com.lody.virtual.client.stub.StubContentProvider$C12"
989-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:958:13-80
990            android:authorities="io.virtualapp.virtual_stub_12"
990-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:959:13-67
991            android:exported="false"
991-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:960:13-37
992            android:process=":p12" />
992-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:961:13-35
993        <provider
993-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:962:9-966:38
994            android:name="com.lody.virtual.client.stub.StubContentProvider$C13"
994-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:963:13-80
995            android:authorities="io.virtualapp.virtual_stub_13"
995-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:964:13-67
996            android:exported="false"
996-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:965:13-37
997            android:process=":p13" />
997-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:966:13-35
998        <provider
998-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:967:9-971:38
999            android:name="com.lody.virtual.client.stub.StubContentProvider$C14"
999-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:968:13-80
1000            android:authorities="io.virtualapp.virtual_stub_14"
1000-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:969:13-67
1001            android:exported="false"
1001-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:970:13-37
1002            android:process=":p14" />
1002-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:971:13-35
1003        <provider
1003-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:972:9-976:38
1004            android:name="com.lody.virtual.client.stub.StubContentProvider$C15"
1004-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:973:13-80
1005            android:authorities="io.virtualapp.virtual_stub_15"
1005-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:974:13-67
1006            android:exported="false"
1006-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:975:13-37
1007            android:process=":p15" />
1007-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:976:13-35
1008        <provider
1008-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:977:9-981:38
1009            android:name="com.lody.virtual.client.stub.StubContentProvider$C16"
1009-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:978:13-80
1010            android:authorities="io.virtualapp.virtual_stub_16"
1010-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:979:13-67
1011            android:exported="false"
1011-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:980:13-37
1012            android:process=":p16" />
1012-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:981:13-35
1013        <provider
1013-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:982:9-986:38
1014            android:name="com.lody.virtual.client.stub.StubContentProvider$C17"
1014-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:983:13-80
1015            android:authorities="io.virtualapp.virtual_stub_17"
1015-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:984:13-67
1016            android:exported="false"
1016-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:985:13-37
1017            android:process=":p17" />
1017-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:986:13-35
1018        <provider
1018-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:987:9-991:38
1019            android:name="com.lody.virtual.client.stub.StubContentProvider$C18"
1019-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:988:13-80
1020            android:authorities="io.virtualapp.virtual_stub_18"
1020-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:989:13-67
1021            android:exported="false"
1021-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:990:13-37
1022            android:process=":p18" />
1022-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:991:13-35
1023        <provider
1023-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:992:9-996:38
1024            android:name="com.lody.virtual.client.stub.StubContentProvider$C19"
1024-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:993:13-80
1025            android:authorities="io.virtualapp.virtual_stub_19"
1025-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:994:13-67
1026            android:exported="false"
1026-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:995:13-37
1027            android:process=":p19" />
1027-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:996:13-35
1028        <provider
1028-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:997:9-1001:38
1029            android:name="com.lody.virtual.client.stub.StubContentProvider$C20"
1029-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:998:13-80
1030            android:authorities="io.virtualapp.virtual_stub_20"
1030-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:999:13-67
1031            android:exported="false"
1031-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1000:13-37
1032            android:process=":p20" />
1032-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1001:13-35
1033        <provider
1033-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1002:9-1006:38
1034            android:name="com.lody.virtual.client.stub.StubContentProvider$C21"
1034-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1003:13-80
1035            android:authorities="io.virtualapp.virtual_stub_21"
1035-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1004:13-67
1036            android:exported="false"
1036-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1005:13-37
1037            android:process=":p21" />
1037-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1006:13-35
1038        <provider
1038-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1007:9-1011:38
1039            android:name="com.lody.virtual.client.stub.StubContentProvider$C22"
1039-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1008:13-80
1040            android:authorities="io.virtualapp.virtual_stub_22"
1040-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1009:13-67
1041            android:exported="false"
1041-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1010:13-37
1042            android:process=":p22" />
1042-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1011:13-35
1043        <provider
1043-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1012:9-1016:38
1044            android:name="com.lody.virtual.client.stub.StubContentProvider$C23"
1044-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1013:13-80
1045            android:authorities="io.virtualapp.virtual_stub_23"
1045-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1014:13-67
1046            android:exported="false"
1046-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1015:13-37
1047            android:process=":p23" />
1047-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1016:13-35
1048        <provider
1048-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1017:9-1021:38
1049            android:name="com.lody.virtual.client.stub.StubContentProvider$C24"
1049-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1018:13-80
1050            android:authorities="io.virtualapp.virtual_stub_24"
1050-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1019:13-67
1051            android:exported="false"
1051-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1020:13-37
1052            android:process=":p24" />
1052-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1021:13-35
1053        <provider
1053-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1022:9-1026:38
1054            android:name="com.lody.virtual.client.stub.StubContentProvider$C25"
1054-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1023:13-80
1055            android:authorities="io.virtualapp.virtual_stub_25"
1055-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1024:13-67
1056            android:exported="false"
1056-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1025:13-37
1057            android:process=":p25" />
1057-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1026:13-35
1058        <provider
1058-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1027:9-1031:38
1059            android:name="com.lody.virtual.client.stub.StubContentProvider$C26"
1059-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1028:13-80
1060            android:authorities="io.virtualapp.virtual_stub_26"
1060-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1029:13-67
1061            android:exported="false"
1061-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1030:13-37
1062            android:process=":p26" />
1062-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1031:13-35
1063        <provider
1063-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1032:9-1036:38
1064            android:name="com.lody.virtual.client.stub.StubContentProvider$C27"
1064-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1033:13-80
1065            android:authorities="io.virtualapp.virtual_stub_27"
1065-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1034:13-67
1066            android:exported="false"
1066-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1035:13-37
1067            android:process=":p27" />
1067-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1036:13-35
1068        <provider
1068-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1037:9-1041:38
1069            android:name="com.lody.virtual.client.stub.StubContentProvider$C28"
1069-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1038:13-80
1070            android:authorities="io.virtualapp.virtual_stub_28"
1070-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1039:13-67
1071            android:exported="false"
1071-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1040:13-37
1072            android:process=":p28" />
1072-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1041:13-35
1073        <provider
1073-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1042:9-1046:38
1074            android:name="com.lody.virtual.client.stub.StubContentProvider$C29"
1074-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1043:13-80
1075            android:authorities="io.virtualapp.virtual_stub_29"
1075-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1044:13-67
1076            android:exported="false"
1076-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1045:13-37
1077            android:process=":p29" />
1077-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1046:13-35
1078        <provider
1078-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1047:9-1051:38
1079            android:name="com.lody.virtual.client.stub.StubContentProvider$C30"
1079-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1048:13-80
1080            android:authorities="io.virtualapp.virtual_stub_30"
1080-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1049:13-67
1081            android:exported="false"
1081-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1050:13-37
1082            android:process=":p30" />
1082-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1051:13-35
1083        <provider
1083-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1052:9-1056:38
1084            android:name="com.lody.virtual.client.stub.StubContentProvider$C31"
1084-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1053:13-80
1085            android:authorities="io.virtualapp.virtual_stub_31"
1085-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1054:13-67
1086            android:exported="false"
1086-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1055:13-37
1087            android:process=":p31" />
1087-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1056:13-35
1088        <provider
1088-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1057:9-1061:38
1089            android:name="com.lody.virtual.client.stub.StubContentProvider$C32"
1089-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1058:13-80
1090            android:authorities="io.virtualapp.virtual_stub_32"
1090-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1059:13-67
1091            android:exported="false"
1091-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1060:13-37
1092            android:process=":p32" />
1092-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1061:13-35
1093        <provider
1093-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1062:9-1066:38
1094            android:name="com.lody.virtual.client.stub.StubContentProvider$C33"
1094-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1063:13-80
1095            android:authorities="io.virtualapp.virtual_stub_33"
1095-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1064:13-67
1096            android:exported="false"
1096-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1065:13-37
1097            android:process=":p33" />
1097-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1066:13-35
1098        <provider
1098-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1067:9-1071:38
1099            android:name="com.lody.virtual.client.stub.StubContentProvider$C34"
1099-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1068:13-80
1100            android:authorities="io.virtualapp.virtual_stub_34"
1100-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1069:13-67
1101            android:exported="false"
1101-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1070:13-37
1102            android:process=":p34" />
1102-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1071:13-35
1103        <provider
1103-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1072:9-1076:38
1104            android:name="com.lody.virtual.client.stub.StubContentProvider$C35"
1104-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1073:13-80
1105            android:authorities="io.virtualapp.virtual_stub_35"
1105-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1074:13-67
1106            android:exported="false"
1106-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1075:13-37
1107            android:process=":p35" />
1107-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1076:13-35
1108        <provider
1108-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1077:9-1081:38
1109            android:name="com.lody.virtual.client.stub.StubContentProvider$C36"
1109-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1078:13-80
1110            android:authorities="io.virtualapp.virtual_stub_36"
1110-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1079:13-67
1111            android:exported="false"
1111-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1080:13-37
1112            android:process=":p36" />
1112-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1081:13-35
1113        <provider
1113-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1082:9-1086:38
1114            android:name="com.lody.virtual.client.stub.StubContentProvider$C37"
1114-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1083:13-80
1115            android:authorities="io.virtualapp.virtual_stub_37"
1115-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1084:13-67
1116            android:exported="false"
1116-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1085:13-37
1117            android:process=":p37" />
1117-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1086:13-35
1118        <provider
1118-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1087:9-1091:38
1119            android:name="com.lody.virtual.client.stub.StubContentProvider$C38"
1119-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1088:13-80
1120            android:authorities="io.virtualapp.virtual_stub_38"
1120-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1089:13-67
1121            android:exported="false"
1121-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1090:13-37
1122            android:process=":p38" />
1122-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1091:13-35
1123        <provider
1123-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1092:9-1096:38
1124            android:name="com.lody.virtual.client.stub.StubContentProvider$C39"
1124-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1093:13-80
1125            android:authorities="io.virtualapp.virtual_stub_39"
1125-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1094:13-67
1126            android:exported="false"
1126-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1095:13-37
1127            android:process=":p39" />
1127-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1096:13-35
1128        <provider
1128-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1097:9-1101:38
1129            android:name="com.lody.virtual.client.stub.StubContentProvider$C40"
1129-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1098:13-80
1130            android:authorities="io.virtualapp.virtual_stub_40"
1130-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1099:13-67
1131            android:exported="false"
1131-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1100:13-37
1132            android:process=":p40" />
1132-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1101:13-35
1133        <provider
1133-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1102:9-1106:38
1134            android:name="com.lody.virtual.client.stub.StubContentProvider$C41"
1134-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1103:13-80
1135            android:authorities="io.virtualapp.virtual_stub_41"
1135-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1104:13-67
1136            android:exported="false"
1136-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1105:13-37
1137            android:process=":p41" />
1137-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1106:13-35
1138        <provider
1138-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1107:9-1111:38
1139            android:name="com.lody.virtual.client.stub.StubContentProvider$C42"
1139-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1108:13-80
1140            android:authorities="io.virtualapp.virtual_stub_42"
1140-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1109:13-67
1141            android:exported="false"
1141-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1110:13-37
1142            android:process=":p42" />
1142-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1111:13-35
1143        <provider
1143-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1112:9-1116:38
1144            android:name="com.lody.virtual.client.stub.StubContentProvider$C43"
1144-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1113:13-80
1145            android:authorities="io.virtualapp.virtual_stub_43"
1145-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1114:13-67
1146            android:exported="false"
1146-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1115:13-37
1147            android:process=":p43" />
1147-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1116:13-35
1148        <provider
1148-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1117:9-1121:38
1149            android:name="com.lody.virtual.client.stub.StubContentProvider$C44"
1149-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1118:13-80
1150            android:authorities="io.virtualapp.virtual_stub_44"
1150-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1119:13-67
1151            android:exported="false"
1151-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1120:13-37
1152            android:process=":p44" />
1152-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1121:13-35
1153        <provider
1153-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1122:9-1126:38
1154            android:name="com.lody.virtual.client.stub.StubContentProvider$C45"
1154-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1123:13-80
1155            android:authorities="io.virtualapp.virtual_stub_45"
1155-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1124:13-67
1156            android:exported="false"
1156-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1125:13-37
1157            android:process=":p45" />
1157-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1126:13-35
1158        <provider
1158-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1127:9-1131:38
1159            android:name="com.lody.virtual.client.stub.StubContentProvider$C46"
1159-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1128:13-80
1160            android:authorities="io.virtualapp.virtual_stub_46"
1160-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1129:13-67
1161            android:exported="false"
1161-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1130:13-37
1162            android:process=":p46" />
1162-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1131:13-35
1163        <provider
1163-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1132:9-1136:38
1164            android:name="com.lody.virtual.client.stub.StubContentProvider$C47"
1164-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1133:13-80
1165            android:authorities="io.virtualapp.virtual_stub_47"
1165-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1134:13-67
1166            android:exported="false"
1166-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1135:13-37
1167            android:process=":p47" />
1167-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1136:13-35
1168        <provider
1168-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1137:9-1141:38
1169            android:name="com.lody.virtual.client.stub.StubContentProvider$C48"
1169-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1138:13-80
1170            android:authorities="io.virtualapp.virtual_stub_48"
1170-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1139:13-67
1171            android:exported="false"
1171-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1140:13-37
1172            android:process=":p48" />
1172-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1141:13-35
1173        <provider
1173-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1142:9-1146:38
1174            android:name="com.lody.virtual.client.stub.StubContentProvider$C49"
1174-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1143:13-80
1175            android:authorities="io.virtualapp.virtual_stub_49"
1175-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1144:13-67
1176            android:exported="false"
1176-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1145:13-37
1177            android:process=":p49" />
1177-->[:lib] D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:1146:13-35
1178        <provider
1178-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
1179            android:name="androidx.startup.InitializationProvider"
1179-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
1180            android:authorities="io.virtualapp.androidx-startup"
1180-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
1181            android:exported="false" >
1181-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
1182            <meta-data
1182-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
1183                android:name="androidx.emoji2.text.EmojiCompatInitializer"
1183-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
1184                android:value="androidx.startup" />
1184-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\62b28d7eb847e7abf6aaaff8ea6ef2f1\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
1185            <meta-data
1185-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
1186                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
1186-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
1187                android:value="androidx.startup" />
1187-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6067670ce502994b3fa6fe45200d3c72\transformed\jetified-lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
1188            <meta-data
1188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
1189                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
1189-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
1190                android:value="androidx.startup" />
1190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
1191        </provider>
1192
1193        <receiver
1193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
1194            android:name="androidx.profileinstaller.ProfileInstallReceiver"
1194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
1195            android:directBootAware="false"
1195-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
1196            android:enabled="true"
1196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
1197            android:exported="true"
1197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
1198            android:permission="android.permission.DUMP" >
1198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
1199            <intent-filter>
1199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
1200                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
1200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
1200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
1201            </intent-filter>
1202            <intent-filter>
1202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
1203                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
1203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
1203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
1204            </intent-filter>
1205            <intent-filter>
1205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
1206                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
1206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
1206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
1207            </intent-filter>
1208            <intent-filter>
1208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
1209                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
1209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
1209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\eac5a751c1d4e91bba643d59ea33cf96\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
1210            </intent-filter>
1211        </receiver>
1212    </application>
1213
1214</manifest>
