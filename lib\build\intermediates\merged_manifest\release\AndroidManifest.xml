<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.lody.virtual" >

    <uses-sdk android:minSdkVersion="21" />

    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" />
    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
    <uses-permission
        android:name="android.permission.ACCOUNT_MANAGER"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.PACKAGE_USAGE_STATS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission
        android:name="android.permission.ACCESS_MOCK_LOCATION"
        tools:ignore="MockLocation" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
    <uses-permission
        android:name="android.permission.BIND_APPWIDGET"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BODY_SENSORS" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
    <uses-permission android:name="android.permission.GET_CLIPS" />
    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
    <uses-permission android:name="android.permission.READ_CALENDAR" />
    <uses-permission android:name="android.permission.READ_CALL_LOG" />
    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
    <uses-permission android:name="android.permission.READ_CLIPS" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECEIVE_MMS" />
    <uses-permission android:name="android.permission.RECEIVE_SMS" />
    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REORDER_TASKS" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.SEND_SMS" />
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
    <uses-permission android:name="android.permission.SET_WALLPAPER" />
    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.TRANSMIT_IR" />
    <uses-permission android:name="android.permission.USE_SIP" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
    <uses-permission android:name="android.permission.WRITE_CLIPS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_PROFILE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SMS" />
    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adsense" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adwords" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ah" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.android" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.androidsecure" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.blogger" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cl" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cp" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.dodgeball" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.finance" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.gbase" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.grandcentral" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.groups2" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.health" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ig" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.jotspot" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.knol" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.lh2" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.local" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mail" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mobile" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.news" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.notebook" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.orkut" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.print" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierra" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierraqa" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierrasandbox" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sitemaps" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speech" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speechpersonalization" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.talk" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wifi" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wise" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.writely" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.youtube" />
    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.DELETE_PACKAGES"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.CLEAR_APP_USER_DATA"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.WRITE_MEDIA_STORAGE"
        tools:ignore="ProtectedPermissions" />
    <uses-permission
        android:name="android.permission.ACCESS_CACHE_FILESYSTEM"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission
        android:name="android.permission.DEVICE_POWER"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />

    <!-- Required for Android 11+ to query other apps -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />
    <uses-permission
        android:name="android.permission.WRITE_APN_SETTINGS"
        tools:ignore="ProtectedPermissions" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <application>
        <service
            android:name="com.lody.virtual.client.stub.DaemonService"
            android:process="@string/engine_process_name" />
        <service
            android:name="com.lody.virtual.client.stub.DaemonService$InnerService"
            android:process="@string/engine_process_name" />

        <activity
            android:name="com.lody.virtual.client.stub.ShortcutHandleActivity"
            android:exported="true"
            android:process="@string/engine_process_name"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.lody.virtual.client.stub.StubPendingActivity"
            android:process="@string/engine_process_name" />

        <service
            android:name="com.lody.virtual.client.stub.StubPendingService"
            android:process="@string/engine_process_name" />

        <receiver
            android:name="com.lody.virtual.client.stub.StubPendingReceiver"
            android:process="@string/engine_process_name" />

        <service
            android:name="com.lody.virtual.client.stub.StubJob"
            android:permission="android.permission.BIND_JOB_SERVICE"
            android:process="@string/engine_process_name" />

        <activity
            android:name="com.lody.virtual.client.stub.ChooseAccountTypeActivity"
            android:configChanges="keyboard|keyboardHidden|orientation"
            android:excludeFromRecents="true"
            android:exported="false"
            android:process="@string/engine_process_name"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"
            android:configChanges="keyboard|keyboardHidden|orientation"
            android:excludeFromRecents="true"
            android:exported="false"
            android:process="@string/engine_process_name"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.lody.virtual.client.stub.ChooserActivity"
            android:configChanges="keyboard|keyboardHidden|orientation"
            android:excludeFromRecents="true"
            android:exported="true"
            android:finishOnCloseSystemDialogs="true"
            android:process="@string/engine_process_name"
            android:screenOrientation="portrait"
            android:theme="@style/VAAlertTheme" />
        <activity
            android:name="com.lody.virtual.client.stub.ResolverActivity"
            android:configChanges="keyboard|keyboardHidden|orientation"
            android:excludeFromRecents="true"
            android:exported="true"
            android:finishOnCloseSystemDialogs="true"
            android:process="@string/engine_process_name"
            android:screenOrientation="portrait"
            android:theme="@style/VAAlertTheme" />

        <provider
            android:name="com.lody.virtual.server.BinderProvider"
            android:authorities="${applicationId}.virtual.service.BinderProvider"
            android:exported="false"
            android:process="@string/engine_process_name" />

        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C0"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p0"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C1"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p1"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C2"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p2"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C3"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p3"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C4"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p4"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C5"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p5"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C6"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p6"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C7"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p7"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C8"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p8"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C9"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p9"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C10"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p10"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C11"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p11"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C12"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p12"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C13"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p13"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C14"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p14"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C15"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p15"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C16"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p16"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C17"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p17"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C18"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p18"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C19"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p19"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C20"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p20"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C21"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p21"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C22"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p22"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C23"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p23"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C24"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p24"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C25"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p25"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C26"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p26"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C27"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p27"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C28"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p28"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C29"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p29"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C30"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p30"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C31"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p31"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C32"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p32"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C33"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p33"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C34"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p34"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C35"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p35"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C36"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p36"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C37"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p37"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C38"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p38"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C39"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p39"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C40"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p40"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C41"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p41"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C42"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p42"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C43"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p43"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C44"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p44"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C45"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p45"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C46"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p46"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C47"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p47"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C48"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p48"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubActivity$C49"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p49"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@style/VATheme" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C0"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p0"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C1"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p1"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C2"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p2"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C3"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p3"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C4"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p4"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C5"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p5"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C6"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p6"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C7"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p7"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C8"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p8"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C9"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p9"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C10"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p10"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C11"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p11"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C12"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p12"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C13"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p13"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C14"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p14"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C15"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p15"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C16"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p16"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C17"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p17"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C18"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p18"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C19"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p19"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C20"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p20"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C21"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p21"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C22"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p22"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C23"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p23"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C24"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p24"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C25"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p25"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C26"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p26"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C27"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p27"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C28"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p28"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C29"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p29"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C30"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p30"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C31"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p31"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C32"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p32"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C33"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p33"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C34"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p34"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C35"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p35"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C36"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p36"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C37"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p37"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C38"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p38"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C39"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p39"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C40"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p40"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C41"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p41"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C42"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p42"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C43"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p43"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C44"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p44"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C45"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p45"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C46"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p46"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C47"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p47"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C48"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p48"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />
        <activity
            android:name="com.lody.virtual.client.stub.StubDialog$C49"
            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
            android:process=":p49"
            android:taskAffinity="com.lody.virtual.vt"
            android:theme="@android:style/Theme.Dialog" />

        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C0"
            android:authorities="${applicationId}.virtual_stub_0"
            android:exported="false"
            android:process=":p0" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C1"
            android:authorities="${applicationId}.virtual_stub_1"
            android:exported="false"
            android:process=":p1" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C2"
            android:authorities="${applicationId}.virtual_stub_2"
            android:exported="false"
            android:process=":p2" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C3"
            android:authorities="${applicationId}.virtual_stub_3"
            android:exported="false"
            android:process=":p3" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C4"
            android:authorities="${applicationId}.virtual_stub_4"
            android:exported="false"
            android:process=":p4" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C5"
            android:authorities="${applicationId}.virtual_stub_5"
            android:exported="false"
            android:process=":p5" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C6"
            android:authorities="${applicationId}.virtual_stub_6"
            android:exported="false"
            android:process=":p6" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C7"
            android:authorities="${applicationId}.virtual_stub_7"
            android:exported="false"
            android:process=":p7" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C8"
            android:authorities="${applicationId}.virtual_stub_8"
            android:exported="false"
            android:process=":p8" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C9"
            android:authorities="${applicationId}.virtual_stub_9"
            android:exported="false"
            android:process=":p9" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C10"
            android:authorities="${applicationId}.virtual_stub_10"
            android:exported="false"
            android:process=":p10" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C11"
            android:authorities="${applicationId}.virtual_stub_11"
            android:exported="false"
            android:process=":p11" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C12"
            android:authorities="${applicationId}.virtual_stub_12"
            android:exported="false"
            android:process=":p12" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C13"
            android:authorities="${applicationId}.virtual_stub_13"
            android:exported="false"
            android:process=":p13" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C14"
            android:authorities="${applicationId}.virtual_stub_14"
            android:exported="false"
            android:process=":p14" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C15"
            android:authorities="${applicationId}.virtual_stub_15"
            android:exported="false"
            android:process=":p15" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C16"
            android:authorities="${applicationId}.virtual_stub_16"
            android:exported="false"
            android:process=":p16" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C17"
            android:authorities="${applicationId}.virtual_stub_17"
            android:exported="false"
            android:process=":p17" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C18"
            android:authorities="${applicationId}.virtual_stub_18"
            android:exported="false"
            android:process=":p18" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C19"
            android:authorities="${applicationId}.virtual_stub_19"
            android:exported="false"
            android:process=":p19" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C20"
            android:authorities="${applicationId}.virtual_stub_20"
            android:exported="false"
            android:process=":p20" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C21"
            android:authorities="${applicationId}.virtual_stub_21"
            android:exported="false"
            android:process=":p21" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C22"
            android:authorities="${applicationId}.virtual_stub_22"
            android:exported="false"
            android:process=":p22" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C23"
            android:authorities="${applicationId}.virtual_stub_23"
            android:exported="false"
            android:process=":p23" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C24"
            android:authorities="${applicationId}.virtual_stub_24"
            android:exported="false"
            android:process=":p24" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C25"
            android:authorities="${applicationId}.virtual_stub_25"
            android:exported="false"
            android:process=":p25" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C26"
            android:authorities="${applicationId}.virtual_stub_26"
            android:exported="false"
            android:process=":p26" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C27"
            android:authorities="${applicationId}.virtual_stub_27"
            android:exported="false"
            android:process=":p27" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C28"
            android:authorities="${applicationId}.virtual_stub_28"
            android:exported="false"
            android:process=":p28" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C29"
            android:authorities="${applicationId}.virtual_stub_29"
            android:exported="false"
            android:process=":p29" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C30"
            android:authorities="${applicationId}.virtual_stub_30"
            android:exported="false"
            android:process=":p30" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C31"
            android:authorities="${applicationId}.virtual_stub_31"
            android:exported="false"
            android:process=":p31" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C32"
            android:authorities="${applicationId}.virtual_stub_32"
            android:exported="false"
            android:process=":p32" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C33"
            android:authorities="${applicationId}.virtual_stub_33"
            android:exported="false"
            android:process=":p33" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C34"
            android:authorities="${applicationId}.virtual_stub_34"
            android:exported="false"
            android:process=":p34" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C35"
            android:authorities="${applicationId}.virtual_stub_35"
            android:exported="false"
            android:process=":p35" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C36"
            android:authorities="${applicationId}.virtual_stub_36"
            android:exported="false"
            android:process=":p36" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C37"
            android:authorities="${applicationId}.virtual_stub_37"
            android:exported="false"
            android:process=":p37" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C38"
            android:authorities="${applicationId}.virtual_stub_38"
            android:exported="false"
            android:process=":p38" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C39"
            android:authorities="${applicationId}.virtual_stub_39"
            android:exported="false"
            android:process=":p39" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C40"
            android:authorities="${applicationId}.virtual_stub_40"
            android:exported="false"
            android:process=":p40" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C41"
            android:authorities="${applicationId}.virtual_stub_41"
            android:exported="false"
            android:process=":p41" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C42"
            android:authorities="${applicationId}.virtual_stub_42"
            android:exported="false"
            android:process=":p42" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C43"
            android:authorities="${applicationId}.virtual_stub_43"
            android:exported="false"
            android:process=":p43" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C44"
            android:authorities="${applicationId}.virtual_stub_44"
            android:exported="false"
            android:process=":p44" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C45"
            android:authorities="${applicationId}.virtual_stub_45"
            android:exported="false"
            android:process=":p45" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C46"
            android:authorities="${applicationId}.virtual_stub_46"
            android:exported="false"
            android:process=":p46" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C47"
            android:authorities="${applicationId}.virtual_stub_47"
            android:exported="false"
            android:process=":p47" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C48"
            android:authorities="${applicationId}.virtual_stub_48"
            android:exported="false"
            android:process=":p48" />
        <provider
            android:name="com.lody.virtual.client.stub.StubContentProvider$C49"
            android:authorities="${applicationId}.virtual_stub_49"
            android:exported="false"
            android:process=":p49" />
    </application>

</manifest>