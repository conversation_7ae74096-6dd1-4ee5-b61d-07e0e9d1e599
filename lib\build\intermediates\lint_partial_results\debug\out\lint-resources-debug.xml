http://schemas.android.com/apk/res-auto;;${\:lib*debug*sourceProvider*0*resDir*0}/values/dimens.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/app_not_authorized.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/choose_type_and_account.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/custom_notification_lite.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml,${\:lib*debug*sourceProvider*0*resDir*0}/values/integer.xml,${\:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_type.xml,${\:lib*debug*sourceProvider*0*resDir*0}/values/strings.xml,${\:lib*debug*sourceProvider*0*resDir*0}/values/styles.xml,+dimen:standard_notification_panel_width,0,V"416dp";notification_panel_width,0,V"-1dp";notification_max_height,0,V"256dp";notification_mid_height,0,V"128dp";notification_side_padding,0,V"8dp";notification_padding,0,V"4dp";match_parent,0,V"-1px";notification_min_height,0,V"64dp";+id:btn_6,1,F;icon,2,F;description,3,F;description,4,F;btn_5,1,F;btn_4,1,F;btn_3,1,F;btn_9,1,F;btn_8,1,F;btn_7,1,F;btn_20,1,F;btn_21,1,F;btn_22,1,F;btn_2,1,F;btn_23,1,F;btn_1,1,F;btn_24,1,F;btn_25,1,F;btn_26,1,F;btn_27,1,F;btn_28,1,F;btn_29,1,F;button_bar,3,F;button_bar,4,F;im_main,1,F;im_main,5,F;btn_30,1,F;btn_31,1,F;btn_10,1,F;btn_32,1,F;btn_11,1,F;btn_12,1,F;account_row_text,6,F;btn_13,1,F;btn_14,1,F;text1,2,F;btn_15,1,F;text2,2,F;btn_16,1,F;btn_17,1,F;btn_18,1,F;btn_19,1,F;account_row_icon,6,F;+integer:config_maxResolverActivityColumns,7,V"8";+layout:choose_type_and_account,4,F;choose_account_row,6,F;resolve_list_item,2,F;choose_account_type,8,F;custom_notification_lite,5,F;custom_notification,1,F;app_not_authorized,3,F;+string:noApplications,9,V"No find applications";owner_name,9,V"Admin";virtual_installer,9,V"VirtualPackage Installer";choose,9,V"Choose";engine_process_name,9,V"\:x";add_account_button_label,9,V"Add account";choose_empty,9,V"Chooser is Empty";+style:notification_button,10,VNandroid\:layout_width:0dp,android\:layout_height:match_parent,android\:layout_weight:1,;notification_layout,10,VNandroid\:layout_width:match_parent,android\:layout_height:0dp,android\:layout_weight:1,;VATheme,10,VD@android\:style/Theme.Light.NoTitleBar,android\:windowBackground:@android\:color/transparent,android\:windowDisablePreview:true,;VAAlertTheme,10,VDandroid\:Theme.DeviceDefault.Dialog,;