D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/va++/Foundation/SymbolFinder.o: \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/SymbolFinder.cpp \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni/Helper.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/Environment.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/functional \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/type_traits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstddef \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/version \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/typeinfo \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/exception \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdint \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdint.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/memory \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/new \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/utility \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__tuple \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/initializer_list \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstring \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__debug \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iosfwd \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wchar.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iterator \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__functional_base \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/tuple \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdexcept \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/atomic \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__threading_support \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/chrono \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctime \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ratio \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/climits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string_view \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__string \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/algorithm \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bit \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdio \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwchar \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwctype \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cctype \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctype.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wctype.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/visibility.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/ALog.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Common.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Exceptions.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cassert \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/ReferenceAllocators.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/ReferenceAllocators-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/TypeTraits.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References-forward.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/CoreClasses.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta-forward.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/CoreClasses-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/MetaConvert.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Boxed.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/Build.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Iterator.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Iterator-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Hybrid.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/assert.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Registration.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Registration-inl.h \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/SymbolFinder.h

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Jni/Helper.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/Environment.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/functional:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/type_traits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstddef:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/version:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/typeinfo:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/exception:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdint:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdint.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/memory:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/new:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/utility:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__tuple:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/initializer_list:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstring:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__debug:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iosfwd:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wchar.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iterator:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__functional_base:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/tuple:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdexcept:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/atomic:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__threading_support:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/chrono:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctime:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ratio:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/climits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string_view:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__string:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/algorithm:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bit:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdio:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwchar:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwctype:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cctype:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctype.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wctype.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/visibility.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/ALog.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Common.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Exceptions.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cassert:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/ReferenceAllocators.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/ReferenceAllocators-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/TypeTraits.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References-forward.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/References-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/CoreClasses.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta-forward.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/CoreClasses-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Meta-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/MetaConvert.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Boxed.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/Build.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Iterator.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Iterator-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Hybrid.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/assert.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Registration.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/fbjni/Registration-inl.h:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Foundation/SymbolFinder.h:
