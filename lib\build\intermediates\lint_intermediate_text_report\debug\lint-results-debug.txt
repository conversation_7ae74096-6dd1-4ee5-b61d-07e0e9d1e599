D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\BroadcastSystem.java: Error: Unexpected failure during lint analysis of BroadcastSystem.java (this is a bug in lint or one of the libraries it depends on)


The crash seems to involve the detector com.android.tools.lint.checks.UnsafeIntentLaunchDetector.
You can try disabling it with something like this:
    android {
        lint {
            disable "UnsafeIntentLaunch"
        }
    }

Stack: NullPointerException:BroadcastReceiverUtils$ActionCollectorVisitor.argument(BroadcastReceiverUtils.kt:124)←DataFlowAnalyzer.afterVisitCallExpression(DataFlowAnalyzer.kt:355)←UCallExpression.accept(UCallExpression.kt:90)←UVariableKt.visitContents(UVariable.kt:68)←UVariableKt.access$visitContents(UVariable.kt:1)←ULocalVariable.accept(UVariable.kt:123)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UDeclarationsExpression.accept(UDeclarationsExpression.kt:22)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UForEachExpression.accept(UForEachExpression.kt:38)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←BroadcastReceiverUtils.checkIsProtectedReceiverAndReturnUnprotectedActions(BroadcastReceiverUtils.kt:73)←UnsafeIntentLaunchDetector.isRuntimeReceiverProtected(UnsafeIntentLaunchDetector.kt:304)←UnsafeIntentLaunchDetector.processRuntimeReceiver(UnsafeIntentLaunchDetector.kt:276)←UnsafeIntentLaunchDetector.visitMethodCall(UnsafeIntentLaunchDetector.kt:206)←UElementVisitor$DelegatingPsiVisitor.visitMethodCallExpression(UElementVisitor.kt:1082)←UElementVisitor$DelegatingPsiVisitor.visitCallExpression(UElementVisitor.kt:1062)←UCallExpression.accept(UCallExpression.kt:85)←UQualifiedReferenceExpression.accept(UQualifiedReferenceExpression.kt:34)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UForEachExpression.accept(UForEachExpression.kt:38)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UBlockExpression.accept(UBlockExpression.kt:21)←UMethod.accept(UMethod.kt:45)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UClass.accept(UClass.kt:64)←ImplementationUtilsKt.acceptList(implementationUtils.kt:14)←UFile.accept(UFile.kt:89)←UastLintUtilsKt.acceptSourceFile(UastLintUtils.kt:826)←UElementVisitor$visitFile$3.run(UElementVisitor.kt:267)←LintClient.runReadAction(LintClient.kt:1700)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)←UElementVisitor.visitFile(UElementVisitor.kt:264)←LintDriver$visitUastDetectors$1.run(LintDriver.kt:2165)←LintClient.runReadAction(LintClient.kt:1700)←LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)←LintDriver.visitUastDetectors(LintDriver.kt:2165)←LintDriver.visitUast(LintDriver.kt:2127)←LintDriver.runFileDetectors(LintDriver.kt:1379)←LintDriver.checkProject(LintDriver.kt:1144)←LintDriver.checkProjectRoot(LintDriver.kt:615)←LintDriver.access$checkProjectRoot(LintDriver.kt:170)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:441)←LintDriver$analyzeOnly$1.invoke(LintDriver.kt:438)←LintDriver.doAnalyze(LintDriver.kt:497)←LintDriver.analyzeOnly(LintDriver.kt:438)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)←LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)←LintCliClient.run(LintCliClient.kt:279)←LintCliClient.run$default(LintCliClient.kt:262)←LintCliClient.analyzeOnly(LintCliClient.kt:237)←Main.run(Main.java:1689)←Main.run(Main.java:275)←DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)←Method.invoke(Method.java:580)←AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:98)←AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:87)←AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:62)←DefaultWorkerServer.execute(DefaultWorkerServer.java:63)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)←NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)←ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)←NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)←AbstractWorker$1.call(AbstractWorker.java:44)←AbstractWorker$1.call(AbstractWorker.java:41)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)←DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)←DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)←DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)←DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)←DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:73)←AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)←NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)←DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:170)←FutureTask.run(FutureTask.java:317)←DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:187)←DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:120)←DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:162)←Factories$1.create(Factories.java:31)←DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:264)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:128)←DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:133)←DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:157)←DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:126)←Executors$RunnableAdapter.call(Executors.java:572)←FutureTask.run(FutureTask.java:317)←ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)←AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:47)←ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)←ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)←Thread.run(Thread.java:1583)

You can run with --stacktrace or set environment variable LINT_PRINT_STACKTRACE=true to dump a full stacktrace to stdout. [LintError]
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\fixer\ActivityFixer.java:35: Error: Missing permissions required by WallpaperManager.getDrawable: android.permission.MANAGE_EXTERNAL_STORAGE or android.permission.READ_WALLPAPER_INTERNAL [MissingPermission]
     activity.getWindow().setBackgroundDrawable(WallpaperManager.getInstance(activity).getDrawable());
                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "MissingPermission":
   This check scans through your code and libraries and looks at the APIs
   being used, and checks this against the set of permissions required to
   access those APIs. If the code using those APIs is called at runtime, then
   the program will crash.

   Furthermore, for permissions that are revocable (with targetSdkVersion 23),
   client code must also be prepared to handle the calls throwing an exception
   if the user rejects the request for permission at runtime.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\StubActivity.java:29: Error: Calling super.onCreate more than once can lead to crashes [MissingSuperCall]
   super.onCreate(null);
   ~~~~~

   Explanation for issues of type "MissingSuperCall":
   Some methods, such as View#onDetachedFromWindow, require that you also call
   the super implementation as part of your method.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:77: Warning: READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READ_MEDIA_AUDIO. [ScopedStorage]
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:108: Warning: WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the MediaStore.createWriteRequest intent. [ScopedStorage]
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ScopedStorage":
   Scoped storage is enforced on Android 10+ (or Android 11+ if using
   requestLegacyExternalStorage). In particular, WRITE_EXTERNAL_STORAGE will
   no longer provide write access to all files; it will provide the equivalent
   of READ_EXTERNAL_STORAGE instead.

   As of Android 13, if you need to query or interact with MediaStore or media
   files on the shared storage, you should be using instead one or more new
   storage permissions:
   * android.permission.READ_MEDIA_IMAGES
   * android.permission.READ_MEDIA_VIDEO
   * android.permission.READ_MEDIA_AUDIO

   and then add maxSdkVersion="33" to the older permission. See the developer
   guide for how to do this:
   https://developer.android.com/about/versions/13/behavior-changes-13#granula
   r-media-permissions

   The MANAGE_EXTERNAL_STORAGE permission can be used to manage all files, but
   it is rarely necessary and most apps on Google Play are not allowed to use
   it. Most apps should instead migrate to use scoped storage. To modify or
   delete files, apps should request write access from the user as described
   at https://goo.gle/android-mediastore-createwriterequest.

   To learn more, read these resources: Play policy:
   https://goo.gle/policy-storage-help Allowable use cases:
   https://goo.gle/policy-storage-usecases

   https://goo.gle/android-storage-usecases

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\PackageParserCompat.java:44: Warning: This field should be annotated with ChecksSdkIntAtLeast(extension=0) [AnnotateVersionCheck]
    private static final int API_LEVEL = Build.VERSION.SDK_INT;
                             ~~~~~~~~~

   Explanation for issues of type "AnnotateVersionCheck":
   Methods which perform SDK_INT version checks (or field constants which
   reflect the result of a version check) in libraries should be annotated
   with @ChecksSdkIntAtLeast. This makes it possible for lint to correctly
   check calls into the library later to correctly understand that problematic
   code which is wrapped within a call into this library is safe after all.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\location\MockLocationHelper.java:205: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
        return nema + "*" + String.format("%02X", sum).toLowerCase();
                                                       ~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\utils\Reflect.java:133: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            return string.toLowerCase();
                          ~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\utils\Reflect.java:135: Warning: Implicitly using the default locale is a common source of bugs: Use toLowerCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
            return string.substring(0, 1).toLowerCase() + string.substring(1);
                                          ~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\wifi\WifiManagerStub.java:194: Warning: Implicitly using the default locale is a common source of bugs: Use toUpperCase(Locale) instead. For strings meant to be internal use Locale.ROOT, otherwise Locale.getDefault(). [DefaultLocale]
                        String sAddr = addr.getHostAddress().toUpperCase();
                                                             ~~~~~~~~~~~

   Explanation for issues of type "DefaultLocale":
   Calling String#toLowerCase() or #toUpperCase() without specifying an
   explicit locale is a common source of bugs. The reason for that is that
   those methods will use the current locale on the user's device, and even
   though the code appears to work correctly when you are developing the app,
   it will fail in some locales. For example, in the Turkish locale, the
   uppercase replacement for i is not I.

   If you want the methods to just perform ASCII replacement, for example to
   convert an enum name, call String#toUpperCase(Locale.US) instead. If you
   really want to use the current locale, call
   String#toUpperCase(Locale.getDefault()) instead.

   https://developer.android.com/reference/java/util/Locale.html#default_locale

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\natives\NativeMethods.java:39: Warning: Reflective access to native_setup, which is not part of the public SDK and therefore likely to change in future Android releases [DiscouragedPrivateApi]
            gCameraNativeSetup = Camera.class.getDeclaredMethod("native_setup", Object.class, int.class, String.class);
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedPrivateApi":
   Usage of restricted non-SDK interface may throw an exception at runtime.
   Accessing non-SDK methods or fields through reflection has a high
   likelihood to break your app between versions, and is being restricted to
   facilitate future app compatibility.

   https://developer.android.com/preview/restrictions-non-sdk-interfaces

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\env\DeadServerException.java:25: Warning: Call requires API level 24 (current min is 21): new java.lang.RuntimeException [NewApi]
        super(message, cause, enableSuppression, writableStackTrace);
        ~~~~~

   Explanation for issues of type "NewApi":
   This check scans through all the Android API calls in the application and
   warns about any calls that are not available on all versions targeted by
   this application (according to its minimum SDK attribute in the manifest).

   If you really want to use this API and don't need to support older devices
   just set the minSdkVersion in your build.gradle or AndroidManifest.xml
   files.

   If your code is deliberately accessing newer APIs, and you have ensured
   (e.g. with conditional execution) that this code will only ever be called
   on a supported platform, then you can annotate your class or method with
   the @TargetApi annotation specifying the local minimum SDK to apply, such
   as @TargetApi(11), such that this check considers 11 rather than your
   manifest file's minimum SDK as the required API level.

   If you are deliberately setting android: attributes in style definitions,
   make sure you place this in a values-vNN folder in order to avoid running
   into runtime conflicts on certain devices where manufacturers have added
   custom attributes whose ids conflict with the new ones on later platforms.

   Similarly, you can use tools:targetApi="11" in an XML file to indicate that
   the element will only be inflated in an adequate context.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build.gradle:11: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdk 34
        ~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\HCallbackStub.java:70: Warning: Accessing internal APIs via reflection is not supported and may not work on all devices or in the future [PrivateApi]
                Class<?> activityThreadClass = Class.forName("android.app.ActivityThread");
                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\PackageParserCompat.java:223: Warning: Accessing internal APIs via reflection is not supported and may not work on all devices or in the future [PrivateApi]
                    Class.forName("android.content.pm.PackageParser$Package"), int.class);
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\PackageParserCompat.java:235: Warning: Accessing internal APIs via reflection is not supported and may not work on all devices or in the future [PrivateApi]
                    Class.forName("android.content.pm.PackageParser$Package"), int.class);
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\SystemPropertiesCompat.java:16: Warning: Accessing internal APIs via reflection is not supported and may not work on all devices or in the future [PrivateApi]
            sClass = Class.forName("android.os.SystemProperties");
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "PrivateApi":
   Using reflection to access hidden/private Android APIs is not safe; it will
   often not work on devices from other vendors, and it may suddenly stop
   working (if the API is removed) or crash spectacularly (if the API behavior
   changes, since there are no guarantees for compatibility).

   https://developer.android.com/preview/restrictions-non-sdk-interfaces

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\NativeEngine.java:127: Warning: Do not hardcode "/data/"; use Context.getFilesDir().getPath() instead [SdCardPath]
            String soPath = String.format("/data/data/%s/lib/libva++.so", VirtualCore.get().getHostPkg());
                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SdCardPath":
   Your code should not reference the /sdcard path directly; instead use
   Environment.getExternalStorageDirectory().getPath().

   Similarly, do not reference the /data/data/ path directly; it can vary in
   multi-user scenarios. Instead, use Context.getFilesDir().getPath().

   https://developer.android.com/training/data-storage#filesExternal

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ChooseAccountTypeActivity.java:160: Warning: Avoid passing null as the view root (needed to resolve layout parameters on the inflated layout's root element) [InflateParams]
                convertView = mLayoutInflater.inflate(R.layout.choose_account_row, null);
                                                                                   ~~~~

   Explanation for issues of type "InflateParams":
   When inflating a layout, avoid passing in null as the parent view, since
   otherwise any layout parameters on the root of the inflated layout will be
   ignored.

   https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:21: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:22: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:38: Error: Permission is only granted to system apps [ProtectedPermissions]
        android:name="android.permission.ACCESS_MOCK_LOCATION"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:57: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:94: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:110: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:170: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:171: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.READ_LOGS" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:189: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:193: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.BATTERY_STATS" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:213: Error: Permission is only granted to system apps [ProtectedPermissions]
    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ProtectedPermissions":
   Permissions with the protection level signature, privileged or
   signatureOrSystem are only granted to system apps. If an app is a regular
   non-system app, it will never be able to use these permissions.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:52: Error: Combining ellipsize=marquee and maxLines=1 can lead to crashes. Use singleLine=true instead. [EllipsizeMaxLines]
            android:maxLines="1"
            ~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:60: Error: Combining ellipsize=marquee and maxLines=1 can lead to crashes. Use singleLine=true instead. [EllipsizeMaxLines]
            android:maxLines="1"
            ~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "EllipsizeMaxLines":
   Combining ellipsize and maxLines=1 can lead to crashes on some devices.
   Earlier versions of lint recommended replacing singleLine=true with
   maxLines=1 but that should not be done when using ellipsize.

   https://issuetracker.google.com/issues/36950033

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\remote\PendingResultData.java:85: Warning: Using the default class loader will not work if you are restoring your own classes. Consider using for example readBundle(getClass().getClassLoader()) instead. [ParcelClassLoader]
        this.mResultExtras = in.readBundle();
                                ~~~~~~~~~~~~

   Explanation for issues of type "ParcelClassLoader":
   The documentation for Parcel#readParcelable(ClassLoader) (and its
   variations) says that you can pass in null to pick up the default class
   loader. However, that ClassLoader is a system class loader and is not able
   to find classes in your own application.

   If you are writing your own classes into the Parcel (not just SDK classes
   like String and so on), then you should supply a ClassLoader for your
   application instead; a simple way to obtain one is to just call
   getClass().getClassLoader() from your own class.

   https://developer.android.com/reference/android/os/Parcel.html

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1592: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
                            int resId = resources.getIdentifier(icon.resourceName, "drawable", pkg);
                                                  ~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\RemoteViewsFixer.java:260: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
            int id = sysContext.getResources().getIdentifier(name, "dimen", NotificationCompat.SYSTEM_UI_PKG);
                                               ~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\WidthCompat.java:131: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
        return systemUi.getResources().getIdentifier(name, type, NotificationCompat.SYSTEM_UI_PKG);
                                       ~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ShortcutHandleActivity.java:34: Warning: This intent could be coming from an untrusted source. It is later launched by an unprotected component com.lody.virtual.client.stub.ShortcutHandleActivity. You could either make the component com.lody.virtual.client.stub.ShortcutHandleActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer. [UnsafeIntentLaunch]
                splashIntent = Intent.parseUri(splashUri, 0);
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ShortcutHandleActivity.java:63: The unsafe intent is launched here.

   Explanation for issues of type "UnsafeIntentLaunch":
   Intent that potentially could come from an untrusted source should not be
   launched from an unprotected component without first being sanitized. See
   this support FAQ for details:
   https://support.google.com/faqs/answer/9267555

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:42: Warning: Did you mean android.permission.ACCESS_WIFI_STATE? [SystemPermissionTypo]
    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:56: Warning: Did you mean android.permission.CHANGE_WIFI_STATE? [SystemPermissionTypo]
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:75: Warning: Did you mean android.permission.READ_LOGS? [SystemPermissionTypo]
    <uses-permission android:name="android.permission.READ_CLIPS" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:111: Warning: Did you mean android.permission.WRITE_OBB? [SystemPermissionTypo]
    <uses-permission android:name="android.permission.WRITE_SMS" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:196: Warning: Did you mean android.permission.WRITE_SETTINGS? [SystemPermissionTypo]
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "SystemPermissionTypo":
   This check looks for required permissions that look like well-known system
   permissions or permissions from the Android SDK, but aren't, and may be
   typos.

   Please double check the permission value you have supplied.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\fixer\ActivityFixer.java:43: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\ActivityManagerCompat.java:74: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
  } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\ActivityStack.java:223: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\ActivityStack.java:301: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\ActivityStack.java:414: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\ActivityStack.java:421: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\ActivityStack.java:507: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\app\ActivityThread.java:52: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\alarm\AlarmManagerStub.java:54: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\appops\AppOpsManagerStub.java:22: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.KITKAT)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\appwidget\AppWidgetManagerStub.java:17: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\base\BinderInvocationStub.java:113: Warning: Unnecessary; SDK_INT is always >= 13 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.HONEYCOMB_MR2)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\BinderProvider.java:64: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\bluetooth\BluetoothStub.java:18: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
    public static final String SERVICE_NAME = Build.VERSION.SDK_INT >= 17 ?
                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\bluetooth\BluetoothStub.java:23: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        super(Build.VERSION.SDK_INT >= 17 ? IBluetoothManager.Stub.asInterface : IBluetooth.Stub.asInterface,
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\BundleCompat.java:16: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 18) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\BundleCompat.java:24: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 18) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellIdentityCdma.java:14: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellIdentityGsm.java:14: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellInfoCdma.java:15: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellInfoGsm.java:14: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellSignalStrengthCdma.java:14: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\telephony\CellSignalStrengthGsm.java:14: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ChooseTypeAndAccountActivity.java:354: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\clipboard\ClipBoardStub.java:39: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\ContentProviderCompat.java:18: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
        if (VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\ContentProviderCompat.java:35: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\ContentProviderCompat.java:68: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\fixer\ContextFixer.java:67: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\fixer\ContextFixer.java:70: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\display\DisplayStub.java:16: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\installer\FileBridge.java:26: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\utils\FileUtils.java:36: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\utils\FileUtils.java:55: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\IApplicationThreadCompat.java:29: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\IApplicationThreadCompat.java:32: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\IApplicationThreadCompat.java:42: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\IApplicationThreadCompat.java:61: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\isms\ISmsStub.java:35: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\isms\ISmsStub.java:50: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\input\InputMethodManagerStub.java:17: Warning: Unnecessary; SDK_INT is always >= 16 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\delegate\InstrumentationDelegate.java:247: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\delegate\InstrumentationDelegate.java:263: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\delegate\InstrumentationDelegate.java:275: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\delegate\InstrumentationDelegate.java:312: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\delegate\InstrumentationDelegate.java:340: Warning: Unnecessary; SDK_INT is always >= 18 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR2)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\IntentResolver.java:149: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\core\InvocationStubManager.java:146: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= JELLY_BEAN_MR2) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\core\InvocationStubManager.java:152: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= JELLY_BEAN_MR1) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\core\InvocationStubManager.java:156: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= JELLY_BEAN_MR1) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\core\InvocationStubManager.java:159: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= LOLLIPOP) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\core\InvocationStubManager.java:167: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
   if (Build.VERSION.SDK_INT >= KITKAT) {
       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\app\job\JobInfo.java:15: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\app\job\JobParameters.java:18: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\job\JobServiceStub.java:24: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\location\LocationManagerStub.java:41: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\location\LocationManagerStub.java:47: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\location\LocationManagerStub.java:52: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\location\LocationManagerStub.java:56: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\media\router\MediaRouterServiceStub.java:16: Warning: Unnecessary; SDK_INT is always >= 16 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\mount\MethodProxies.java:57: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\notification\MethodProxies.java:65: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            int tagIndex = (Build.VERSION.SDK_INT >= 18 ? 2 : 1);
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\notification\MethodProxies.java:80: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= 18 && args[1] instanceof String) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:273: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:430: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:874: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1010: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\pm\MethodProxies.java:1154: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\pm\MethodProxies.java:1192: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.KITKAT)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1214: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        private static final int IDX_IIntentReceiver = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1
                                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1218: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        private static final int IDX_RequiredPermission = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1
                                                          ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1221: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        private static final int IDX_IntentFilter = Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1
                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1289: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT > Build.VERSION_CODES.JELLY_BEAN) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\am\MethodProxies.java:1422: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\NativeLibraryHelperCompat.java:24: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
  if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\NativeLibraryHelperCompat.java:41: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\helper\compat\NativeLibraryHelperCompat.java:84: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
 @TargetApi(Build.VERSION_CODES.LOLLIPOP)
 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\natives\NativeMethods.java:25: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT ? "openDexFileNative" : "openDexFile";
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompat.java:45: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV14.java:35: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV14.java:60: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV14.java:70: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV21.java:20: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV21.java:86: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationCompatCompatV21.java:92: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationFixer.java:100: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\NotificationFixer.java:196: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\installer\PackageHelper.java:28: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\installer\PackageInstallerSession.java:38: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\parser\PackageParserEx.java:297: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\parser\PackageParserEx.java:325: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\providers\ProviderHook.java:148: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        int start = Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2 ? 1 : 0;
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\ProviderIntentResolver.java:86: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.KITKAT)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\ProviderIntentResolver.java:115: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.KITKAT)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\RemoteViewsFixer.java:232: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT <= 19) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ResolverActivity.java:115: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 17) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ResolverActivity.java:177: Warning: Unnecessary; SDK_INT is always >= 15 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ResolverActivity.java:319: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
                    if (Build.VERSION.SDK_INT >= 19) {
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\restriction\RestrictionStub.java:15: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\search\SearchManagerStub.java:21: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\media\session\SessionManagerStub.java:15: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\installer\SessionParams.java:17: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\providers\Settings.java:18: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ShortcutHandleActivity.java:50: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\StubJob.java:34: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\content\SyncRequest.java:14: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.KITKAT)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\telephony\TelephonyRegistryStub.java:26: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
    if (android.os.Build.VERSION.SDK_INT >= 17) {
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\user\UserManagerStub.java:19: Warning: Unnecessary; SDK_INT is always >= 17 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.JELLY_BEAN_MR1)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\VActivityManagerService.java:477: Warning: Unnecessary; SDK_INT is never < 21 [ObsoleteSdkInt]
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VAppManagerService.java:298: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:260: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && targetSdkVersion < Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:291: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:303: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:503: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:534: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:568: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && Settings.Global.TYPE != null) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\os\VEnvironment.java:39: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\job\VJobSchedulerService.java:38: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\remote\vloc\VLocation.java:92: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\parser\VPackage.java:291: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.KITKAT) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\installer\VPackageInstallerService.java:44: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:88: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
    private final ProviderIntentResolver mProviders = Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT ? new ProviderIntentResolver() : null;
                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:148: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:196: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:439: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:477: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:530: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:562: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
    @TargetApi(Build.VERSION_CODES.KITKAT)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\pm\VPackageManagerService.java:569: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\env\VirtualRuntime.java:48: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\WidthCompat.java:32: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
            if (Build.VERSION.SDK_INT >= 21) {
                ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\notification\WidthCompat.java:47: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 21)
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\wifi\WifiManagerStub.java:121: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\wifi\WifiManagerStub.java:254: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= 21) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\mirror\android\net\wifi\WifiSsid.java:13: Warning: Unnecessary; SDK_INT is always >= 19 [ObsoleteSdkInt]
@TargetApi(Build.VERSION_CODES.KITKAT)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\hook\proxies\window\WindowManagerStub.java:28: Warning: Unnecessary; SDK_INT is always >= 21 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\AttributeCache.java:36: Warning: Do not place Android context classes in static fields (static reference to AttributeCache which has field mContext pointing to Context); this is a memory leak [StaticFieldLeak]
 private static AttributeCache sInstance = null;
         ~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\server\am\BroadcastSystem.java:54: Warning: Do not place Android context classes in static fields (static reference to BroadcastSystem which has field mContext pointing to Context); this is a memory leak [StaticFieldLeak]
    private static BroadcastSystem gDefault;
            ~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\stub\ResolverActivity.java:734: Warning: This AsyncTask class should be static or leaks might occur (com.lody.virtual.client.stub.ResolverActivity.LoadIconTask) [StaticFieldLeak]
    class LoadIconTask extends AsyncTask<DisplayResolveInfo, Void, DisplayResolveInfo> {
          ~~~~~~~~~~~~

   Explanation for issues of type "StaticFieldLeak":
   A static field will leak contexts.

   Non-static inner classes have an implicit reference to their outer class.
   If that outer class is for example a Fragment or Activity, then this
   reference means that the long-running handler/loader/task will hold a
   reference to the activity which prevents it from getting garbage
   collected.

   Similarly, direct field references to activities and fragments from these
   longer running instances can cause leaks.

   ViewModel classes should never point to Views or non-application Contexts.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:3: Warning: This tag and its children can be replaced by one <TextView/> and a compound drawable [UseCompoundDrawables]
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
 ~~~~~~~~~~~~

   Explanation for issues of type "UseCompoundDrawables":
   A LinearLayout which contains an ImageView and a TextView can be more
   efficiently handled as a compound drawable (a single TextView, using the
   drawableTop, drawableLeft, drawableRight and/or drawableBottom attributes
   to draw one or more images adjacent to the text).

   If the two widgets are offset from each other with margins, this can be
   replaced with a drawablePadding attribute.

   There's a lint quickfix to perform this conversion in the Eclipse plugin.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\java\com\lody\virtual\client\VClientImpl.java:689: Warning: This Handler class should be static or leaks might occur (com.lody.virtual.client.VClientImpl.H) [HandlerLeak]
    private class H extends Handler {
                  ~

   Explanation for issues of type "HandlerLeak":
   Since this Handler is declared as an inner class, it may prevent the outer
   class from being garbage collected. If the Handler is using a Looper or
   MessageQueue for a thread other than the main thread, then there is no
   issue. If the Handler is using the Looper or MessageQueue of the main
   thread, you need to fix your Handler declaration, as follows: Declare the
   Handler as a static class; In the outer class, instantiate a WeakReference
   to the outer class and pass this object to your Handler when you
   instantiate the Handler; Make all references to members of the outer class
   using the WeakReference object.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_type_and_account.xml:24: Warning: Use a layout_height of 0dp instead of wrap_content for better performance [InefficientWeight]
        android:layout_height="wrap_content"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "InefficientWeight":
   When only a single widget in a LinearLayout defines a weight, it is more
   efficient to assign a width/height of 0dp to it since it will absorb all
   the remaining space anyway. With a declared width/height of 0dp it does not
   have to measure its own size first.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:18: Warning: This namespace declaration is redundant [RedundantNamespace]
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:39: Warning: This namespace declaration is redundant [RedundantNamespace]
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RedundantNamespace":
   In Android XML documents, only specify the namespace on the root/document
   element. Namespace declarations elsewhere in the document are typically
   accidental leftovers from copy/pasting XML from other files or
   documentation.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:18: Warning: Unused namespace declaration xmlns:android; already declared on the root element [UnusedNamespace]
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:39: Warning: Unused namespace declaration xmlns:android; already declared on the root element [UnusedNamespace]
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedNamespace":
   Unused namespace declarations take up space and require processing that is
   not necessary

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\app_not_authorized.xml:53: Warning: @android:string/yes actually returns "OK", not "Yes"; use @android:string/ok instead or create a local string resource for Yes [ButtonCase]
            android:text="@android:string/yes"
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_type_and_account.xml:51: Warning: @android:string/no actually returns "Cancel", not "No"; use @android:string/cancel instead or create a local string resource for No [ButtonCase]
            android:text="@android:string/no" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_type_and_account.xml:60: Warning: @android:string/yes actually returns "OK", not "Yes"; use @android:string/ok instead or create a local string resource for Yes [ButtonCase]
            android:text="@android:string/yes" />
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ButtonCase":
   The standard capitalization for OK/Cancel dialogs is "OK" and "Cancel". To
   ensure that your dialogs use the standard strings, you can use the resource
   strings @android:string/ok and @android:string/cancel.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:12: Warning: Missing contentDescription attribute on image [ContentDescription]
   <ImageView android:id="@+id/account_row_icon"
    ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:6: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:20: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:24: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:28: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:32: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:36: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:40: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:44: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:48: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:58: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:62: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:66: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:70: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:74: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:78: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:82: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:86: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:96: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:100: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:104: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:108: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:112: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:116: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:120: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:124: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:134: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:138: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:142: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:146: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:150: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:154: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:158: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification.xml:162: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\custom_notification_lite.xml:6: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:29: Warning: Missing contentDescription attribute on image [ContentDescription]
    <ImageView
     ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\app_not_authorized.xml:35: Warning: Hardcoded string "Change not allowed", should use @string resource [HardcodedText]
        android:text="Change not allowed"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:15: Warning: When you define paddingRight you should probably also define paddingLeft for right-to-left symmetry [RtlSymmetry]
        android:paddingRight="8dip"
        ~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:16: Warning: When you define paddingEnd you should probably also define paddingStart for right-to-left symmetry [RtlSymmetry]
        android:paddingEnd="8dip" />
        ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlSymmetry":
   If you specify padding or margin on the left side of a layout, you should
   probably also specify padding on the right side (and vice versa) for
   right-to-left layout symmetry.

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\choose_account_row.xml:15: Warning: Redundant attribute paddingRight; already defining paddingEnd with targetSdkVersion 34 [RtlHardcoded]
        android:paddingRight="8dip"
        ~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:33: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
        android:layout_marginLeft="8dp"
        ~~~~~~~~~~~~~~~~~~~~~~~~~
D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\res\layout\resolve_list_item.xml:42: Warning: Consider replacing android:layout_marginLeft with android:layout_marginStart="8dp" to better support right-to-left layouts [RtlHardcoded]
                  android:layout_marginLeft="8dp"
                  ~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "RtlHardcoded":
   Using Gravity#LEFT and Gravity#RIGHT can lead to problems when a layout is
   rendered in locales where text flows from right to left. Use Gravity#START
   and Gravity#END instead. Similarly, in XML gravity and layout_gravity
   attributes, use start rather than left.

   For XML attributes such as paddingLeft and layout_marginLeft, use
   paddingStart and layout_marginStart. NOTE: If your minSdkVersion is less
   than 17, you should add both the older left/right attributes as well as the
   new start/end attributes. On older platforms, where RTL is not supported
   and the start/end attributes are unknown and therefore ignored, you need
   the older left/right attributes. There is a separate lint check which
   catches that type of error.

   (Note: For Gravity#LEFT and Gravity#START, you can use these constants even
   when targeting older platforms, because the start bitmask is a superset of
   the left bitmask. Therefore, you can use gravity="start" rather than
   gravity="left|start".)

D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml: Warning: The project references RTL attributes, but does not explicitly enable or disable RTL support with android:supportsRtl in the manifest [RtlEnabled]

   Explanation for issues of type "RtlEnabled":
   To enable right-to-left support, when running on API 17 and higher, you
   must set the android:supportsRtl attribute in the manifest <application>
   element.

   If you have started adding RTL attributes, but have not yet finished the
   migration, you can set the attribute to false to satisfy this lint check.

16 errors, 218 warnings
