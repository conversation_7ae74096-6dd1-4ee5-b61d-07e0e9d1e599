Android NDK: WARNING: Ignoring unknown import directory: :D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni    
Android NDK: WARNING:D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/Android.mk:fb: LOCAL_LDLIBS is always ignored for static libraries    
Android NDK: WARNING:D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\jni\Android.mk:va++: non-system libraries in linker flags: -latomic    
Android NDK:     This is likely to result in incorrect builds. Try using LOCAL_STATIC_LIBRARIES    
Android NDK:     or LOCAL_SHARED_LIBRARIES instead to list the library dependencies of the    
Android NDK:     current module    
[x86] Clean          : android_support [x86]
[x86] Clean          : c++_shared [x86]
[x86] Clean          : c++_static [x86]
[x86] Clean          : c++abi [x86]
[x86] Clean          : fb [x86]
[x86] Clean          : unwind [x86]
[x86] Clean          : va++ [x86]
