1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    xmlns:tools="http://schemas.android.com/tools"
4    package="com.lody.virtual" >
5
6    <uses-sdk android:minSdkVersion="21" />
7
8    <uses-permission android:name="com.huawei.authentication.HW_ACCESS_AUTH_SERVICE" />
8-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:5:5-88
8-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:5:22-85
9    <uses-permission android:name="com.samsung.svoice.sync.READ_DATABASE" />
9-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:7:5-77
9-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:7:22-74
10    <uses-permission android:name="com.samsung.svoice.sync.ACCESS_SERVICE" />
10-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:8:5-78
10-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:8:22-75
11    <uses-permission android:name="com.samsung.svoice.sync.WRITE_DATABASE" />
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:9:5-78
11-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:9:22-75
12    <uses-permission android:name="com.sec.android.app.voicenote.Controller" />
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:10:5-80
12-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:10:22-77
13    <uses-permission android:name="com.sec.android.permission.VOIP_INTERFACE" />
13-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:11:5-81
13-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:11:22-78
14    <uses-permission android:name="com.sec.android.permission.LAUNCH_PERSONAL_PAGE_SERVICE" />
14-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:12:5-95
14-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:12:22-92
15    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_USE_APP_FEATURE_SURVEY" />
15-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:13:5-117
15-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:13:22-114
16    <uses-permission android:name="com.samsung.android.providers.context.permission.READ_RECORD_AUDIO" />
16-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:14:5-106
16-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:14:22-103
17    <uses-permission android:name="com.samsung.android.providers.context.permission.WRITE_RECORD_AUDIO" />
17-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:15:5-107
17-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:15:22-104
18    <uses-permission android:name="com.sec.android.settings.permission.SOFT_RESET" />
18-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:16:5-86
18-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:16:22-83
19    <uses-permission android:name="sec.android.permission.READ_MSG_PREF" />
19-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:17:5-76
19-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:17:22-73
20    <uses-permission android:name="com.samsung.android.scloud.backup.lib.read" />
20-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:18:5-82
20-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:18:22-79
21    <uses-permission android:name="com.samsung.android.scloud.backup.lib.write" />
21-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:19:5-83
21-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:19:22-80
22    <uses-permission android:name="android.permission.BIND_DIRECTORY_SEARCH" />
22-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:21:5-80
22-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:21:22-77
23    <uses-permission android:name="android.permission.UPDATE_APP_OPS_STATS" />
23-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:22:5-79
23-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:22:22-76
24    <uses-permission android:name="com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL" />
24-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:23:5-97
24-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:23:22-94
25    <uses-permission
25-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:25:5-27:47
26        android:name="android.permission.ACCOUNT_MANAGER"
26-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:26:9-58
27        tools:ignore="ProtectedPermissions" />
27-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:27:9-44
28    <uses-permission
28-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:29:5-31:47
29        android:name="android.permission.PACKAGE_USAGE_STATS"
29-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:30:9-62
30        tools:ignore="ProtectedPermissions" />
30-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:31:9-44
31    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
31-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:32:5-74
31-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:32:22-71
32    <uses-permission android:name="android.permission.INTERNET" />
32-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:33:5-67
32-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:33:22-64
33    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
33-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:34:5-81
33-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:34:22-78
34    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
34-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:35:5-79
34-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:35:22-76
35    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
35-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:36:5-89
35-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:36:22-86
36    <uses-permission
36-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:37:5-39:39
37        android:name="android.permission.ACCESS_MOCK_LOCATION"
37-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:38:9-63
38        tools:ignore="MockLocation" />
38-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:39:9-36
39    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
39-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:40:5-79
39-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:40:22-76
40    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
40-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:41:5-76
40-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:41:22-73
41    <uses-permission android:name="android.permission.ACCESS_WIMAX_STATE" />
41-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:42:5-77
41-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:42:22-74
42    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
42-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:43:5-80
42-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:43:22-77
43    <uses-permission
43-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:44:5-46:47
44        android:name="android.permission.BIND_APPWIDGET"
44-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:45:9-57
45        tools:ignore="ProtectedPermissions" />
45-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:46:9-44
46    <uses-permission android:name="android.permission.BLUETOOTH" />
46-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:47:5-68
46-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:47:22-65
47    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
47-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:48:5-74
47-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:48:22-71
48    <uses-permission android:name="android.permission.BODY_SENSORS" />
48-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:49:5-71
48-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:49:22-68
49    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
49-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:50:5-75
49-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:50:22-72
50    <uses-permission android:name="android.permission.CALL_PHONE" />
50-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:51:5-69
50-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:51:22-66
51    <uses-permission android:name="android.permission.CAMERA" />
51-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:52:5-65
51-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:52:22-62
52    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
52-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:53:5-79
52-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:53:22-76
53    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
53-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:54:5-86
53-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:54:22-83
54    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
54-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:55:5-76
54-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:55:22-73
55    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
55-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:56:5-77
55-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:56:22-74
56    <uses-permission android:name="android.permission.CLEAR_APP_CACHE" />
56-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:57:5-74
56-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:57:22-71
57    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
57-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:58:5-75
57-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:58:22-72
58    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
58-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:59:5-88
58-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:59:22-85
59    <uses-permission android:name="android.permission.EXPAND_STATUS_BAR" />
59-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:60:5-76
59-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:60:22-73
60    <uses-permission android:name="android.permission.FLASHLIGHT" />
60-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:61:5-69
60-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:61:22-66
61    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
61-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:62:5-71
61-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:62:22-68
62    <uses-permission android:name="android.permission.GET_CLIPS" />
62-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:63:5-68
62-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:63:22-65
63    <uses-permission android:name="android.permission.GET_PACKAGE_SIZE" />
63-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:64:5-75
63-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:64:22-72
64    <uses-permission android:name="android.permission.GET_TASKS" />
64-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:65:5-68
64-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:65:22-65
65    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
65-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:66:5-84
65-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:66:22-81
66    <uses-permission android:name="android.permission.MANAGE_ACCOUNTS" />
66-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:67:5-74
66-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:67:22-71
67    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
67-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:68:5-80
67-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:68:22-77
68    <uses-permission android:name="android.permission.NFC" />
68-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:69:5-62
68-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:69:22-59
69    <uses-permission android:name="android.permission.PERSISTENT_ACTIVITY" />
69-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:70:5-78
69-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:70:22-75
70    <uses-permission android:name="android.permission.PROCESS_OUTGOING_CALLS" />
70-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:71:5-81
70-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:71:22-78
71    <uses-permission android:name="android.permission.READ_CALENDAR" />
71-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:72:5-72
71-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:72:22-69
72    <uses-permission android:name="android.permission.READ_CALL_LOG" />
72-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:73:5-72
72-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:73:22-69
73    <uses-permission android:name="android.permission.READ_CELL_BROADCASTS" />
73-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:74:5-79
73-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:74:22-76
74    <uses-permission android:name="android.permission.READ_CLIPS" />
74-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:75:5-69
74-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:75:22-66
75    <uses-permission android:name="android.permission.READ_CONTACTS" />
75-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:76:5-72
75-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:76:22-69
76    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
76-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:77:5-80
76-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:77:22-77
77    <uses-permission android:name="android.permission.READ_INSTALL_SESSIONS" />
77-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:78:5-80
77-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:78:22-77
78    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
78-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:79:5-75
78-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:79:22-72
79    <uses-permission android:name="android.permission.READ_PROFILE" />
79-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:80:5-71
79-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:80:22-68
80    <uses-permission android:name="android.permission.READ_SMS" />
80-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:81:5-67
80-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:81:22-64
81    <uses-permission android:name="android.permission.READ_SOCIAL_STREAM" />
81-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:82:5-77
81-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:82:22-74
82    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
82-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:83:5-77
82-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:83:22-74
83    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
83-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:84:5-74
83-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:84:22-71
84    <uses-permission android:name="android.permission.READ_USER_DICTIONARY" />
84-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:85:5-79
84-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:85:22-76
85    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
85-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:86:5-81
85-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:86:22-78
86    <uses-permission android:name="android.permission.RECEIVE_MMS" />
86-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:87:5-70
86-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:87:22-67
87    <uses-permission android:name="android.permission.RECEIVE_SMS" />
87-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:88:5-70
87-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:88:22-67
88    <uses-permission android:name="android.permission.RECEIVE_WAP_PUSH" />
88-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:89:5-75
88-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:89:22-72
89    <uses-permission android:name="android.permission.RECORD_AUDIO" />
89-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:90:5-71
89-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:90:22-68
90    <uses-permission android:name="android.permission.REORDER_TASKS" />
90-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:91:5-72
90-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:91:22-69
91    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
91-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:92:5-75
91-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:92:22-72
92    <uses-permission android:name="android.permission.SEND_SMS" />
92-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:93:5-67
92-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:93:22-64
93    <uses-permission android:name="android.permission.SET_TIME_ZONE" />
93-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:94:5-72
93-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:94:22-69
94    <uses-permission android:name="android.permission.SET_WALLPAPER" />
94-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:95:5-72
94-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:95:22-69
95    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
95-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:96:5-78
95-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:96:22-75
96    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_READ" />
96-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:97:5-80
96-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:97:22-77
97    <uses-permission android:name="android.permission.SUBSCRIBED_FEEDS_WRITE" />
97-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:98:5-81
97-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:98:22-78
98    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
98-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:99:5-78
98-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:99:22-75
99    <uses-permission android:name="android.permission.TRANSMIT_IR" />
99-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:100:5-70
99-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:100:22-67
100    <uses-permission android:name="android.permission.USE_SIP" />
100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:101:5-66
100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:101:22-63
101    <uses-permission android:name="android.permission.VIBRATE" />
101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:102:5-66
101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:102:22-63
102    <uses-permission android:name="android.permission.WAKE_LOCK" />
102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:103:5-68
102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:103:22-65
103    <uses-permission android:name="android.permission.WRITE_CALENDAR" />
103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:104:5-73
103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:104:22-70
104    <uses-permission android:name="android.permission.WRITE_CALL_LOG" />
104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:105:5-73
104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:105:22-70
105    <uses-permission android:name="android.permission.WRITE_CLIPS" />
105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:106:5-70
105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:106:22-67
106    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:107:5-73
106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:107:22-70
107    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:108:5-81
107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:108:22-78
108    <uses-permission android:name="android.permission.WRITE_PROFILE" />
108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:109:5-72
108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:109:22-69
109    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:110:5-73
109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:110:22-70
110    <uses-permission android:name="android.permission.WRITE_SMS" />
110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:111:5-68
110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:111:22-65
111    <uses-permission android:name="android.permission.WRITE_SOCIAL_STREAM" />
111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:112:5-78
111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:112:22-75
112    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:113:5-78
112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:113:22-75
113    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY" />
113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:114:5-80
113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:114:22-77
114    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:115:5-74
114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:115:22-71
115    <uses-permission android:name="com.android.alarm.permission.SET_ALARM" />
115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:116:5-78
115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:116:22-75
116    <uses-permission android:name="com.android.browser.permission.READ_HISTORY_BOOKMARKS" />
116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:117:5-93
116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:117:22-90
117    <uses-permission android:name="com.android.browser.permission.WRITE_HISTORY_BOOKMARKS" />
117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:118:5-94
117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:118:22-91
118    <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" />
118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:119:5-88
118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:119:22-85
119    <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" />
119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:120:5-90
119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:120:22-87
120    <uses-permission android:name="com.android.vending.BILLING" />
120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:121:5-67
120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:121:22-64
121    <uses-permission android:name="com.android.vending.CHECK_LICENSE" />
121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:122:5-73
121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:122:22-70
122    <uses-permission android:name="com.android.voicemail.permission.ADD_VOICEMAIL" />
122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:123:5-86
122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:123:22-83
123    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:124:5-82
123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:124:22-79
124    <uses-permission android:name="com.google.android.gms.permission.ACTIVITY_RECOGNITION" />
124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:125:5-94
124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:125:22-91
125    <uses-permission android:name="com.google.android.gms.permission.AD_ID_NOTIFICATION" />
125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:126:5-92
125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:126:22-89
126    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:127:5-92
126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:127:22-89
127    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.OTHER_SERVICES" />
127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:128:5-107
127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:128:22-104
128    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.YouTubeUser" />
128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:129:5-104
128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:129:22-101
129    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adsense" />
129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:130:5-100
129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:130:22-97
130    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.adwords" />
130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:131:5-100
130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:131:22-97
131    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ah" />
131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:132:5-95
131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:132:22-92
132    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.android" />
132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:133:5-100
132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:133:22-97
133    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.androidsecure" />
133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:134:5-106
133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:134:22-103
134    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.blogger" />
134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:135:5-100
134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:135:22-97
135    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cl" />
135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:136:5-95
135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:136:22-92
136    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.cp" />
136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:137:5-95
136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:137:22-92
137    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.dodgeball" />
137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:138:5-102
137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:138:22-99
138    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.finance" />
138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:139:5-100
138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:139:22-97
139    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.gbase" />
139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:140:5-98
139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:140:22-95
140    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.grandcentral" />
140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:141:5-105
140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:141:22-102
141    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.groups2" />
141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:142:5-100
141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:142:22-97
142    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.health" />
142-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:143:5-99
142-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:143:22-96
143    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.ig" />
143-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:144:5-95
143-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:144:22-92
144    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.jotspot" />
144-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:145:5-100
144-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:145:22-97
145    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.knol" />
145-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:146:5-97
145-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:146:22-94
146    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.lh2" />
146-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:147:5-96
146-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:147:22-93
147    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.local" />
147-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:148:5-98
147-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:148:22-95
148    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mail" />
148-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:149:5-97
148-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:149:22-94
149    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.mobile" />
149-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:150:5-99
149-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:150:22-96
150    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.news" />
150-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:151:5-97
150-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:151:22-94
151    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.notebook" />
151-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:152:5-101
151-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:152:22-98
152    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.orkut" />
152-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:153:5-98
152-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:153:22-95
153    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.print" />
153-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:154:5-98
153-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:154:22-95
154    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierra" />
154-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:155:5-99
154-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:155:22-96
155    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierraqa" />
155-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:156:5-101
155-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:156:22-98
156    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sierrasandbox" />
156-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:157:5-106
156-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:157:22-103
157    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.sitemaps" />
157-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:158:5-101
157-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:158:22-98
158    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speech" />
158-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:159:5-99
158-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:159:22-96
159    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.speechpersonalization" />
159-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:160:5-114
159-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:160:22-111
160    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.talk" />
160-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:161:5-97
160-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:161:22-94
161    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wifi" />
161-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:162:5-97
161-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:162:22-94
162    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.wise" />
162-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:163:5-97
162-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:163:22-94
163    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.writely" />
163-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:164:5-100
163-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:164:22-97
164    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH.youtube" />
164-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:165:5-100
164-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:165:22-97
165    <uses-permission android:name="com.google.android.launcher.permission.READ_SETTINGS" />
165-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:166:5-92
165-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:166:22-89
166    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
166-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:167:5-98
166-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:167:22-95
167    <uses-permission android:name="com.google.android.providers.talk.permission.READ_ONLY" />
167-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:168:5-94
167-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:168:22-91
168    <uses-permission android:name="com.google.android.providers.talk.permission.WRITE_ONLY" />
168-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:169:5-95
168-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:169:22-92
169    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
169-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:170:5-84
169-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:170:22-81
170    <uses-permission android:name="android.permission.READ_LOGS" />
170-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:171:5-68
170-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:171:22-65
171    <uses-permission
171-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:172:5-174:47
172        android:name="android.permission.INSTALL_PACKAGES"
172-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:173:9-59
173        tools:ignore="ProtectedPermissions" />
173-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:174:9-44
174    <uses-permission
174-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:175:5-177:47
175        android:name="android.permission.DELETE_PACKAGES"
175-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:176:9-58
176        tools:ignore="ProtectedPermissions" />
176-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:177:9-44
177    <uses-permission
177-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:178:5-180:47
178        android:name="android.permission.CLEAR_APP_USER_DATA"
178-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:179:9-62
179        tools:ignore="ProtectedPermissions" />
179-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:180:9-44
180    <uses-permission
180-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:181:5-183:47
181        android:name="android.permission.WRITE_MEDIA_STORAGE"
181-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:182:9-62
182        tools:ignore="ProtectedPermissions" />
182-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:183:9-44
183    <uses-permission
183-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:184:5-186:47
184        android:name="android.permission.ACCESS_CACHE_FILESYSTEM"
184-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:185:9-66
185        tools:ignore="ProtectedPermissions" />
185-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:186:9-44
186    <uses-permission android:name="android.permission.READ_OWNER_DATA" />
186-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:187:5-74
186-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:187:22-71
187    <uses-permission android:name="android.permission.WRITE_OWNER_DATA" />
187-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:188:5-75
187-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:188:22-72
188    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
188-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:189:5-79
188-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:189:22-76
189    <uses-permission
189-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:190:5-192:47
190        android:name="android.permission.DEVICE_POWER"
190-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:191:9-55
191        tools:ignore="ProtectedPermissions" />
191-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:192:9-44
192    <uses-permission android:name="android.permission.BATTERY_STATS" />
192-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:193:5-72
192-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:193:22-69
193    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
193-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:194:5-82
193-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:194:22-79
194    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
194-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:195:5-85
194-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:195:22-82
195    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
195-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:196:5-86
195-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:196:22-83
196    <uses-permission android:name="com.android.launcher3.permission.READ_SETTINGS" />
196-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:197:5-86
196-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:197:22-83
197    <uses-permission android:name="com.android.launcher2.permission.READ_SETTINGS" />
197-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:198:5-86
197-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:198:22-83
198    <uses-permission android:name="com.teslacoilsw.launcher.permission.READ_SETTINGS" />
198-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:199:5-89
198-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:199:22-86
199    <uses-permission android:name="com.actionlauncher.playstore.permission.READ_SETTINGS" />
199-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:200:5-93
199-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:200:22-90
200    <uses-permission android:name="com.mx.launcher.permission.READ_SETTINGS" />
200-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:201:5-80
200-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:201:22-77
201    <uses-permission android:name="com.anddoes.launcher.permission.READ_SETTINGS" />
201-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:202:5-85
201-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:202:22-82
202    <uses-permission android:name="com.apusapps.launcher.permission.READ_SETTINGS" />
202-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:203:5-86
202-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:203:22-83
203    <uses-permission android:name="com.tsf.shell.permission.READ_SETTINGS" />
203-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:204:5-78
203-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:204:22-75
204    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
204-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:205:5-81
204-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:205:22-78
205    <uses-permission android:name="com.lenovo.launcher.permission.READ_SETTINGS" />
205-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:206:5-84
205-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:206:22-81
206    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
206-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:207:5-82
206-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:207:22-79
207    <uses-permission android:name="com.bbk.launcher2.permission.READ_SETTINGS" />
207-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:208:5-82
207-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:208:22-79
208    <uses-permission android:name="com.s.launcher.permission.READ_SETTINGS" />
208-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:209:5-79
208-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:209:22-76
209    <uses-permission android:name="cn.nubia.launcher.permission.READ_SETTINGS" />
209-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:210:5-82
209-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:210:22-79
210    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
210-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:211:5-92
210-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:211:22-89
211    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:212:5-91
211-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:212:22-88
212    <uses-permission android:name="android.permission.GET_INTENT_SENDER_INTENT" />
212-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:213:5-83
212-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:213:22-80
213
214    <!-- Required for Android 11+ to query other apps -->
215    <uses-permission
215-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:216:5-217:53
216        android:name="android.permission.QUERY_ALL_PACKAGES"
216-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:216:22-74
217        tools:ignore="QueryAllPackagesPermission" />
217-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:217:9-50
218    <uses-permission
218-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:219:5-221:47
219        android:name="android.permission.WRITE_APN_SETTINGS"
219-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:220:9-61
220        tools:ignore="ProtectedPermissions" />
220-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:221:9-44
221
222    <uses-feature android:name="android.hardware.camera" />
222-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:223:5-60
222-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:223:19-57
223    <uses-feature
223-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:224:5-226:36
224        android:name="android.hardware.camera.autofocus"
224-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:225:9-57
225        android:required="false" />
225-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:226:9-33
226
227    <application>
227-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:228:5-1302:19
228        <service
228-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:229:9-231:61
229            android:name="com.lody.virtual.client.stub.DaemonService"
229-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:230:13-70
230            android:process="@string/engine_process_name" />
230-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:231:13-58
231        <service
231-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:233:9-235:61
232            android:name="com.lody.virtual.client.stub.DaemonService$InnerService"
232-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:234:13-83
233            android:process="@string/engine_process_name" />
233-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:235:13-58
234
235        <activity
235-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:237:9-241:75
236            android:name="com.lody.virtual.client.stub.ShortcutHandleActivity"
236-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:238:13-79
237            android:exported="true"
237-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:239:13-36
238            android:process="@string/engine_process_name"
238-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:240:13-58
239            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
239-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:241:13-72
240        <activity
240-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:243:9-245:61
241            android:name="com.lody.virtual.client.stub.StubPendingActivity"
241-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:244:13-60
242            android:process="@string/engine_process_name" />
242-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:245:13-58
243
244        <service
244-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:247:9-249:61
245            android:name="com.lody.virtual.client.stub.StubPendingService"
245-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:248:13-59
246            android:process="@string/engine_process_name" />
246-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:249:13-58
247
248        <receiver
248-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:250:9-252:61
249            android:name="com.lody.virtual.client.stub.StubPendingReceiver"
249-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:251:13-60
250            android:process="@string/engine_process_name" />
250-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:252:13-58
251
252        <service
252-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:254:9-257:61
253            android:name="com.lody.virtual.client.stub.StubJob"
253-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:255:13-48
254            android:permission="android.permission.BIND_JOB_SERVICE"
254-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:256:13-69
255            android:process="@string/engine_process_name" />
255-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:257:13-58
256
257        <activity
257-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:259:9-265:52
258            android:name="com.lody.virtual.client.stub.ChooseAccountTypeActivity"
258-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:260:13-66
259            android:configChanges="keyboard|keyboardHidden|orientation"
259-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:261:13-72
260            android:excludeFromRecents="true"
260-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:262:13-46
261            android:exported="false"
261-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:263:13-37
262            android:process="@string/engine_process_name"
262-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:264:13-58
263            android:screenOrientation="portrait" />
263-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:265:13-49
264        <activity
264-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:267:9-273:52
265            android:name="com.lody.virtual.client.stub.ChooseTypeAndAccountActivity"
265-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:268:13-69
266            android:configChanges="keyboard|keyboardHidden|orientation"
266-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:269:13-72
267            android:excludeFromRecents="true"
267-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:270:13-46
268            android:exported="false"
268-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:271:13-37
269            android:process="@string/engine_process_name"
269-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:272:13-58
270            android:screenOrientation="portrait" />
270-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:273:13-49
271        <activity
271-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:275:9-283:51
272            android:name="com.lody.virtual.client.stub.ChooserActivity"
272-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:276:13-56
273            android:configChanges="keyboard|keyboardHidden|orientation"
273-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:277:13-72
274            android:excludeFromRecents="true"
274-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:278:13-46
275            android:exported="true"
275-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:279:13-36
276            android:finishOnCloseSystemDialogs="true"
276-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:280:13-54
277            android:process="@string/engine_process_name"
277-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:281:13-58
278            android:screenOrientation="portrait"
278-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:282:13-49
279            android:theme="@style/VAAlertTheme" />
279-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:283:13-48
280        <activity
280-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:285:9-293:51
281            android:name="com.lody.virtual.client.stub.ResolverActivity"
281-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:286:13-57
282            android:configChanges="keyboard|keyboardHidden|orientation"
282-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:287:13-72
283            android:excludeFromRecents="true"
283-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:288:13-46
284            android:exported="true"
284-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:289:13-36
285            android:finishOnCloseSystemDialogs="true"
285-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:290:13-54
286            android:process="@string/engine_process_name"
286-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:291:13-58
287            android:screenOrientation="portrait"
287-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:292:13-49
288            android:theme="@style/VAAlertTheme" />
288-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:293:13-48
289
290        <provider
290-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:295:9-299:61
291            android:name="com.lody.virtual.server.BinderProvider"
291-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:296:13-66
292            android:authorities="${applicationId}.virtual.service.BinderProvider"
292-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:297:13-82
293            android:exported="false"
293-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:298:13-37
294            android:process="@string/engine_process_name" />
294-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:299:13-58
295
296        <activity
296-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:301:9-306:46
297            android:name="com.lody.virtual.client.stub.StubActivity$C0"
297-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:302:13-72
298            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
298-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:303:13-170
299            android:process=":p0"
299-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:304:13-34
300            android:taskAffinity="com.lody.virtual.vt"
300-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:305:13-55
301            android:theme="@style/VATheme" />
301-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:306:13-43
302        <activity
302-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:308:9-313:46
303            android:name="com.lody.virtual.client.stub.StubActivity$C1"
303-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:309:13-72
304            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
304-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:310:13-170
305            android:process=":p1"
305-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:311:13-34
306            android:taskAffinity="com.lody.virtual.vt"
306-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:312:13-55
307            android:theme="@style/VATheme" />
307-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:313:13-43
308        <activity
308-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:315:9-320:46
309            android:name="com.lody.virtual.client.stub.StubActivity$C2"
309-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:316:13-72
310            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
310-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:317:13-170
311            android:process=":p2"
311-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:318:13-34
312            android:taskAffinity="com.lody.virtual.vt"
312-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:319:13-55
313            android:theme="@style/VATheme" />
313-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:320:13-43
314        <activity
314-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:322:9-327:46
315            android:name="com.lody.virtual.client.stub.StubActivity$C3"
315-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:323:13-72
316            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
316-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:324:13-170
317            android:process=":p3"
317-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:325:13-34
318            android:taskAffinity="com.lody.virtual.vt"
318-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:326:13-55
319            android:theme="@style/VATheme" />
319-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:327:13-43
320        <activity
320-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:329:9-334:46
321            android:name="com.lody.virtual.client.stub.StubActivity$C4"
321-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:330:13-72
322            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
322-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:331:13-170
323            android:process=":p4"
323-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:332:13-34
324            android:taskAffinity="com.lody.virtual.vt"
324-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:333:13-55
325            android:theme="@style/VATheme" />
325-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:334:13-43
326        <activity
326-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:336:9-341:46
327            android:name="com.lody.virtual.client.stub.StubActivity$C5"
327-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:337:13-72
328            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
328-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:338:13-170
329            android:process=":p5"
329-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:339:13-34
330            android:taskAffinity="com.lody.virtual.vt"
330-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:340:13-55
331            android:theme="@style/VATheme" />
331-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:341:13-43
332        <activity
332-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:343:9-348:46
333            android:name="com.lody.virtual.client.stub.StubActivity$C6"
333-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:344:13-72
334            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
334-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:345:13-170
335            android:process=":p6"
335-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:346:13-34
336            android:taskAffinity="com.lody.virtual.vt"
336-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:347:13-55
337            android:theme="@style/VATheme" />
337-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:348:13-43
338        <activity
338-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:350:9-355:46
339            android:name="com.lody.virtual.client.stub.StubActivity$C7"
339-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:351:13-72
340            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
340-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:352:13-170
341            android:process=":p7"
341-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:353:13-34
342            android:taskAffinity="com.lody.virtual.vt"
342-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:354:13-55
343            android:theme="@style/VATheme" />
343-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:355:13-43
344        <activity
344-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:357:9-362:46
345            android:name="com.lody.virtual.client.stub.StubActivity$C8"
345-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:358:13-72
346            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
346-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:359:13-170
347            android:process=":p8"
347-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:360:13-34
348            android:taskAffinity="com.lody.virtual.vt"
348-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:361:13-55
349            android:theme="@style/VATheme" />
349-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:362:13-43
350        <activity
350-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:364:9-369:46
351            android:name="com.lody.virtual.client.stub.StubActivity$C9"
351-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:365:13-72
352            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
352-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:366:13-170
353            android:process=":p9"
353-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:367:13-34
354            android:taskAffinity="com.lody.virtual.vt"
354-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:368:13-55
355            android:theme="@style/VATheme" />
355-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:369:13-43
356        <activity
356-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:371:9-376:46
357            android:name="com.lody.virtual.client.stub.StubActivity$C10"
357-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:372:13-73
358            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
358-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:373:13-170
359            android:process=":p10"
359-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:374:13-35
360            android:taskAffinity="com.lody.virtual.vt"
360-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:375:13-55
361            android:theme="@style/VATheme" />
361-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:376:13-43
362        <activity
362-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:378:9-383:46
363            android:name="com.lody.virtual.client.stub.StubActivity$C11"
363-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:379:13-73
364            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
364-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:380:13-170
365            android:process=":p11"
365-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:381:13-35
366            android:taskAffinity="com.lody.virtual.vt"
366-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:382:13-55
367            android:theme="@style/VATheme" />
367-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:383:13-43
368        <activity
368-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:385:9-390:46
369            android:name="com.lody.virtual.client.stub.StubActivity$C12"
369-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:386:13-73
370            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
370-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:387:13-170
371            android:process=":p12"
371-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:388:13-35
372            android:taskAffinity="com.lody.virtual.vt"
372-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:389:13-55
373            android:theme="@style/VATheme" />
373-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:390:13-43
374        <activity
374-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:392:9-397:46
375            android:name="com.lody.virtual.client.stub.StubActivity$C13"
375-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:393:13-73
376            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
376-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:394:13-170
377            android:process=":p13"
377-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:395:13-35
378            android:taskAffinity="com.lody.virtual.vt"
378-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:396:13-55
379            android:theme="@style/VATheme" />
379-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:397:13-43
380        <activity
380-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:399:9-404:46
381            android:name="com.lody.virtual.client.stub.StubActivity$C14"
381-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:400:13-73
382            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
382-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:401:13-170
383            android:process=":p14"
383-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:402:13-35
384            android:taskAffinity="com.lody.virtual.vt"
384-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:403:13-55
385            android:theme="@style/VATheme" />
385-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:404:13-43
386        <activity
386-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:406:9-411:46
387            android:name="com.lody.virtual.client.stub.StubActivity$C15"
387-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:407:13-73
388            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
388-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:408:13-170
389            android:process=":p15"
389-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:409:13-35
390            android:taskAffinity="com.lody.virtual.vt"
390-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:410:13-55
391            android:theme="@style/VATheme" />
391-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:411:13-43
392        <activity
392-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:413:9-418:46
393            android:name="com.lody.virtual.client.stub.StubActivity$C16"
393-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:414:13-73
394            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
394-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:415:13-170
395            android:process=":p16"
395-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:416:13-35
396            android:taskAffinity="com.lody.virtual.vt"
396-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:417:13-55
397            android:theme="@style/VATheme" />
397-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:418:13-43
398        <activity
398-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:420:9-425:46
399            android:name="com.lody.virtual.client.stub.StubActivity$C17"
399-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:421:13-73
400            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
400-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:422:13-170
401            android:process=":p17"
401-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:423:13-35
402            android:taskAffinity="com.lody.virtual.vt"
402-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:424:13-55
403            android:theme="@style/VATheme" />
403-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:425:13-43
404        <activity
404-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:427:9-432:46
405            android:name="com.lody.virtual.client.stub.StubActivity$C18"
405-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:428:13-73
406            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
406-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:429:13-170
407            android:process=":p18"
407-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:430:13-35
408            android:taskAffinity="com.lody.virtual.vt"
408-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:431:13-55
409            android:theme="@style/VATheme" />
409-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:432:13-43
410        <activity
410-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:434:9-439:46
411            android:name="com.lody.virtual.client.stub.StubActivity$C19"
411-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:435:13-73
412            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
412-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:436:13-170
413            android:process=":p19"
413-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:437:13-35
414            android:taskAffinity="com.lody.virtual.vt"
414-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:438:13-55
415            android:theme="@style/VATheme" />
415-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:439:13-43
416        <activity
416-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:441:9-446:46
417            android:name="com.lody.virtual.client.stub.StubActivity$C20"
417-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:442:13-73
418            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
418-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:443:13-170
419            android:process=":p20"
419-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:444:13-35
420            android:taskAffinity="com.lody.virtual.vt"
420-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:445:13-55
421            android:theme="@style/VATheme" />
421-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:446:13-43
422        <activity
422-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:448:9-453:46
423            android:name="com.lody.virtual.client.stub.StubActivity$C21"
423-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:449:13-73
424            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
424-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:450:13-170
425            android:process=":p21"
425-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:451:13-35
426            android:taskAffinity="com.lody.virtual.vt"
426-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:452:13-55
427            android:theme="@style/VATheme" />
427-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:453:13-43
428        <activity
428-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:455:9-460:46
429            android:name="com.lody.virtual.client.stub.StubActivity$C22"
429-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:456:13-73
430            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
430-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:457:13-170
431            android:process=":p22"
431-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:458:13-35
432            android:taskAffinity="com.lody.virtual.vt"
432-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:459:13-55
433            android:theme="@style/VATheme" />
433-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:460:13-43
434        <activity
434-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:462:9-467:46
435            android:name="com.lody.virtual.client.stub.StubActivity$C23"
435-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:463:13-73
436            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
436-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:464:13-170
437            android:process=":p23"
437-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:465:13-35
438            android:taskAffinity="com.lody.virtual.vt"
438-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:466:13-55
439            android:theme="@style/VATheme" />
439-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:467:13-43
440        <activity
440-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:469:9-474:46
441            android:name="com.lody.virtual.client.stub.StubActivity$C24"
441-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:470:13-73
442            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
442-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:471:13-170
443            android:process=":p24"
443-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:472:13-35
444            android:taskAffinity="com.lody.virtual.vt"
444-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:473:13-55
445            android:theme="@style/VATheme" />
445-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:474:13-43
446        <activity
446-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:476:9-481:46
447            android:name="com.lody.virtual.client.stub.StubActivity$C25"
447-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:477:13-73
448            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
448-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:478:13-170
449            android:process=":p25"
449-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:479:13-35
450            android:taskAffinity="com.lody.virtual.vt"
450-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:480:13-55
451            android:theme="@style/VATheme" />
451-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:481:13-43
452        <activity
452-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:483:9-488:46
453            android:name="com.lody.virtual.client.stub.StubActivity$C26"
453-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:484:13-73
454            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
454-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:485:13-170
455            android:process=":p26"
455-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:486:13-35
456            android:taskAffinity="com.lody.virtual.vt"
456-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:487:13-55
457            android:theme="@style/VATheme" />
457-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:488:13-43
458        <activity
458-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:490:9-495:46
459            android:name="com.lody.virtual.client.stub.StubActivity$C27"
459-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:491:13-73
460            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
460-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:492:13-170
461            android:process=":p27"
461-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:493:13-35
462            android:taskAffinity="com.lody.virtual.vt"
462-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:494:13-55
463            android:theme="@style/VATheme" />
463-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:495:13-43
464        <activity
464-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:497:9-502:46
465            android:name="com.lody.virtual.client.stub.StubActivity$C28"
465-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:498:13-73
466            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
466-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:499:13-170
467            android:process=":p28"
467-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:500:13-35
468            android:taskAffinity="com.lody.virtual.vt"
468-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:501:13-55
469            android:theme="@style/VATheme" />
469-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:502:13-43
470        <activity
470-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:504:9-509:46
471            android:name="com.lody.virtual.client.stub.StubActivity$C29"
471-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:505:13-73
472            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
472-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:506:13-170
473            android:process=":p29"
473-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:507:13-35
474            android:taskAffinity="com.lody.virtual.vt"
474-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:508:13-55
475            android:theme="@style/VATheme" />
475-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:509:13-43
476        <activity
476-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:511:9-516:46
477            android:name="com.lody.virtual.client.stub.StubActivity$C30"
477-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:512:13-73
478            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
478-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:513:13-170
479            android:process=":p30"
479-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:514:13-35
480            android:taskAffinity="com.lody.virtual.vt"
480-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:515:13-55
481            android:theme="@style/VATheme" />
481-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:516:13-43
482        <activity
482-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:518:9-523:46
483            android:name="com.lody.virtual.client.stub.StubActivity$C31"
483-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:519:13-73
484            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
484-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:520:13-170
485            android:process=":p31"
485-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:521:13-35
486            android:taskAffinity="com.lody.virtual.vt"
486-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:522:13-55
487            android:theme="@style/VATheme" />
487-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:523:13-43
488        <activity
488-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:525:9-530:46
489            android:name="com.lody.virtual.client.stub.StubActivity$C32"
489-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:526:13-73
490            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
490-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:527:13-170
491            android:process=":p32"
491-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:528:13-35
492            android:taskAffinity="com.lody.virtual.vt"
492-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:529:13-55
493            android:theme="@style/VATheme" />
493-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:530:13-43
494        <activity
494-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:532:9-537:46
495            android:name="com.lody.virtual.client.stub.StubActivity$C33"
495-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:533:13-73
496            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
496-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:534:13-170
497            android:process=":p33"
497-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:535:13-35
498            android:taskAffinity="com.lody.virtual.vt"
498-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:536:13-55
499            android:theme="@style/VATheme" />
499-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:537:13-43
500        <activity
500-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:539:9-545:46
501            android:name="com.lody.virtual.client.stub.StubActivity$C34"
501-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:540:13-73
502            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
502-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:541:13-170
503            android:process=":p34"
503-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:543:13-35
504            android:taskAffinity="com.lody.virtual.vt"
504-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:544:13-55
505            android:theme="@style/VATheme" />
505-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:545:13-43
506        <activity
506-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:547:9-552:46
507            android:name="com.lody.virtual.client.stub.StubActivity$C35"
507-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:548:13-73
508            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
508-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:549:13-170
509            android:process=":p35"
509-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:550:13-35
510            android:taskAffinity="com.lody.virtual.vt"
510-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:551:13-55
511            android:theme="@style/VATheme" />
511-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:552:13-43
512        <activity
512-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:554:9-559:46
513            android:name="com.lody.virtual.client.stub.StubActivity$C36"
513-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:555:13-73
514            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
514-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:556:13-170
515            android:process=":p36"
515-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:557:13-35
516            android:taskAffinity="com.lody.virtual.vt"
516-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:558:13-55
517            android:theme="@style/VATheme" />
517-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:559:13-43
518        <activity
518-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:561:9-566:46
519            android:name="com.lody.virtual.client.stub.StubActivity$C37"
519-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:562:13-73
520            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
520-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:563:13-170
521            android:process=":p37"
521-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:564:13-35
522            android:taskAffinity="com.lody.virtual.vt"
522-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:565:13-55
523            android:theme="@style/VATheme" />
523-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:566:13-43
524        <activity
524-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:568:9-573:46
525            android:name="com.lody.virtual.client.stub.StubActivity$C38"
525-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:569:13-73
526            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
526-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:570:13-170
527            android:process=":p38"
527-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:571:13-35
528            android:taskAffinity="com.lody.virtual.vt"
528-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:572:13-55
529            android:theme="@style/VATheme" />
529-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:573:13-43
530        <activity
530-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:575:9-580:46
531            android:name="com.lody.virtual.client.stub.StubActivity$C39"
531-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:576:13-73
532            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
532-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:577:13-170
533            android:process=":p39"
533-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:578:13-35
534            android:taskAffinity="com.lody.virtual.vt"
534-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:579:13-55
535            android:theme="@style/VATheme" />
535-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:580:13-43
536        <activity
536-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:582:9-587:46
537            android:name="com.lody.virtual.client.stub.StubActivity$C40"
537-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:583:13-73
538            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
538-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:584:13-170
539            android:process=":p40"
539-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:585:13-35
540            android:taskAffinity="com.lody.virtual.vt"
540-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:586:13-55
541            android:theme="@style/VATheme" />
541-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:587:13-43
542        <activity
542-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:589:9-594:46
543            android:name="com.lody.virtual.client.stub.StubActivity$C41"
543-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:590:13-73
544            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
544-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:591:13-170
545            android:process=":p41"
545-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:592:13-35
546            android:taskAffinity="com.lody.virtual.vt"
546-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:593:13-55
547            android:theme="@style/VATheme" />
547-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:594:13-43
548        <activity
548-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:596:9-601:46
549            android:name="com.lody.virtual.client.stub.StubActivity$C42"
549-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:597:13-73
550            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
550-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:598:13-170
551            android:process=":p42"
551-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:599:13-35
552            android:taskAffinity="com.lody.virtual.vt"
552-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:600:13-55
553            android:theme="@style/VATheme" />
553-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:601:13-43
554        <activity
554-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:603:9-608:46
555            android:name="com.lody.virtual.client.stub.StubActivity$C43"
555-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:604:13-73
556            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
556-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:605:13-170
557            android:process=":p43"
557-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:606:13-35
558            android:taskAffinity="com.lody.virtual.vt"
558-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:607:13-55
559            android:theme="@style/VATheme" />
559-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:608:13-43
560        <activity
560-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:610:9-615:46
561            android:name="com.lody.virtual.client.stub.StubActivity$C44"
561-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:611:13-73
562            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
562-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:612:13-170
563            android:process=":p44"
563-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:613:13-35
564            android:taskAffinity="com.lody.virtual.vt"
564-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:614:13-55
565            android:theme="@style/VATheme" />
565-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:615:13-43
566        <activity
566-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:617:9-622:46
567            android:name="com.lody.virtual.client.stub.StubActivity$C45"
567-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:618:13-73
568            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
568-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:619:13-170
569            android:process=":p45"
569-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:620:13-35
570            android:taskAffinity="com.lody.virtual.vt"
570-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:621:13-55
571            android:theme="@style/VATheme" />
571-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:622:13-43
572        <activity
572-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:624:9-629:46
573            android:name="com.lody.virtual.client.stub.StubActivity$C46"
573-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:625:13-73
574            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
574-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:626:13-170
575            android:process=":p46"
575-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:627:13-35
576            android:taskAffinity="com.lody.virtual.vt"
576-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:628:13-55
577            android:theme="@style/VATheme" />
577-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:629:13-43
578        <activity
578-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:631:9-636:46
579            android:name="com.lody.virtual.client.stub.StubActivity$C47"
579-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:632:13-73
580            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
580-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:633:13-170
581            android:process=":p47"
581-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:634:13-35
582            android:taskAffinity="com.lody.virtual.vt"
582-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:635:13-55
583            android:theme="@style/VATheme" />
583-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:636:13-43
584        <activity
584-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:638:9-643:46
585            android:name="com.lody.virtual.client.stub.StubActivity$C48"
585-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:639:13-73
586            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
586-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:640:13-170
587            android:process=":p48"
587-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:641:13-35
588            android:taskAffinity="com.lody.virtual.vt"
588-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:642:13-55
589            android:theme="@style/VATheme" />
589-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:643:13-43
590        <activity
590-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:645:9-650:46
591            android:name="com.lody.virtual.client.stub.StubActivity$C49"
591-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:646:13-73
592            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
592-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:647:13-170
593            android:process=":p49"
593-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:648:13-35
594            android:taskAffinity="com.lody.virtual.vt"
594-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:649:13-55
595            android:theme="@style/VATheme" />
595-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:650:13-43
596        <activity
596-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:652:9-657:59
597            android:name="com.lody.virtual.client.stub.StubDialog$C0"
597-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:653:13-70
598            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
598-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:654:13-170
599            android:process=":p0"
599-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:655:13-34
600            android:taskAffinity="com.lody.virtual.vt"
600-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:656:13-55
601            android:theme="@android:style/Theme.Dialog" />
601-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:657:13-56
602        <activity
602-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:659:9-664:59
603            android:name="com.lody.virtual.client.stub.StubDialog$C1"
603-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:660:13-70
604            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
604-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:661:13-170
605            android:process=":p1"
605-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:662:13-34
606            android:taskAffinity="com.lody.virtual.vt"
606-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:663:13-55
607            android:theme="@android:style/Theme.Dialog" />
607-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:664:13-56
608        <activity
608-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:666:9-671:59
609            android:name="com.lody.virtual.client.stub.StubDialog$C2"
609-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:667:13-70
610            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
610-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:668:13-170
611            android:process=":p2"
611-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:669:13-34
612            android:taskAffinity="com.lody.virtual.vt"
612-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:670:13-55
613            android:theme="@android:style/Theme.Dialog" />
613-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:671:13-56
614        <activity
614-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:673:9-678:59
615            android:name="com.lody.virtual.client.stub.StubDialog$C3"
615-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:674:13-70
616            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
616-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:675:13-170
617            android:process=":p3"
617-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:676:13-34
618            android:taskAffinity="com.lody.virtual.vt"
618-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:677:13-55
619            android:theme="@android:style/Theme.Dialog" />
619-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:678:13-56
620        <activity
620-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:680:9-685:59
621            android:name="com.lody.virtual.client.stub.StubDialog$C4"
621-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:681:13-70
622            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
622-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:682:13-170
623            android:process=":p4"
623-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:683:13-34
624            android:taskAffinity="com.lody.virtual.vt"
624-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:684:13-55
625            android:theme="@android:style/Theme.Dialog" />
625-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:685:13-56
626        <activity
626-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:687:9-692:59
627            android:name="com.lody.virtual.client.stub.StubDialog$C5"
627-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:688:13-70
628            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
628-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:689:13-170
629            android:process=":p5"
629-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:690:13-34
630            android:taskAffinity="com.lody.virtual.vt"
630-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:691:13-55
631            android:theme="@android:style/Theme.Dialog" />
631-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:692:13-56
632        <activity
632-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:694:9-699:59
633            android:name="com.lody.virtual.client.stub.StubDialog$C6"
633-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:695:13-70
634            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
634-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:696:13-170
635            android:process=":p6"
635-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:697:13-34
636            android:taskAffinity="com.lody.virtual.vt"
636-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:698:13-55
637            android:theme="@android:style/Theme.Dialog" />
637-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:699:13-56
638        <activity
638-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:701:9-706:59
639            android:name="com.lody.virtual.client.stub.StubDialog$C7"
639-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:702:13-70
640            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
640-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:703:13-170
641            android:process=":p7"
641-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:704:13-34
642            android:taskAffinity="com.lody.virtual.vt"
642-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:705:13-55
643            android:theme="@android:style/Theme.Dialog" />
643-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:706:13-56
644        <activity
644-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:708:9-713:59
645            android:name="com.lody.virtual.client.stub.StubDialog$C8"
645-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:709:13-70
646            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
646-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:710:13-170
647            android:process=":p8"
647-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:711:13-34
648            android:taskAffinity="com.lody.virtual.vt"
648-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:712:13-55
649            android:theme="@android:style/Theme.Dialog" />
649-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:713:13-56
650        <activity
650-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:715:9-720:59
651            android:name="com.lody.virtual.client.stub.StubDialog$C9"
651-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:716:13-70
652            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
652-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:717:13-170
653            android:process=":p9"
653-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:718:13-34
654            android:taskAffinity="com.lody.virtual.vt"
654-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:719:13-55
655            android:theme="@android:style/Theme.Dialog" />
655-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:720:13-56
656        <activity
656-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:722:9-727:59
657            android:name="com.lody.virtual.client.stub.StubDialog$C10"
657-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:723:13-71
658            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
658-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:724:13-170
659            android:process=":p10"
659-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:725:13-35
660            android:taskAffinity="com.lody.virtual.vt"
660-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:726:13-55
661            android:theme="@android:style/Theme.Dialog" />
661-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:727:13-56
662        <activity
662-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:729:9-734:59
663            android:name="com.lody.virtual.client.stub.StubDialog$C11"
663-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:730:13-71
664            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
664-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:731:13-170
665            android:process=":p11"
665-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:732:13-35
666            android:taskAffinity="com.lody.virtual.vt"
666-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:733:13-55
667            android:theme="@android:style/Theme.Dialog" />
667-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:734:13-56
668        <activity
668-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:736:9-741:59
669            android:name="com.lody.virtual.client.stub.StubDialog$C12"
669-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:737:13-71
670            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
670-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:738:13-170
671            android:process=":p12"
671-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:739:13-35
672            android:taskAffinity="com.lody.virtual.vt"
672-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:740:13-55
673            android:theme="@android:style/Theme.Dialog" />
673-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:741:13-56
674        <activity
674-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:743:9-748:59
675            android:name="com.lody.virtual.client.stub.StubDialog$C13"
675-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:744:13-71
676            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
676-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:745:13-170
677            android:process=":p13"
677-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:746:13-35
678            android:taskAffinity="com.lody.virtual.vt"
678-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:747:13-55
679            android:theme="@android:style/Theme.Dialog" />
679-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:748:13-56
680        <activity
680-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:750:9-755:59
681            android:name="com.lody.virtual.client.stub.StubDialog$C14"
681-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:751:13-71
682            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
682-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:752:13-170
683            android:process=":p14"
683-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:753:13-35
684            android:taskAffinity="com.lody.virtual.vt"
684-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:754:13-55
685            android:theme="@android:style/Theme.Dialog" />
685-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:755:13-56
686        <activity
686-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:757:9-762:59
687            android:name="com.lody.virtual.client.stub.StubDialog$C15"
687-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:758:13-71
688            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
688-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:759:13-170
689            android:process=":p15"
689-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:760:13-35
690            android:taskAffinity="com.lody.virtual.vt"
690-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:761:13-55
691            android:theme="@android:style/Theme.Dialog" />
691-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:762:13-56
692        <activity
692-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:764:9-769:59
693            android:name="com.lody.virtual.client.stub.StubDialog$C16"
693-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:765:13-71
694            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
694-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:766:13-170
695            android:process=":p16"
695-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:767:13-35
696            android:taskAffinity="com.lody.virtual.vt"
696-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:768:13-55
697            android:theme="@android:style/Theme.Dialog" />
697-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:769:13-56
698        <activity
698-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:771:9-776:59
699            android:name="com.lody.virtual.client.stub.StubDialog$C17"
699-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:772:13-71
700            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
700-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:773:13-170
701            android:process=":p17"
701-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:774:13-35
702            android:taskAffinity="com.lody.virtual.vt"
702-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:775:13-55
703            android:theme="@android:style/Theme.Dialog" />
703-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:776:13-56
704        <activity
704-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:778:9-783:59
705            android:name="com.lody.virtual.client.stub.StubDialog$C18"
705-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:779:13-71
706            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
706-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:780:13-170
707            android:process=":p18"
707-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:781:13-35
708            android:taskAffinity="com.lody.virtual.vt"
708-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:782:13-55
709            android:theme="@android:style/Theme.Dialog" />
709-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:783:13-56
710        <activity
710-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:785:9-790:59
711            android:name="com.lody.virtual.client.stub.StubDialog$C19"
711-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:786:13-71
712            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
712-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:787:13-170
713            android:process=":p19"
713-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:788:13-35
714            android:taskAffinity="com.lody.virtual.vt"
714-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:789:13-55
715            android:theme="@android:style/Theme.Dialog" />
715-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:790:13-56
716        <activity
716-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:792:9-797:59
717            android:name="com.lody.virtual.client.stub.StubDialog$C20"
717-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:793:13-71
718            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
718-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:794:13-170
719            android:process=":p20"
719-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:795:13-35
720            android:taskAffinity="com.lody.virtual.vt"
720-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:796:13-55
721            android:theme="@android:style/Theme.Dialog" />
721-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:797:13-56
722        <activity
722-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:799:9-804:59
723            android:name="com.lody.virtual.client.stub.StubDialog$C21"
723-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:800:13-71
724            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
724-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:801:13-170
725            android:process=":p21"
725-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:802:13-35
726            android:taskAffinity="com.lody.virtual.vt"
726-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:803:13-55
727            android:theme="@android:style/Theme.Dialog" />
727-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:804:13-56
728        <activity
728-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:806:9-811:59
729            android:name="com.lody.virtual.client.stub.StubDialog$C22"
729-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:807:13-71
730            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
730-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:808:13-170
731            android:process=":p22"
731-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:809:13-35
732            android:taskAffinity="com.lody.virtual.vt"
732-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:810:13-55
733            android:theme="@android:style/Theme.Dialog" />
733-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:811:13-56
734        <activity
734-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:813:9-818:59
735            android:name="com.lody.virtual.client.stub.StubDialog$C23"
735-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:814:13-71
736            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
736-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:815:13-170
737            android:process=":p23"
737-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:816:13-35
738            android:taskAffinity="com.lody.virtual.vt"
738-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:817:13-55
739            android:theme="@android:style/Theme.Dialog" />
739-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:818:13-56
740        <activity
740-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:820:9-825:59
741            android:name="com.lody.virtual.client.stub.StubDialog$C24"
741-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:821:13-71
742            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
742-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:822:13-170
743            android:process=":p24"
743-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:823:13-35
744            android:taskAffinity="com.lody.virtual.vt"
744-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:824:13-55
745            android:theme="@android:style/Theme.Dialog" />
745-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:825:13-56
746        <activity
746-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:827:9-832:59
747            android:name="com.lody.virtual.client.stub.StubDialog$C25"
747-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:828:13-71
748            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
748-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:829:13-170
749            android:process=":p25"
749-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:830:13-35
750            android:taskAffinity="com.lody.virtual.vt"
750-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:831:13-55
751            android:theme="@android:style/Theme.Dialog" />
751-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:832:13-56
752        <activity
752-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:834:9-839:59
753            android:name="com.lody.virtual.client.stub.StubDialog$C26"
753-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:835:13-71
754            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
754-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:836:13-170
755            android:process=":p26"
755-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:837:13-35
756            android:taskAffinity="com.lody.virtual.vt"
756-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:838:13-55
757            android:theme="@android:style/Theme.Dialog" />
757-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:839:13-56
758        <activity
758-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:841:9-846:59
759            android:name="com.lody.virtual.client.stub.StubDialog$C27"
759-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:842:13-71
760            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
760-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:843:13-170
761            android:process=":p27"
761-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:844:13-35
762            android:taskAffinity="com.lody.virtual.vt"
762-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:845:13-55
763            android:theme="@android:style/Theme.Dialog" />
763-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:846:13-56
764        <activity
764-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:848:9-853:59
765            android:name="com.lody.virtual.client.stub.StubDialog$C28"
765-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:849:13-71
766            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
766-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:850:13-170
767            android:process=":p28"
767-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:851:13-35
768            android:taskAffinity="com.lody.virtual.vt"
768-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:852:13-55
769            android:theme="@android:style/Theme.Dialog" />
769-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:853:13-56
770        <activity
770-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:855:9-860:59
771            android:name="com.lody.virtual.client.stub.StubDialog$C29"
771-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:856:13-71
772            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
772-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:857:13-170
773            android:process=":p29"
773-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:858:13-35
774            android:taskAffinity="com.lody.virtual.vt"
774-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:859:13-55
775            android:theme="@android:style/Theme.Dialog" />
775-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:860:13-56
776        <activity
776-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:862:9-867:59
777            android:name="com.lody.virtual.client.stub.StubDialog$C30"
777-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:863:13-71
778            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
778-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:864:13-170
779            android:process=":p30"
779-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:865:13-35
780            android:taskAffinity="com.lody.virtual.vt"
780-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:866:13-55
781            android:theme="@android:style/Theme.Dialog" />
781-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:867:13-56
782        <activity
782-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:869:9-874:59
783            android:name="com.lody.virtual.client.stub.StubDialog$C31"
783-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:870:13-71
784            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
784-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:871:13-170
785            android:process=":p31"
785-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:872:13-35
786            android:taskAffinity="com.lody.virtual.vt"
786-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:873:13-55
787            android:theme="@android:style/Theme.Dialog" />
787-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:874:13-56
788        <activity
788-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:876:9-881:59
789            android:name="com.lody.virtual.client.stub.StubDialog$C32"
789-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:877:13-71
790            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
790-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:878:13-170
791            android:process=":p32"
791-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:879:13-35
792            android:taskAffinity="com.lody.virtual.vt"
792-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:880:13-55
793            android:theme="@android:style/Theme.Dialog" />
793-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:881:13-56
794        <activity
794-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:883:9-888:59
795            android:name="com.lody.virtual.client.stub.StubDialog$C33"
795-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:884:13-71
796            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
796-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:885:13-170
797            android:process=":p33"
797-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:886:13-35
798            android:taskAffinity="com.lody.virtual.vt"
798-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:887:13-55
799            android:theme="@android:style/Theme.Dialog" />
799-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:888:13-56
800        <activity
800-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:890:9-895:59
801            android:name="com.lody.virtual.client.stub.StubDialog$C34"
801-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:891:13-71
802            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
802-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:892:13-170
803            android:process=":p34"
803-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:893:13-35
804            android:taskAffinity="com.lody.virtual.vt"
804-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:894:13-55
805            android:theme="@android:style/Theme.Dialog" />
805-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:895:13-56
806        <activity
806-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:897:9-902:59
807            android:name="com.lody.virtual.client.stub.StubDialog$C35"
807-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:898:13-71
808            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
808-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:899:13-170
809            android:process=":p35"
809-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:900:13-35
810            android:taskAffinity="com.lody.virtual.vt"
810-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:901:13-55
811            android:theme="@android:style/Theme.Dialog" />
811-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:902:13-56
812        <activity
812-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:904:9-909:59
813            android:name="com.lody.virtual.client.stub.StubDialog$C36"
813-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:905:13-71
814            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
814-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:906:13-170
815            android:process=":p36"
815-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:907:13-35
816            android:taskAffinity="com.lody.virtual.vt"
816-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:908:13-55
817            android:theme="@android:style/Theme.Dialog" />
817-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:909:13-56
818        <activity
818-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:911:9-916:59
819            android:name="com.lody.virtual.client.stub.StubDialog$C37"
819-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:912:13-71
820            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
820-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:913:13-170
821            android:process=":p37"
821-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:914:13-35
822            android:taskAffinity="com.lody.virtual.vt"
822-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:915:13-55
823            android:theme="@android:style/Theme.Dialog" />
823-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:916:13-56
824        <activity
824-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:918:9-923:59
825            android:name="com.lody.virtual.client.stub.StubDialog$C38"
825-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:919:13-71
826            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
826-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:920:13-170
827            android:process=":p38"
827-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:921:13-35
828            android:taskAffinity="com.lody.virtual.vt"
828-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:922:13-55
829            android:theme="@android:style/Theme.Dialog" />
829-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:923:13-56
830        <activity
830-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:925:9-930:59
831            android:name="com.lody.virtual.client.stub.StubDialog$C39"
831-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:926:13-71
832            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
832-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:927:13-170
833            android:process=":p39"
833-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:928:13-35
834            android:taskAffinity="com.lody.virtual.vt"
834-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:929:13-55
835            android:theme="@android:style/Theme.Dialog" />
835-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:930:13-56
836        <activity
836-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:932:9-937:59
837            android:name="com.lody.virtual.client.stub.StubDialog$C40"
837-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:933:13-71
838            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
838-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:934:13-170
839            android:process=":p40"
839-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:935:13-35
840            android:taskAffinity="com.lody.virtual.vt"
840-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:936:13-55
841            android:theme="@android:style/Theme.Dialog" />
841-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:937:13-56
842        <activity
842-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:939:9-944:59
843            android:name="com.lody.virtual.client.stub.StubDialog$C41"
843-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:940:13-71
844            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
844-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:941:13-170
845            android:process=":p41"
845-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:942:13-35
846            android:taskAffinity="com.lody.virtual.vt"
846-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:943:13-55
847            android:theme="@android:style/Theme.Dialog" />
847-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:944:13-56
848        <activity
848-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:946:9-951:59
849            android:name="com.lody.virtual.client.stub.StubDialog$C42"
849-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:947:13-71
850            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
850-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:948:13-170
851            android:process=":p42"
851-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:949:13-35
852            android:taskAffinity="com.lody.virtual.vt"
852-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:950:13-55
853            android:theme="@android:style/Theme.Dialog" />
853-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:951:13-56
854        <activity
854-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:953:9-958:59
855            android:name="com.lody.virtual.client.stub.StubDialog$C43"
855-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:954:13-71
856            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
856-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:955:13-170
857            android:process=":p43"
857-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:956:13-35
858            android:taskAffinity="com.lody.virtual.vt"
858-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:957:13-55
859            android:theme="@android:style/Theme.Dialog" />
859-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:958:13-56
860        <activity
860-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:960:9-965:59
861            android:name="com.lody.virtual.client.stub.StubDialog$C44"
861-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:961:13-71
862            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
862-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:962:13-170
863            android:process=":p44"
863-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:963:13-35
864            android:taskAffinity="com.lody.virtual.vt"
864-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:964:13-55
865            android:theme="@android:style/Theme.Dialog" />
865-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:965:13-56
866        <activity
866-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:967:9-972:59
867            android:name="com.lody.virtual.client.stub.StubDialog$C45"
867-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:968:13-71
868            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
868-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:969:13-170
869            android:process=":p45"
869-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:970:13-35
870            android:taskAffinity="com.lody.virtual.vt"
870-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:971:13-55
871            android:theme="@android:style/Theme.Dialog" />
871-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:972:13-56
872        <activity
872-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:974:9-979:59
873            android:name="com.lody.virtual.client.stub.StubDialog$C46"
873-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:975:13-71
874            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
874-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:976:13-170
875            android:process=":p46"
875-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:977:13-35
876            android:taskAffinity="com.lody.virtual.vt"
876-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:978:13-55
877            android:theme="@android:style/Theme.Dialog" />
877-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:979:13-56
878        <activity
878-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:981:9-986:59
879            android:name="com.lody.virtual.client.stub.StubDialog$C47"
879-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:982:13-71
880            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
880-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:983:13-170
881            android:process=":p47"
881-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:984:13-35
882            android:taskAffinity="com.lody.virtual.vt"
882-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:985:13-55
883            android:theme="@android:style/Theme.Dialog" />
883-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:986:13-56
884        <activity
884-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:988:9-993:59
885            android:name="com.lody.virtual.client.stub.StubDialog$C48"
885-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:989:13-71
886            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
886-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:990:13-170
887            android:process=":p48"
887-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:991:13-35
888            android:taskAffinity="com.lody.virtual.vt"
888-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:992:13-55
889            android:theme="@android:style/Theme.Dialog" />
889-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:993:13-56
890        <activity
890-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:995:9-1000:59
891            android:name="com.lody.virtual.client.stub.StubDialog$C49"
891-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:996:13-71
892            android:configChanges="mcc|mnc|locale|touchscreen|keyboard|keyboardHidden|navigation|orientation|screenLayout|uiMode|screenSize|smallestScreenSize|fontScale"
892-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:997:13-170
893            android:process=":p49"
893-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:998:13-35
894            android:taskAffinity="com.lody.virtual.vt"
894-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:999:13-55
895            android:theme="@android:style/Theme.Dialog" />
895-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1000:13-56
896
897        <provider
897-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1002:9-1006:37
898            android:name="com.lody.virtual.client.stub.StubContentProvider$C0"
898-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1003:13-79
899            android:authorities="${applicationId}.virtual_stub_0"
899-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1004:13-66
900            android:exported="false"
900-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1005:13-37
901            android:process=":p0" />
901-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1006:13-34
902        <provider
902-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1008:9-1012:37
903            android:name="com.lody.virtual.client.stub.StubContentProvider$C1"
903-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1009:13-79
904            android:authorities="${applicationId}.virtual_stub_1"
904-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1010:13-66
905            android:exported="false"
905-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1011:13-37
906            android:process=":p1" />
906-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1012:13-34
907        <provider
907-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1014:9-1018:37
908            android:name="com.lody.virtual.client.stub.StubContentProvider$C2"
908-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1015:13-79
909            android:authorities="${applicationId}.virtual_stub_2"
909-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1016:13-66
910            android:exported="false"
910-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1017:13-37
911            android:process=":p2" />
911-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1018:13-34
912        <provider
912-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1020:9-1024:37
913            android:name="com.lody.virtual.client.stub.StubContentProvider$C3"
913-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1021:13-79
914            android:authorities="${applicationId}.virtual_stub_3"
914-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1022:13-66
915            android:exported="false"
915-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1023:13-37
916            android:process=":p3" />
916-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1024:13-34
917        <provider
917-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1026:9-1030:37
918            android:name="com.lody.virtual.client.stub.StubContentProvider$C4"
918-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1027:13-79
919            android:authorities="${applicationId}.virtual_stub_4"
919-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1028:13-66
920            android:exported="false"
920-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1029:13-37
921            android:process=":p4" />
921-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1030:13-34
922        <provider
922-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1032:9-1036:37
923            android:name="com.lody.virtual.client.stub.StubContentProvider$C5"
923-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1033:13-79
924            android:authorities="${applicationId}.virtual_stub_5"
924-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1034:13-66
925            android:exported="false"
925-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1035:13-37
926            android:process=":p5" />
926-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1036:13-34
927        <provider
927-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1038:9-1042:37
928            android:name="com.lody.virtual.client.stub.StubContentProvider$C6"
928-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1039:13-79
929            android:authorities="${applicationId}.virtual_stub_6"
929-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1040:13-66
930            android:exported="false"
930-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1041:13-37
931            android:process=":p6" />
931-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1042:13-34
932        <provider
932-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1044:9-1048:37
933            android:name="com.lody.virtual.client.stub.StubContentProvider$C7"
933-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1045:13-79
934            android:authorities="${applicationId}.virtual_stub_7"
934-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1046:13-66
935            android:exported="false"
935-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1047:13-37
936            android:process=":p7" />
936-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1048:13-34
937        <provider
937-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1050:9-1054:37
938            android:name="com.lody.virtual.client.stub.StubContentProvider$C8"
938-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1051:13-79
939            android:authorities="${applicationId}.virtual_stub_8"
939-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1052:13-66
940            android:exported="false"
940-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1053:13-37
941            android:process=":p8" />
941-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1054:13-34
942        <provider
942-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1056:9-1060:37
943            android:name="com.lody.virtual.client.stub.StubContentProvider$C9"
943-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1057:13-79
944            android:authorities="${applicationId}.virtual_stub_9"
944-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1058:13-66
945            android:exported="false"
945-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1059:13-37
946            android:process=":p9" />
946-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1060:13-34
947        <provider
947-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1062:9-1066:38
948            android:name="com.lody.virtual.client.stub.StubContentProvider$C10"
948-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1063:13-80
949            android:authorities="${applicationId}.virtual_stub_10"
949-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1064:13-67
950            android:exported="false"
950-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1065:13-37
951            android:process=":p10" />
951-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1066:13-35
952        <provider
952-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1068:9-1072:38
953            android:name="com.lody.virtual.client.stub.StubContentProvider$C11"
953-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1069:13-80
954            android:authorities="${applicationId}.virtual_stub_11"
954-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1070:13-67
955            android:exported="false"
955-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1071:13-37
956            android:process=":p11" />
956-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1072:13-35
957        <provider
957-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1074:9-1078:38
958            android:name="com.lody.virtual.client.stub.StubContentProvider$C12"
958-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1075:13-80
959            android:authorities="${applicationId}.virtual_stub_12"
959-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1076:13-67
960            android:exported="false"
960-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1077:13-37
961            android:process=":p12" />
961-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1078:13-35
962        <provider
962-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1080:9-1084:38
963            android:name="com.lody.virtual.client.stub.StubContentProvider$C13"
963-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1081:13-80
964            android:authorities="${applicationId}.virtual_stub_13"
964-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1082:13-67
965            android:exported="false"
965-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1083:13-37
966            android:process=":p13" />
966-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1084:13-35
967        <provider
967-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1086:9-1090:38
968            android:name="com.lody.virtual.client.stub.StubContentProvider$C14"
968-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1087:13-80
969            android:authorities="${applicationId}.virtual_stub_14"
969-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1088:13-67
970            android:exported="false"
970-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1089:13-37
971            android:process=":p14" />
971-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1090:13-35
972        <provider
972-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1092:9-1096:38
973            android:name="com.lody.virtual.client.stub.StubContentProvider$C15"
973-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1093:13-80
974            android:authorities="${applicationId}.virtual_stub_15"
974-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1094:13-67
975            android:exported="false"
975-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1095:13-37
976            android:process=":p15" />
976-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1096:13-35
977        <provider
977-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1098:9-1102:38
978            android:name="com.lody.virtual.client.stub.StubContentProvider$C16"
978-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1099:13-80
979            android:authorities="${applicationId}.virtual_stub_16"
979-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1100:13-67
980            android:exported="false"
980-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1101:13-37
981            android:process=":p16" />
981-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1102:13-35
982        <provider
982-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1104:9-1108:38
983            android:name="com.lody.virtual.client.stub.StubContentProvider$C17"
983-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1105:13-80
984            android:authorities="${applicationId}.virtual_stub_17"
984-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1106:13-67
985            android:exported="false"
985-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1107:13-37
986            android:process=":p17" />
986-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1108:13-35
987        <provider
987-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1110:9-1114:38
988            android:name="com.lody.virtual.client.stub.StubContentProvider$C18"
988-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1111:13-80
989            android:authorities="${applicationId}.virtual_stub_18"
989-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1112:13-67
990            android:exported="false"
990-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1113:13-37
991            android:process=":p18" />
991-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1114:13-35
992        <provider
992-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1116:9-1120:38
993            android:name="com.lody.virtual.client.stub.StubContentProvider$C19"
993-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1117:13-80
994            android:authorities="${applicationId}.virtual_stub_19"
994-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1118:13-67
995            android:exported="false"
995-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1119:13-37
996            android:process=":p19" />
996-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1120:13-35
997        <provider
997-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1122:9-1126:38
998            android:name="com.lody.virtual.client.stub.StubContentProvider$C20"
998-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1123:13-80
999            android:authorities="${applicationId}.virtual_stub_20"
999-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1124:13-67
1000            android:exported="false"
1000-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1125:13-37
1001            android:process=":p20" />
1001-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1126:13-35
1002        <provider
1002-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1128:9-1132:38
1003            android:name="com.lody.virtual.client.stub.StubContentProvider$C21"
1003-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1129:13-80
1004            android:authorities="${applicationId}.virtual_stub_21"
1004-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1130:13-67
1005            android:exported="false"
1005-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1131:13-37
1006            android:process=":p21" />
1006-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1132:13-35
1007        <provider
1007-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1134:9-1138:38
1008            android:name="com.lody.virtual.client.stub.StubContentProvider$C22"
1008-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1135:13-80
1009            android:authorities="${applicationId}.virtual_stub_22"
1009-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1136:13-67
1010            android:exported="false"
1010-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1137:13-37
1011            android:process=":p22" />
1011-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1138:13-35
1012        <provider
1012-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1140:9-1144:38
1013            android:name="com.lody.virtual.client.stub.StubContentProvider$C23"
1013-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1141:13-80
1014            android:authorities="${applicationId}.virtual_stub_23"
1014-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1142:13-67
1015            android:exported="false"
1015-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1143:13-37
1016            android:process=":p23" />
1016-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1144:13-35
1017        <provider
1017-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1146:9-1150:38
1018            android:name="com.lody.virtual.client.stub.StubContentProvider$C24"
1018-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1147:13-80
1019            android:authorities="${applicationId}.virtual_stub_24"
1019-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1148:13-67
1020            android:exported="false"
1020-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1149:13-37
1021            android:process=":p24" />
1021-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1150:13-35
1022        <provider
1022-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1152:9-1156:38
1023            android:name="com.lody.virtual.client.stub.StubContentProvider$C25"
1023-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1153:13-80
1024            android:authorities="${applicationId}.virtual_stub_25"
1024-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1154:13-67
1025            android:exported="false"
1025-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1155:13-37
1026            android:process=":p25" />
1026-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1156:13-35
1027        <provider
1027-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1158:9-1162:38
1028            android:name="com.lody.virtual.client.stub.StubContentProvider$C26"
1028-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1159:13-80
1029            android:authorities="${applicationId}.virtual_stub_26"
1029-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1160:13-67
1030            android:exported="false"
1030-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1161:13-37
1031            android:process=":p26" />
1031-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1162:13-35
1032        <provider
1032-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1164:9-1168:38
1033            android:name="com.lody.virtual.client.stub.StubContentProvider$C27"
1033-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1165:13-80
1034            android:authorities="${applicationId}.virtual_stub_27"
1034-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1166:13-67
1035            android:exported="false"
1035-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1167:13-37
1036            android:process=":p27" />
1036-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1168:13-35
1037        <provider
1037-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1170:9-1174:38
1038            android:name="com.lody.virtual.client.stub.StubContentProvider$C28"
1038-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1171:13-80
1039            android:authorities="${applicationId}.virtual_stub_28"
1039-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1172:13-67
1040            android:exported="false"
1040-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1173:13-37
1041            android:process=":p28" />
1041-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1174:13-35
1042        <provider
1042-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1176:9-1180:38
1043            android:name="com.lody.virtual.client.stub.StubContentProvider$C29"
1043-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1177:13-80
1044            android:authorities="${applicationId}.virtual_stub_29"
1044-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1178:13-67
1045            android:exported="false"
1045-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1179:13-37
1046            android:process=":p29" />
1046-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1180:13-35
1047        <provider
1047-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1182:9-1186:38
1048            android:name="com.lody.virtual.client.stub.StubContentProvider$C30"
1048-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1183:13-80
1049            android:authorities="${applicationId}.virtual_stub_30"
1049-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1184:13-67
1050            android:exported="false"
1050-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1185:13-37
1051            android:process=":p30" />
1051-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1186:13-35
1052        <provider
1052-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1188:9-1192:38
1053            android:name="com.lody.virtual.client.stub.StubContentProvider$C31"
1053-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1189:13-80
1054            android:authorities="${applicationId}.virtual_stub_31"
1054-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1190:13-67
1055            android:exported="false"
1055-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1191:13-37
1056            android:process=":p31" />
1056-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1192:13-35
1057        <provider
1057-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1194:9-1198:38
1058            android:name="com.lody.virtual.client.stub.StubContentProvider$C32"
1058-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1195:13-80
1059            android:authorities="${applicationId}.virtual_stub_32"
1059-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1196:13-67
1060            android:exported="false"
1060-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1197:13-37
1061            android:process=":p32" />
1061-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1198:13-35
1062        <provider
1062-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1200:9-1204:38
1063            android:name="com.lody.virtual.client.stub.StubContentProvider$C33"
1063-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1201:13-80
1064            android:authorities="${applicationId}.virtual_stub_33"
1064-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1202:13-67
1065            android:exported="false"
1065-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1203:13-37
1066            android:process=":p33" />
1066-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1204:13-35
1067        <provider
1067-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1206:9-1210:38
1068            android:name="com.lody.virtual.client.stub.StubContentProvider$C34"
1068-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1207:13-80
1069            android:authorities="${applicationId}.virtual_stub_34"
1069-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1208:13-67
1070            android:exported="false"
1070-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1209:13-37
1071            android:process=":p34" />
1071-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1210:13-35
1072        <provider
1072-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1212:9-1216:38
1073            android:name="com.lody.virtual.client.stub.StubContentProvider$C35"
1073-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1213:13-80
1074            android:authorities="${applicationId}.virtual_stub_35"
1074-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1214:13-67
1075            android:exported="false"
1075-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1215:13-37
1076            android:process=":p35" />
1076-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1216:13-35
1077        <provider
1077-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1218:9-1222:38
1078            android:name="com.lody.virtual.client.stub.StubContentProvider$C36"
1078-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1219:13-80
1079            android:authorities="${applicationId}.virtual_stub_36"
1079-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1220:13-67
1080            android:exported="false"
1080-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1221:13-37
1081            android:process=":p36" />
1081-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1222:13-35
1082        <provider
1082-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1224:9-1228:38
1083            android:name="com.lody.virtual.client.stub.StubContentProvider$C37"
1083-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1225:13-80
1084            android:authorities="${applicationId}.virtual_stub_37"
1084-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1226:13-67
1085            android:exported="false"
1085-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1227:13-37
1086            android:process=":p37" />
1086-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1228:13-35
1087        <provider
1087-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1230:9-1234:38
1088            android:name="com.lody.virtual.client.stub.StubContentProvider$C38"
1088-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1231:13-80
1089            android:authorities="${applicationId}.virtual_stub_38"
1089-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1232:13-67
1090            android:exported="false"
1090-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1233:13-37
1091            android:process=":p38" />
1091-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1234:13-35
1092        <provider
1092-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1236:9-1240:38
1093            android:name="com.lody.virtual.client.stub.StubContentProvider$C39"
1093-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1237:13-80
1094            android:authorities="${applicationId}.virtual_stub_39"
1094-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1238:13-67
1095            android:exported="false"
1095-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1239:13-37
1096            android:process=":p39" />
1096-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1240:13-35
1097        <provider
1097-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1242:9-1246:38
1098            android:name="com.lody.virtual.client.stub.StubContentProvider$C40"
1098-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1243:13-80
1099            android:authorities="${applicationId}.virtual_stub_40"
1099-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1244:13-67
1100            android:exported="false"
1100-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1245:13-37
1101            android:process=":p40" />
1101-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1246:13-35
1102        <provider
1102-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1248:9-1252:38
1103            android:name="com.lody.virtual.client.stub.StubContentProvider$C41"
1103-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1249:13-80
1104            android:authorities="${applicationId}.virtual_stub_41"
1104-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1250:13-67
1105            android:exported="false"
1105-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1251:13-37
1106            android:process=":p41" />
1106-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1252:13-35
1107        <provider
1107-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1254:9-1258:38
1108            android:name="com.lody.virtual.client.stub.StubContentProvider$C42"
1108-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1255:13-80
1109            android:authorities="${applicationId}.virtual_stub_42"
1109-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1256:13-67
1110            android:exported="false"
1110-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1257:13-37
1111            android:process=":p42" />
1111-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1258:13-35
1112        <provider
1112-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1260:9-1264:38
1113            android:name="com.lody.virtual.client.stub.StubContentProvider$C43"
1113-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1261:13-80
1114            android:authorities="${applicationId}.virtual_stub_43"
1114-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1262:13-67
1115            android:exported="false"
1115-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1263:13-37
1116            android:process=":p43" />
1116-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1264:13-35
1117        <provider
1117-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1266:9-1270:38
1118            android:name="com.lody.virtual.client.stub.StubContentProvider$C44"
1118-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1267:13-80
1119            android:authorities="${applicationId}.virtual_stub_44"
1119-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1268:13-67
1120            android:exported="false"
1120-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1269:13-37
1121            android:process=":p44" />
1121-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1270:13-35
1122        <provider
1122-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1272:9-1276:38
1123            android:name="com.lody.virtual.client.stub.StubContentProvider$C45"
1123-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1273:13-80
1124            android:authorities="${applicationId}.virtual_stub_45"
1124-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1274:13-67
1125            android:exported="false"
1125-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1275:13-37
1126            android:process=":p45" />
1126-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1276:13-35
1127        <provider
1127-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1278:9-1282:38
1128            android:name="com.lody.virtual.client.stub.StubContentProvider$C46"
1128-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1279:13-80
1129            android:authorities="${applicationId}.virtual_stub_46"
1129-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1280:13-67
1130            android:exported="false"
1130-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1281:13-37
1131            android:process=":p46" />
1131-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1282:13-35
1132        <provider
1132-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1284:9-1288:38
1133            android:name="com.lody.virtual.client.stub.StubContentProvider$C47"
1133-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1285:13-80
1134            android:authorities="${applicationId}.virtual_stub_47"
1134-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1286:13-67
1135            android:exported="false"
1135-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1287:13-37
1136            android:process=":p47" />
1136-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1288:13-35
1137        <provider
1137-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1290:9-1294:38
1138            android:name="com.lody.virtual.client.stub.StubContentProvider$C48"
1138-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1291:13-80
1139            android:authorities="${applicationId}.virtual_stub_48"
1139-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1292:13-67
1140            android:exported="false"
1140-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1293:13-37
1141            android:process=":p48" />
1141-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1294:13-35
1142        <provider
1142-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1296:9-1300:38
1143            android:name="com.lody.virtual.client.stub.StubContentProvider$C49"
1143-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1297:13-80
1144            android:authorities="${applicationId}.virtual_stub_49"
1144-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1298:13-67
1145            android:exported="false"
1145-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1299:13-37
1146            android:process=":p49" />
1146-->D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\src\main\AndroidManifest.xml:1300:13-35
1147    </application>
1148
1149</manifest>
