package com.lody.virtual.client.stub;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;


/**
 * <AUTHOR>
 *
 */
public class DaemonService extends Service {

    private static final int NOTIFY_ID = 1001;
    private static final String CHANNEL_ID = "VirtualApp_Daemon";

	public static void startup(Context context) {
		context.startService(new Intent(context, DaemonService.class));
	}

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "VirtualApp Background Service",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Keeps VirtualApp running in background");
            channel.setShowBadge(false);
            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            if (notificationManager != null) {
                notificationManager.createNotificationChannel(channel);
            }
        }
    }

    private Notification createNotification() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            return new Notification.Builder(this, CHANNEL_ID)
                    .setContentTitle("VirtualApp")
                    .setContentText("Running in background")
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .build();
        } else {
            return new Notification.Builder(this)
                    .setContentTitle("VirtualApp")
                    .setContentText("Running in background")
                    .setSmallIcon(android.R.drawable.ic_dialog_info)
                    .build();
        }
    }

	@Override
	public void onDestroy() {
		super.onDestroy();
		startup(this);
	}

	@Override
	public IBinder onBind(Intent intent) {
		return null;
	}

	@Override
	public void onCreate() {
		super.onCreate();
        createNotificationChannel();
        startService(new Intent(this, InnerService.class));
        startForeground(NOTIFY_ID, createNotification());

	}

	@Override
	public int onStartCommand(Intent intent, int flags, int startId) {
		return START_STICKY;
	}

	public static final class InnerService extends Service {

        @Override
        public int onStartCommand(Intent intent, int flags, int startId) {
            Notification notification;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                notification = new Notification.Builder(this, CHANNEL_ID)
                        .setContentTitle("VirtualApp")
                        .setContentText("Running in background")
                        .setSmallIcon(android.R.drawable.ic_dialog_info)
                        .build();
            } else {
                notification = new Notification.Builder(this)
                        .setContentTitle("VirtualApp")
                        .setContentText("Running in background")
                        .setSmallIcon(android.R.drawable.ic_dialog_info)
                        .build();
            }
            startForeground(NOTIFY_ID, notification);
            stopForeground(true);
            stopSelf();
            return super.onStartCommand(intent, flags, startId);
        }

		@Override
		public IBinder onBind(Intent intent) {
			return null;
		}
	}


}
