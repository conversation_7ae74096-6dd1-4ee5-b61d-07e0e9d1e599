package com.lody.virtual.client.hook.proxies.am;

import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.ServiceInfo;
import android.os.Handler;
import android.os.IBinder;
import android.os.Message;

import com.lody.virtual.client.VClientImpl;
import com.lody.virtual.client.core.VirtualCore;
import com.lody.virtual.client.interfaces.IInjector;
import com.lody.virtual.client.ipc.VActivityManager;
import com.lody.virtual.helper.utils.ComponentUtils;
import com.lody.virtual.helper.utils.Reflect;
import com.lody.virtual.helper.utils.VLog;
import com.lody.virtual.remote.InstalledAppInfo;
import com.lody.virtual.remote.StubActivityRecord;

import mirror.android.app.ActivityManagerNative;
import mirror.android.app.ActivityThread;
import mirror.android.app.IActivityManager;

/**
     * <AUTHOR>
     * @see Handler.Callback
     */
    public class HCallbackStub implements Handler.Callback, IInjector {


        // 安全获取ActivityThread.H中的常量值，处理反射初始化失败的情况
        private static final int LAUNCH_ACTIVITY = getActivityThreadConstant("LAUNCH_ACTIVITY", 100);
        private static final int CREATE_SERVICE = getActivityThreadConstant("CREATE_SERVICE", 114);
        private static final int SCHEDULE_CRASH = getActivityThreadConstant("SCHEDULE_CRASH", -1);

        private static final String TAG = HCallbackStub.class.getSimpleName();
        private static final HCallbackStub sCallback = new HCallbackStub();

        /**
         * 安全获取ActivityThread.H中的常量值
         * @param constantName 常量名称
         * @param defaultValue 默认值（当反射失败时使用）
         * @return 常量值
         */
        private static int getActivityThreadConstant(String constantName, int defaultValue) {
            try {
                switch (constantName) {
                    case "LAUNCH_ACTIVITY":
                        if (ActivityThread.H.LAUNCH_ACTIVITY != null) {
                            return ActivityThread.H.LAUNCH_ACTIVITY.get();
                        }
                        break;
                    case "CREATE_SERVICE":
                        if (ActivityThread.H.CREATE_SERVICE != null) {
                            return ActivityThread.H.CREATE_SERVICE.get();
                        }
                        break;
                    case "SCHEDULE_CRASH":
                        if (ActivityThread.H.SCHEDULE_CRASH != null) {
                            return ActivityThread.H.SCHEDULE_CRASH.get();
                        }
                        break;
                }

                // 如果mirror反射失败，尝试使用Java反射直接获取
                android.util.Log.w("HCallbackStub", "Mirror reflection failed for " + constantName + ", trying direct reflection");

                Class<?> activityThreadClass = Class.forName("android.app.ActivityThread");
                Class<?>[] innerClasses = activityThreadClass.getDeclaredClasses();

                for (Class<?> innerClass : innerClasses) {
                    if (innerClass.getSimpleName().equals("H")) {
                        try {
                            java.lang.reflect.Field field = innerClass.getDeclaredField(constantName);
                            field.setAccessible(true);
                            Object value = field.get(null);
                            if (value instanceof Integer) {
                                android.util.Log.i("HCallbackStub", "Successfully got " + constantName + " = " + value + " via direct reflection");
                                return (Integer) value;
                            }
                        } catch (NoSuchFieldException e) {
                            android.util.Log.d("HCallbackStub", "Field " + constantName + " not found in ActivityThread.H");
                        }
                        break;
                    }
                }

            } catch (Exception e) {
                android.util.Log.e("HCallbackStub", "Error getting ActivityThread constant: " + constantName, e);
            }

            android.util.Log.w("HCallbackStub", "Using default value " + defaultValue + " for " + constantName);
            return defaultValue;
        }

        private boolean mCalling = false;


        private Handler.Callback otherCallback;

        private HCallbackStub() {
        }

        public static HCallbackStub getDefault() {
            return sCallback;
        }

        private static Handler getH() {
            return ActivityThread.mH.get(VirtualCore.mainThread());
        }

        private static Handler.Callback getHCallback() {
            try {
                Handler handler = getH();
                return mirror.android.os.Handler.mCallback.get(handler);
            } catch (Throwable e) {
                e.printStackTrace();
            }
            return null;
        }

        @Override
        public boolean handleMessage(Message msg) {
            if (!mCalling) {
                mCalling = true;
                try {
                    if (LAUNCH_ACTIVITY == msg.what) {
                        if (!handleLaunchActivity(msg)) {
                            return true;
                        }
                    } else if (CREATE_SERVICE == msg.what) {
                        if (!VClientImpl.get().isBound()) {
                            try {
                                ServiceInfo info = Reflect.on(msg.obj).get("info");
                                VClientImpl.get().bindApplication(info.packageName, info.processName);
                            } catch (Exception e) {
                                android.util.Log.e("HCallbackStub", "Failed to bind application for service", e);
                                // 继续处理，让系统尝试恢复
                            }
                        }
                    } else if (SCHEDULE_CRASH == msg.what) {
                        // to avoid the exception send from System.
                        return true;
                    }
                    if (otherCallback != null) {
                        boolean desired = otherCallback.handleMessage(msg);
                        mCalling = false;
                        return desired;
                    } else {
                        mCalling = false;
                    }
                } finally {
                    mCalling = false;
                }
            }
            return false;
        }

        private boolean handleLaunchActivity(Message msg) {
            Object r = msg.obj;
            Intent stubIntent = ActivityThread.ActivityClientRecord.intent.get(r);
            StubActivityRecord saveInstance = new StubActivityRecord(stubIntent);
            if (saveInstance.intent == null) {
                return true;
            }
            Intent intent = saveInstance.intent;
            ComponentName caller = saveInstance.caller;
            IBinder token = ActivityThread.ActivityClientRecord.token.get(r);
            ActivityInfo info = saveInstance.info;

            // 检查并修复Resources问题
            try {
                ensureResourcesAvailable(info);
            } catch (Exception e) {
                android.util.Log.e("HCallbackStub", "Failed to ensure resources for " + info.packageName, e);
                // 继续处理，让系统尝试恢复
            }

            if (VClientImpl.get().getToken() == null) {
                InstalledAppInfo installedAppInfo = VirtualCore.get().getInstalledAppInfo(info.packageName, 0);
                if(installedAppInfo == null){
                    return true;
                }
                VActivityManager.get().processRestarted(info.packageName, info.processName, saveInstance.userId);
                getH().sendMessageAtFrontOfQueue(Message.obtain(msg));
                return false;
            }
            if (!VClientImpl.get().isBound()) {
                try {
                    VClientImpl.get().bindApplication(info.packageName, info.processName);
                    getH().sendMessageAtFrontOfQueue(Message.obtain(msg));
                    return false;
                } catch (Exception e) {
                    android.util.Log.e("HCallbackStub", "Failed to bind application: " + info.packageName, e);
                    // 尝试继续处理，看看是否能恢复
                    return true;
                }
            }
            int taskId = IActivityManager.getTaskForActivity.call(
                    ActivityManagerNative.getDefault.call(),
                    token,
                    false
            );
            VActivityManager.get().onActivityCreate(ComponentUtils.toComponentName(info), caller, token, info, intent, ComponentUtils.getTaskAffinity(info), taskId, info.launchMode, info.flags);
            ClassLoader appClassLoader = VClientImpl.get().getClassLoader(info.applicationInfo);
            intent.setExtrasClassLoader(appClassLoader);
            ActivityThread.ActivityClientRecord.intent.set(r, intent);
            ActivityThread.ActivityClientRecord.activityInfo.set(r, info);
            return true;
        }

        /**
         * 确保Resources可用，修复Resources为null的问题
         */
        private void ensureResourcesAvailable(ActivityInfo info) {
            try {
                // 获取当前应用的Context
                android.app.Application app = VClientImpl.get().getCurrentApplication();
                if (app == null) {
                    android.util.Log.w("HCallbackStub", "Current application is null, cannot fix resources");
                    return;
                }

                // 检查Resources是否为null
                android.content.res.Resources resources = app.getResources();
                if (resources == null) {
                    android.util.Log.w("HCallbackStub", "Application resources is null, attempting to fix");

                    // 尝试从VirtualCore获取Resources
                    try {
                        resources = VirtualCore.get().getResources(info.packageName);
                        if (resources != null) {
                            android.util.Log.i("HCallbackStub", "Successfully got resources from VirtualCore for " + info.packageName);
                            // 这里我们无法直接设置Application的resources，但至少确保VirtualCore有正确的resources
                        }
                    } catch (Exception e) {
                        android.util.Log.w("HCallbackStub", "Failed to get resources from VirtualCore for " + info.packageName, e);
                    }
                }

                // 检查主线程的Context
                Object mainThread = VirtualCore.mainThread();
                if (mainThread != null) {
                    try {
                        android.app.Application initialApp = ActivityThread.mInitialApplication.get(mainThread);
                        if (initialApp != null) {
                            android.content.res.Resources appResources = initialApp.getResources();
                            if (appResources == null) {
                                android.util.Log.w("HCallbackStub", "Initial application resources is null");
                            } else {
                                android.util.Log.d("HCallbackStub", "Initial application resources is available");
                            }
                        }
                    } catch (Exception e) {
                        android.util.Log.w("HCallbackStub", "Failed to check initial application resources", e);
                    }
                }

            } catch (Exception e) {
                android.util.Log.e("HCallbackStub", "Error in ensureResourcesAvailable", e);
            }
        }

        @Override
        public void inject() throws Throwable {
            otherCallback = getHCallback();
            mirror.android.os.Handler.mCallback.set(getH(), this);
        }

        @Override
        public boolean isEnvBad() {
            Handler.Callback callback = getHCallback();
            boolean envBad = callback != this;
            if (callback != null && envBad) {
                VLog.d(TAG, "HCallback has bad, other callback = " + callback);
            }
            return envBad;
        }

    }
