# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 23ms
    create-ARMEABI_V7A-model 41ms
    [gap of 48ms]
  create-initial-cxx-model completed in 133ms
  [gap of 35ms]
create_cxx_tasks completed in 175ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 14ms
    create-variant-model 28ms
    create-ARMEABI_V7A-model 57ms
    [gap of 50ms]
  create-initial-cxx-model completed in 156ms
  [gap of 23ms]
create_cxx_tasks completed in 188ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 30ms
    create-variant-model 34ms
    create-ARMEABI_V7A-model 53ms
    create-X86-model 10ms
    [gap of 29ms]
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 188ms
  [gap of 48ms]
create_cxx_tasks completed in 243ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 16ms]
    create-ARM64_V8A-model 10ms
    [gap of 17ms]
    create-ARM64_V8A-model 10ms
    create-X86_64-model 14ms
  create-initial-cxx-model completed in 72ms
create_cxx_tasks completed in 76ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 15ms
    create-variant-model 27ms
    create-ARMEABI_V7A-model 41ms
    [gap of 42ms]
    create-ARM64_V8A-model 10ms
    [gap of 12ms]
  create-initial-cxx-model completed in 155ms
  [gap of 28ms]
create_cxx_tasks completed in 216ms

