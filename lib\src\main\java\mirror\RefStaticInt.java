package mirror;

import java.lang.reflect.Field;

public class RefStaticInt {
    private Field field;

    public RefStaticInt(Class<?> cls, Field field) throws NoSuchFieldException {
        try {
            this.field = cls.getDeclaredField(field.getName());
            this.field.setAccessible(true);
        } catch (NoSuchFieldException e) {
            // 增强错误日志：记录字段查找失败的详细信息
            android.util.Log.e("RefStaticInt", "Field not found: " + field.getName() +
                " in class: " + cls.getName() +
                " (API Level: " + android.os.Build.VERSION.SDK_INT + ")");
            throw e;
        } catch (Exception e) {
            android.util.Log.e("RefStaticInt", "Failed to initialize RefStaticInt for field: " + field.getName(), e);
            throw new NoSuchFieldException("Failed to initialize field: " + field.getName() + " - " + e.getMessage());
        }
    }

    public int get() {
        // 添加null检查防护
        if (this.field == null) {
            android.util.Log.w("RefStaticInt", "RefStaticInt field is null, returning default value 0");
            return 0;
        }
        try {
            return this.field.getInt(null);
        } catch (Exception e) {
            android.util.Log.w("RefStaticInt", "Failed to get static int field value", e);
            return 0;
        }
    }

    public void set(int value) {
        try {
            this.field.setInt(null, value);
        } catch (Exception e) {
            //Ignore
        }
    }
}