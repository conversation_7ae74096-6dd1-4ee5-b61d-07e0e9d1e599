D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/armeabi-v7a/objs/va++/Substrate/SubstrateHook.o: \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateHook.cpp \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/CydiaSubstrate.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateDebug.hpp \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateLog.hpp \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateARM.hpp

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/CydiaSubstrate.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateDebug.hpp:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateLog.hpp:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/Substrate/SubstrateARM.hpp:
