# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 39ms
create_cxx_tasks completed in 43ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 19ms]
    create-X86-model 10ms
  create-initial-cxx-model completed in 34ms
create_cxx_tasks completed in 37ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 48ms
    create-ARMEABI_V7A-model 14ms
    [gap of 20ms]
    create-ARM64_V8A-model 12ms
    [gap of 11ms]
  create-initial-cxx-model completed in 109ms
  [gap of 15ms]
create_cxx_tasks completed in 125ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 10ms
    create-ARMEABI_V7A-model 12ms
    [gap of 11ms]
    create-module-model 12ms
    [gap of 30ms]
  create-initial-cxx-model completed in 77ms
create_cxx_tasks completed in 80ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 20ms]
    create-module-model 26ms
    [gap of 11ms]
    create-variant-model 18ms
  create-initial-cxx-model completed in 84ms
create_cxx_tasks completed in 86ms

