{"abi": "X86", "info": {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, "cxxBuildFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx\\Release\\31t5d25u\\x86", "soFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u\\obj\\local\\x86", "soRepublishFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\ndkBuild\\release\\obj\\local\\x86", "abiPlatformVersion": 21, "variant": {"buildSystemArgumentList": [], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx", "intermediatesBaseFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates", "intermediatesFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx", "gradleModulePathName": ":lib", "moduleRootFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib", "moduleBuildFile": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build.gradle", "makeFile": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "buildSystem": "NDK_BUILD", "ndkFolder": "D:\\test\\ndk\\25.1.8937393", "ndkVersion": "25.1.8937393", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "SYSTEM", "ndkMetaPlatforms": {"min": 19, "max": 33, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "D:\\test\\ndk\\25.1.8937393\\build\\cmake\\android.toolchain.cmake", "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "D:\\test\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "D:\\test\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "D:\\test\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "D:\\test\\ndk\\25.1.8937393\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp", "sdkFolder": "D:\\test", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Release"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\.cxx\\Release\\31t5d25u\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "31t5d25u4d2v471718534t1a1t245m6cx734r3r1c1r4r392z3k441k3b46b", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.1.4.\n#   - $NDK is the path to NDK 25.1.8937393.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\nNDK_PROJECT_PATH=null\nAPP_BUILD_SCRIPT=$PROJECT/lib/src/main/jni/Android.mk\nNDK_APPLICATION_MK=$PROJECT/lib/src/main/jni/Application.mk\nAPP_ABI=$ABI\nNDK_ALL_ABIS=$ABI\nNDK_DEBUG=0\nAPP_PLATFORM=android-21\nNDK_OUT=$PROJECT/lib/build/intermediates/cxx/Release/$HASH/obj\nNDK_LIBS_OUT=$PROJECT/lib/build/intermediates/cxx/Release/$HASH/lib", "configurationArguments": ["NDK_PROJECT_PATH=null", "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk", "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk", "APP_ABI=x86", "NDK_ALL_ABIS=x86", "NDK_DEBUG=0", "APP_PLATFORM=android-21", "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u/obj", "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u/lib"], "intermediatesParentFolder": "D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u"}