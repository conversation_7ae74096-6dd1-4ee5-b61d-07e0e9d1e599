package com.lody.virtual.helper.compat;

import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageParser;
import android.content.pm.PackageParser.Activity;
import android.content.pm.PackageParser.Package;
import android.content.pm.PackageParser.Provider;
import android.content.pm.PackageParser.Service;
import android.content.pm.ProviderInfo;
import android.content.pm.ServiceInfo;
import android.os.Build;
import android.os.Process;
import android.util.DisplayMetrics;

import com.lody.virtual.client.core.VirtualCore;
import com.lody.virtual.os.VUserHandle;

import java.io.File;

import mirror.android.content.pm.PackageParserJellyBean;
import mirror.android.content.pm.PackageParserJellyBean17;
import mirror.android.content.pm.PackageParserLollipop;
import mirror.android.content.pm.PackageParserLollipop22;
import mirror.android.content.pm.PackageParserMarshmallow;
import mirror.android.content.pm.PackageParserNougat;
import mirror.android.content.pm.PackageUserState;

import static android.os.Build.VERSION_CODES.JELLY_BEAN;
import static android.os.Build.VERSION_CODES.JELLY_BEAN_MR1;
import static android.os.Build.VERSION_CODES.LOLLIPOP;
import static android.os.Build.VERSION_CODES.LOLLIPOP_MR1;
import static android.os.Build.VERSION_CODES.M;
import static android.os.Build.VERSION_CODES.N;

/**
 * <AUTHOR>
 */

public class PackageParserCompat {

    public static final int[] GIDS = VirtualCore.get().getGids();
    private static final int API_LEVEL = Build.VERSION.SDK_INT;
    private static final int myUserId = VUserHandle.getUserId(Process.myUid());
    private static final Object sUserState = API_LEVEL >= JELLY_BEAN_MR1 ? PackageUserState.ctor.newInstance() : null;


    public static PackageParser createParser(File packageFile) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.ctor.newInstance();
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.ctor.newInstance();
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.ctor.newInstance();
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.ctor.newInstance(packageFile.getAbsolutePath());
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.ctor.newInstance(packageFile.getAbsolutePath());
        } else {
            return mirror.android.content.pm.PackageParser.ctor.newInstance(packageFile.getAbsolutePath());
        }
    }

    public static Package parsePackage(PackageParser parser, File packageFile, int flags) throws Throwable {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.parsePackage.callWithException(parser, packageFile, flags);
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.parsePackage.callWithException(parser, packageFile, flags);
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.parsePackage.callWithException(parser, packageFile, flags);
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.parsePackage.callWithException(parser, packageFile, null,
                    new DisplayMetrics(), flags);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.parsePackage.callWithException(parser, packageFile, null,
                    new DisplayMetrics(), flags);
        } else {
            return mirror.android.content.pm.PackageParser.parsePackage.callWithException(parser, packageFile, null,
                    new DisplayMetrics(), flags);
        }
    }

    public static ServiceInfo generateServiceInfo(Service service, int flags) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.generateServiceInfo.call(service, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.generateServiceInfo.call(service, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.generateServiceInfo.call(service, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.generateServiceInfo.call(service, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.generateServiceInfo.call(service, flags, false, 1, myUserId);
        } else {
            return mirror.android.content.pm.PackageParser.generateServiceInfo.call(service, flags);
        }
    }

    public static ApplicationInfo generateApplicationInfo(Package p, int flags) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.generateApplicationInfo.call(p, flags, sUserState);
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.generateApplicationInfo.call(p, flags, sUserState);
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.generateApplicationInfo.call(p, flags, sUserState);
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.generateApplicationInfo.call(p, flags, sUserState);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.generateApplicationInfo.call(p, flags, false, 1);
        } else {
            return mirror.android.content.pm.PackageParser.generateApplicationInfo.call(p, flags);
        }
    }

    public static ActivityInfo generateActivityInfo(Activity activity, int flags) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.generateActivityInfo.call(activity, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.generateActivityInfo.call(activity, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.generateActivityInfo.call(activity, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.generateActivityInfo.call(activity, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.generateActivityInfo.call(activity, flags, false, 1, myUserId);
        } else {
            return mirror.android.content.pm.PackageParser.generateActivityInfo.call(activity, flags);
        }
    }

    public static ProviderInfo generateProviderInfo(Provider provider, int flags) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.generateProviderInfo.call(provider, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP_MR1) {
            return PackageParserLollipop22.generateProviderInfo.call(provider, flags, sUserState, myUserId);
        } else if (API_LEVEL >= LOLLIPOP) {
            return PackageParserLollipop.generateProviderInfo.call(provider, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.generateProviderInfo.call(provider, flags, sUserState, myUserId);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.generateProviderInfo.call(provider, flags, false, 1, myUserId);
        } else {
            return mirror.android.content.pm.PackageParser.generateProviderInfo.call(provider, flags);
        }
    }

    public static PackageInfo generatePackageInfo(Package p, int flags, long firstInstallTime, long lastUpdateTime) {
        if (API_LEVEL >= M) {
            return PackageParserMarshmallow.generatePackageInfo.call(p, GIDS, flags, firstInstallTime, lastUpdateTime,
                    null, sUserState);
        } else if (API_LEVEL >= LOLLIPOP) {
            if (PackageParserLollipop22.generatePackageInfo != null) {
                return PackageParserLollipop22.generatePackageInfo.call(p, GIDS, flags, firstInstallTime, lastUpdateTime,
                        null, sUserState);
            } else {
                return PackageParserLollipop.generatePackageInfo.call(p, GIDS, flags, firstInstallTime, lastUpdateTime,
                        null, sUserState);
            }
        } else if (API_LEVEL >= JELLY_BEAN_MR1) {
            return PackageParserJellyBean17.generatePackageInfo.call(p, GIDS, flags, firstInstallTime, lastUpdateTime,
                    null, sUserState);
        } else if (API_LEVEL >= JELLY_BEAN) {
            return PackageParserJellyBean.generatePackageInfo.call(p, GIDS, flags, firstInstallTime, lastUpdateTime,
                    null);
        } else {
            return mirror.android.content.pm.PackageParser.generatePackageInfo.call(p, GIDS, flags, firstInstallTime,
                    lastUpdateTime);
        }
    }

    public static void collectCertificates(PackageParser parser, Package p, int flags) throws Throwable {
        try {
            if (API_LEVEL >= N) {
                // 检查Nougat版本的反射方法是否可用
                if (PackageParserNougat.collectCertificates != null) {
                    PackageParserNougat.collectCertificates.callWithException(p, flags);
                } else {
                    // 降级到Marshmallow版本的实例方法
                    android.util.Log.w("PackageParserCompat", "Nougat collectCertificates not available, falling back to Marshmallow");
                    if (PackageParserMarshmallow.collectCertificates != null) {
                        PackageParserMarshmallow.collectCertificates.callWithException(parser, p, flags);
                    } else {
                        throw new RuntimeException("No available collectCertificates method found");
                    }
                }
            } else if (API_LEVEL >= M) {
                PackageParserMarshmallow.collectCertificates.callWithException(parser, p, flags);
            } else if (API_LEVEL >= LOLLIPOP_MR1) {
                PackageParserLollipop22.collectCertificates.callWithException(parser, p, flags);
            } else if (API_LEVEL >= LOLLIPOP) {
                PackageParserLollipop.collectCertificates.callWithException(parser, p, flags);
            } else if (API_LEVEL >= JELLY_BEAN_MR1) {
                PackageParserJellyBean17.collectCertificates.callWithException(parser, p, flags);
            } else if (API_LEVEL >= JELLY_BEAN) {
                PackageParserJellyBean.collectCertificates.callWithException(parser, p, flags);
            } else {
                mirror.android.content.pm.PackageParser.collectCertificates.call(parser, p, flags);
            }
        } catch (Exception e) {
            // 如果所有反射方法都失败，尝试使用备用策略
            android.util.Log.e("PackageParserCompat", "collectCertificates failed, attempting fallback", e);
            collectCertificatesFallback(parser, p, flags);
        }
    }

    /**
     * 备用证书收集策略
     * 当所有反射方法都失败时，尝试使用更通用的方法或跳过证书收集
     */
    private static void collectCertificatesFallback(PackageParser parser, Package p, int flags) {
        try {
            android.util.Log.w("PackageParserCompat", "Using fallback certificate collection strategy");

            // 尝试使用Java反射直接调用PackageParser的collectCertificates方法
            java.lang.reflect.Method method = null;
            Class<?> packageParserClass = Class.forName("android.content.pm.PackageParser");

            // 尝试不同的方法签名
            try {
                // Android 7.0+ 静态方法签名
                method = packageParserClass.getDeclaredMethod("collectCertificates",
                    Class.forName("android.content.pm.PackageParser$Package"), int.class);
                method.setAccessible(true);
                method.invoke(null, p, flags);
                android.util.Log.i("PackageParserCompat", "Successfully used static collectCertificates method");
                return;
            } catch (Exception e1) {
                android.util.Log.d("PackageParserCompat", "Static method failed: " + e1.getMessage());
            }

            try {
                // Android 6.0 及更早版本的实例方法签名
                method = packageParserClass.getDeclaredMethod("collectCertificates",
                    Class.forName("android.content.pm.PackageParser$Package"), int.class);
                method.setAccessible(true);
                method.invoke(parser, p, flags);
                android.util.Log.i("PackageParserCompat", "Successfully used instance collectCertificates method");
                return;
            } catch (Exception e2) {
                android.util.Log.d("PackageParserCompat", "Instance method failed: " + e2.getMessage());
            }

            // 如果所有方法都失败，记录警告但不抛出异常，允许安装继续
            android.util.Log.w("PackageParserCompat", "All certificate collection methods failed, proceeding without certificate validation");
            android.util.Log.w("PackageParserCompat", "Package " + p.packageName + " will be installed without signature verification");

        } catch (Exception e) {
            android.util.Log.e("PackageParserCompat", "Fallback certificate collection failed", e);
            // 不抛出异常，允许安装过程继续
        }
    }
}
