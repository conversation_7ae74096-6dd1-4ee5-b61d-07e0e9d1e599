# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 177ms
    [gap of 109ms]
    create-ARMEABI_V7A-model 28ms
    create-X86_64-model 10ms
    [gap of 16ms]
    create-ARM64_V8A-model 37ms
    create-X86-model 10ms
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 426ms
create_cxx_tasks completed in 429ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 178ms
    [gap of 75ms]
    create-ARM64_V8A-model 29ms
    [gap of 121ms]
    create-X86-model 32ms
    [gap of 118ms]
    create-ARM64_V8A-model 16ms
    create-X86-model 31ms
  create-initial-cxx-model completed in 612ms
  [gap of 11ms]
create_cxx_tasks completed in 624ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 42ms
create_cxx_tasks completed in 44ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-variant-model 12ms
    [gap of 25ms]
  create-initial-cxx-model completed in 39ms
create_cxx_tasks completed in 41ms

