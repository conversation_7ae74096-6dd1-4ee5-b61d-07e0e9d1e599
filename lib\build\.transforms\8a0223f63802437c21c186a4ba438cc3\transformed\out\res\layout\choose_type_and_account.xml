<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Customizable description text -->
    <TextView
        android:id="@+id/description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start|center_vertical"
        android:paddingBottom="16dip"
        android:paddingEnd="16dip"
        android:paddingStart="16dip"
        android:paddingTop="16dip"
        android:textAppearance="?android:attr/textAppearanceMedium" />

    <!-- List of accounts, with "Add new account" as the last item -->
    <ListView
        android:id="@android:id/list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:choiceMode="singleChoice"
        android:drawSelectorOnTop="false"
        android:scrollbarAlwaysDrawVerticalTrack="true" />

    <!-- Horizontal divider line -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dip"
        android:background="?android:attr/dividerHorizontal" />

    <!-- Alert dialog style buttons along the bottom. -->
    <LinearLayout
        android:id="@+id/button_bar"
        style="?android:attr/buttonBarStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:measureWithLargestChild="true">

        <Button
            android:id="@android:id/button1"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="onCancelButtonClicked"
            android:text="@android:string/no" />

        <Button
            android:id="@android:id/button2"
            style="?android:attr/buttonBarButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:onClick="onOkButtonClicked"
            android:text="@android:string/yes" />
    </LinearLayout>
</LinearLayout>
