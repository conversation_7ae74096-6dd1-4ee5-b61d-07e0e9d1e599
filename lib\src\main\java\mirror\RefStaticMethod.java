package mirror;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@SuppressWarnings("unchecked")
public class RefStaticMethod<T> {
    private Method method;

    public RefStaticMethod(Class<?> cls, Field field) throws NoSuchMethodException {
        if (field.isAnnotationPresent(MethodParams.class)) {
            Class<?>[] types = field.getAnnotation(MethodParams.class).value();
            for (int i = 0; i < types.length; i++) {
                Class<?> clazz = types[i];
                if (clazz.getClassLoader() == getClass().getClassLoader()) {
                    try {
                        Class.forName(clazz.getName());
                        Class<?> realClass = (Class<?>) clazz.getField("TYPE").get(null);
                        types[i] = realClass;
                    } catch (Throwable e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            this.method = cls.getDeclaredMethod(field.getName(), types);
            this.method.setAccessible(true);
        } else if (field.isAnnotationPresent(MethodReflectParams.class)) {
            boolean arrayset=false;
            String[] typeNames = field.getAnnotation(MethodReflectParams.class).value();
            Class<?>[] types = new Class<?>[typeNames.length];
            Class<?>[] types2 = new Class<?>[typeNames.length];
            for (int i = 0; i < typeNames.length; i++) {
                Class<?> type = getProtoType(typeNames[i]);
                if (type == null) {
                    try {
                        type = Class.forName(typeNames[i]);
                    } catch (ClassNotFoundException e) {
                        e.printStackTrace();
                    }
                }
                types[i] = type;
                if("java.util.HashSet".equals(typeNames[i])){
                    arrayset=true;
                    Class<?> type2 =type;
                    try {
                        type2 = Class.forName("android.util.ArraySet");
                    } catch (ClassNotFoundException e) {
                        e.printStackTrace();
                    }
                    if(type2 != null) {
                        types2[i] = type2;
                    }else{
                        types2[i] = type;
                    }
                }else{
                    types2[i] = type;
                }
            }
            try {
                this.method = cls.getDeclaredMethod(field.getName(), types);
            }catch (Exception e){
                android.util.Log.w("RefStaticMethod", "Failed to find method " + field.getName() +
                    " with original parameter types: " + java.util.Arrays.toString(types));
                if(arrayset){
                    try {
                        this.method = cls.getDeclaredMethod(field.getName(), types2);
                        android.util.Log.i("RefStaticMethod", "Successfully found method " + field.getName() +
                            " with ArraySet parameter types: " + java.util.Arrays.toString(types2));
                    } catch (Exception e2) {
                        android.util.Log.w("RefStaticMethod", "Failed to find method " + field.getName() +
                            " with ArraySet parameter types: " + java.util.Arrays.toString(types2));
                        // 尝试查找任何同名方法
                        this.method = findAnyMethodByName(cls, field.getName());
                    }
                } else {
                    // 尝试查找任何同名方法
                    this.method = findAnyMethodByName(cls, field.getName());
                }
            }
            this.method.setAccessible(true);
        } else {
            for (Method method : cls.getDeclaredMethods()) {
                if (method.getName().equals(field.getName())) {
                    this.method = method;
                    this.method.setAccessible(true);
                    break;
                }
            }
        }

        if (this.method == null) {
            // 增强错误信息：提供更详细的方法查找失败信息
            String errorMsg = "Method not found: " + field.getName() +
                " in class: " + cls.getName() +
                " (API Level: " + android.os.Build.VERSION.SDK_INT + ")";
            android.util.Log.e("RefStaticMethod", errorMsg);

            // 列出所有可用的方法以便调试
            android.util.Log.d("RefStaticMethod", "Available methods in " + cls.getName() + ":");
            for (Method method : cls.getDeclaredMethods()) {
                if (method.getName().contains(field.getName().substring(0, Math.min(field.getName().length(), 5)))) {
                    android.util.Log.d("RefStaticMethod", "  - " + method.getName() + "(" +
                        java.util.Arrays.toString(method.getParameterTypes()) + ")");
                }
            }

            throw new NoSuchMethodException(errorMsg);
        }
    }

    /**
     * 尝试查找任何同名的方法，不考虑参数类型
     */
    private Method findAnyMethodByName(Class<?> cls, String methodName) {
        Method[] methods = cls.getDeclaredMethods();

        // 首先查找完全匹配的方法名
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                android.util.Log.w("RefStaticMethod", "Found method by name only: " + methodName +
                    " with parameters: " + java.util.Arrays.toString(method.getParameterTypes()));
                return method;
            }
        }

        return null;
    }

    static Class<?> getProtoType(String typeName) {
        if (typeName.equals("int")) {
            return Integer.TYPE;
        }
        if (typeName.equals("long")) {
            return Long.TYPE;
        }
        if (typeName.equals("boolean")) {
            return Boolean.TYPE;
        }
        if (typeName.equals("byte")) {
            return Byte.TYPE;
        }
        if (typeName.equals("short")) {
            return Short.TYPE;
        }
        if (typeName.equals("char")) {
            return Character.TYPE;
        }
        if (typeName.equals("float")) {
            return Float.TYPE;
        }
        if (typeName.equals("double")) {
            return Double.TYPE;
        }
        if (typeName.equals("void")) {
            return Void.TYPE;
        }
        return null;
    }


    public T call(Object... params) {
        T obj = null;
        try {
            obj = (T) method.invoke(null, params);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }

    public T callWithException(Object... params) throws Throwable {
        // 添加null检查防护
        if (this.method == null) {
            throw new IllegalStateException("RefStaticMethod not properly initialized - method is null");
        }
        try {
            return (T) this.method.invoke(null, params);
        } catch (InvocationTargetException e) {
            if (e.getCause() != null) {
                throw e.getCause();
            }
            throw e;
        }
    }
}
