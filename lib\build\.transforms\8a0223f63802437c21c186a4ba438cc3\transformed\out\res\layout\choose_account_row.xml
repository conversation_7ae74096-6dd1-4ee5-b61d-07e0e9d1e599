<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:paddingLeft="16dp"
    android:paddingStart="16dp"
    android:paddingRight="16dp"
    android:paddingEnd="16dp"
    android:orientation="horizontal" >

   <ImageView android:id="@+id/account_row_icon"
        android:layout_width="wrap_content"
        android:layout_height="fill_parent"
        android:paddingRight="8dip"
        android:paddingEnd="8dip" />

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/account_row_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceListItem"
        android:gravity="center_vertical"
        android:minHeight="?android:listPreferredItemHeight" />

</LinearLayout>
