@echo off
"D:\\test\\ndk\\25.1.8937393\\ndk-build.cmd" ^
  "NDK_PROJECT_PATH=null" ^
  "APP_BUILD_SCRIPT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Android.mk" ^
  "NDK_APPLICATION_MK=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\src\\main\\jni\\Application.mk" ^
  "APP_ABI=x86_64" ^
  "NDK_ALL_ABIS=x86_64" ^
  "NDK_DEBUG=0" ^
  "APP_PLATFORM=android-21" ^
  "NDK_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u/obj" ^
  "NDK_LIBS_OUT=D:\\ypb_xp\\VirtualApp-master\\VirtualApp-master\\VirtualApp\\lib\\build\\intermediates\\cxx\\Release\\31t5d25u/lib" ^
  va++
