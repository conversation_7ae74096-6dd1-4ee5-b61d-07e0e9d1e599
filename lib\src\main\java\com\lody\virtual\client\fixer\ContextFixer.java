package com.lody.virtual.client.fixer;

import android.content.Context;
import android.content.ContextWrapper;
import android.os.Build;
import android.os.DropBoxManager;

import com.lody.virtual.client.core.InvocationStubManager;
import com.lody.virtual.client.core.VirtualCore;
import com.lody.virtual.client.hook.base.BinderInvocationStub;
import com.lody.virtual.client.hook.proxies.dropbox.DropBoxManagerStub;
import com.lody.virtual.client.hook.proxies.graphics.GraphicsStatsStub;
import com.lody.virtual.helper.utils.Reflect;
import com.lody.virtual.helper.utils.ReflectException;

import mirror.android.app.ContextImpl;
import mirror.android.app.ContextImplKitkat;
import mirror.android.content.ContentResolverJBMR2;

/**
 * <AUTHOR>
 */
public class ContextFixer {

    private static final String TAG = ContextFixer.class.getSimpleName();

    /**
     * Fuck AppOps
     *
     * @param context Context
     */
    public static void fixContext(Context context) {
        try {
            context.getPackageName();
        } catch (Throwable e) {
            return;
        }

        // 检查并修复Resources问题
        try {
            fixResources(context);
        } catch (Throwable e) {
            android.util.Log.e(TAG, "Failed to fix resources", e);
        }

        InvocationStubManager.getInstance().checkEnv(GraphicsStatsStub.class);
        int deep = 0;
        while (context instanceof ContextWrapper) {
            context = ((ContextWrapper) context).getBaseContext();
            deep++;
            if (deep >= 10) {
                return;
            }
        }
        ContextImpl.mPackageManager.set(context, null);
        try {
            context.getPackageManager();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        if (!VirtualCore.get().isVAppProcess()) {
            return;
        }
        DropBoxManager dm = (DropBoxManager) context.getSystemService(Context.DROPBOX_SERVICE);
        BinderInvocationStub boxBinder = InvocationStubManager.getInstance().getInvocationStub(DropBoxManagerStub.class);
        if (boxBinder != null) {
            try {
                Reflect.on(dm).set("mService", boxBinder.getProxyInterface());
            } catch (ReflectException e) {
                e.printStackTrace();
            }
        }
        String hostPkg = VirtualCore.get().getHostPkg();
        ContextImpl.mBasePackageName.set(context, hostPkg);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ContextImplKitkat.mOpPackageName.set(context, hostPkg);
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            ContentResolverJBMR2.mPackageName.set(context.getContentResolver(), hostPkg);
        }
    }

    /**
     * 修复Resources相关问题
     */
    private static void fixResources(Context context) {
        try {
            // 检查Context的Resources是否可用
            android.content.res.Resources resources = context.getResources();
            if (resources == null) {
                android.util.Log.w(TAG, "Context resources is null, attempting to fix");

                // 尝试从VirtualCore获取默认Resources
                try {
                    Context hostContext = VirtualCore.get().getContext();
                    if (hostContext != null) {
                        android.content.res.Resources hostResources = hostContext.getResources();
                        if (hostResources != null) {
                            android.util.Log.i(TAG, "Found host resources, will use as fallback");
                            // 这里我们无法直接替换Context的resources，但至少记录了问题
                        }
                    }
                } catch (Exception e) {
                    android.util.Log.w(TAG, "Failed to get host resources", e);
                }
            } else {
                // Resources存在，检查是否正常工作
                try {
                    android.content.res.Configuration config = resources.getConfiguration();
                    if (config == null) {
                        android.util.Log.w(TAG, "Resources configuration is null");
                    } else {
                        android.util.Log.d(TAG, "Resources is working properly");
                    }
                } catch (Exception e) {
                    android.util.Log.w(TAG, "Resources.getConfiguration() failed", e);

                    // 尝试重新初始化Resources
                    try {
                        initializeBackupResources(context);
                    } catch (Exception e2) {
                        android.util.Log.e(TAG, "Failed to initialize backup resources", e2);
                    }
                }
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error in fixResources", e);
        }
    }

    /**
     * 初始化备用Resources
     */
    private static void initializeBackupResources(Context context) {
        try {
            // 获取主机应用的Resources作为备用
            Context hostContext = VirtualCore.get().getContext();
            if (hostContext != null) {
                android.content.res.Resources hostResources = hostContext.getResources();
                if (hostResources != null) {
                    // 测试备用Resources是否工作
                    android.content.res.Configuration hostConfig = hostResources.getConfiguration();
                    if (hostConfig != null) {
                        android.util.Log.i(TAG, "Backup resources initialized successfully");
                    }
                }
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to initialize backup resources", e);
        }
    }

}
