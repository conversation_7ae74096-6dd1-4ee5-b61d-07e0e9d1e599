D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib\build\intermediates\cxx\Debug\3j2n2ke2/obj/local/arm64-v8a/objs/fb/lyra/lyra.o: \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/lyra/lyra.cpp \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/lyra.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iomanip \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__string \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/algorithm \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/initializer_list \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstddef \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/version \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/type_traits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstring \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/utility \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__tuple \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdint \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdint.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__debug \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iosfwd \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wchar.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/memory \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/typeinfo \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/exception \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/new \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iterator \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__functional_base \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/tuple \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdexcept \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/atomic \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__threading_support \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/chrono \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctime \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ratio \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/climits \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/functional \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bit \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdio \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/istream \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ostream \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ios \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__locale \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string_view \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwchar \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwctype \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cctype \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctype.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wctype.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/mutex \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__mutex_base \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/system_error \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__errc \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cerrno \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/locale.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/support/android/locale_bionic.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/streambuf \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/locale \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdarg \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__bsd_locale_fallbacks.h \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bitset \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__bit_reference \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iostream \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/vector \
  D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__split_buffer \
  D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/visibility.h

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/lyra.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iomanip:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__config:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__string:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/algorithm:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/initializer_list:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstddef:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/version:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/type_traits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstring:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/utility:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__tuple:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdint:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdint.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__debug:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iosfwd:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wchar.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/memory:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/typeinfo:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/exception:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdlib:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdlib.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/new:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iterator:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__functional_base:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/tuple:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdexcept:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/atomic:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__threading_support:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/chrono:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctime:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ratio:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/climits:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/limits.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/errno.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/functional:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bit:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdio:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/stdio.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/istream:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ostream:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ios:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__locale:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/string_view:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwchar:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cwctype:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cctype:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/ctype.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/wctype.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/mutex:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__mutex_base:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/system_error:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__errc:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cerrno:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/locale.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/support/android/locale_bionic.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/streambuf:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/locale:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/cstdarg:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__bsd_locale_fallbacks.h:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/bitset:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__bit_reference:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/iostream:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/vector:

D:/test/ndk/25.1.8937393/build/../sources/cxx-stl/llvm-libc++/include/__split_buffer:

D:/ypb_xp/VirtualApp-master/VirtualApp-master/VirtualApp/lib/src/main/jni/fb/include/fb/visibility.h:
