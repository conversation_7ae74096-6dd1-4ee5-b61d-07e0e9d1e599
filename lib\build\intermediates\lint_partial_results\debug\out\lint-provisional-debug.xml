<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.1.4" type="conditional_incidents">

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="77"
            column="36"
            startOffset="5123"
            endLine="77"
            endColumn="76"
            endOffset="5163"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ScopedStorage"
        severity="warning"
        message="">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="108"
            column="36"
            startOffset="7404"
            endLine="108"
            endColumn="77"
            endOffset="7445"/>
        <map>
            <entry
                name="maxSdkVersion"
                int="**********"/>
            <entry
                name="read"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="51"
            column="6"
            startOffset="3154"
            endLine="51"
            endColumn="21"
            endOffset="3169"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="71"
            column="6"
            startOffset="4648"
            endLine="71"
            endColumn="21"
            endOffset="4663"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="81"
            column="6"
            startOffset="5399"
            endLine="81"
            endColumn="21"
            endOffset="5414"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="87"
            column="6"
            startOffset="5854"
            endLine="87"
            endColumn="21"
            endOffset="5869"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="88"
            column="6"
            startOffset="5924"
            endLine="88"
            endColumn="21"
            endOffset="5939"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="89"
            column="6"
            startOffset="5994"
            endLine="89"
            endColumn="21"
            endOffset="6009"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="93"
            column="6"
            startOffset="6287"
            endLine="93"
            endColumn="21"
            endOffset="6302"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="111"
            column="6"
            startOffset="7600"
            endLine="111"
            endColumn="21"
            endOffset="7615"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedChromeOsHardware"
        severity="error"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.telephony&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.telephony"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="219"
            column="6"
            startOffset="16284"
            endLine="219"
            endColumn="21"
            endOffset="16299"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="LockedOrientationActivity"
        severity="warning"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices">
        <fix-attribute
            description="Set screenOrientation=&quot;fullSensor&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="screenOrientation"
            value="fullSensor"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="265"
            column="13"
            startOffset="18099"
            endLine="265"
            endColumn="49"
            endOffset="18135"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="LockedOrientationActivity"
        severity="warning"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices">
        <fix-attribute
            description="Set screenOrientation=&quot;fullSensor&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="screenOrientation"
            value="fullSensor"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="273"
            column="13"
            startOffset="18452"
            endLine="273"
            endColumn="49"
            endOffset="18488"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="LockedOrientationActivity"
        severity="warning"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices">
        <fix-attribute
            description="Set screenOrientation=&quot;fullSensor&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="screenOrientation"
            value="fullSensor"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="282"
            column="13"
            startOffset="18845"
            endLine="282"
            endColumn="49"
            endOffset="18881"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="LockedOrientationActivity"
        severity="warning"
        message="Expecting `android:screenOrientation=&quot;unspecified&quot;` or `&quot;fullSensor&quot;` for this activity so the user can use the application in any orientation and provide a great experience on Chrome OS devices">
        <fix-attribute
            description="Set screenOrientation=&quot;fullSensor&quot;"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="screenOrientation"
            value="fullSensor"/>
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"
            line="292"
            column="13"
            startOffset="19287"
            endLine="292"
            endColumn="49"
            endOffset="19323"/>
        <map>
            <condition library="false"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingEnd` you should probably also define `paddingStart` for right-to-left symmetry">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="16"
            column="9"
            startOffset="540"
            endLine="16"
            endColumn="27"
            endOffset="558"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlSymmetry"
        severity="warning"
        message="When you define `paddingRight` you should probably also define `paddingLeft` for right-to-left symmetry">
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="15"
            column="9"
            startOffset="504"
            endLine="15"
            endColumn="29"
            endOffset="524"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Redundant attribute `paddingRight`; already defining `paddingEnd` with `targetSdkVersion` 34">
        <fix-attribute
            description="Delete paddingRight"
            robot="true"
            independent="true"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="paddingRight"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/choose_account_row.xml"
            line="15"
            column="9"
            startOffset="504"
            endLine="15"
            endColumn="29"
            endOffset="524"/>
        <map>
            <entry
                name="applies"
                int="18"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="33"
            column="9"
            startOffset="1289"
            endLine="33"
            endColumn="34"
            endOffset="1314"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="RtlHardcoded"
        severity="warning"
        message="Consider replacing `android:layout_marginLeft` with `android:layout_marginStart=&quot;8dp&quot;` to better support right-to-left layouts">
        <fix-replace
            description="Replace with android:layout_marginStart=&quot;8dp&quot;"
            oldString="layout_marginLeft"
            replacement="layout_marginStart"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="42"
            column="19"
            startOffset="1713"
            endLine="42"
            endColumn="44"
            endOffset="1738"/>
        <map>
            <entry
                name="applies"
                int="10"/>
        </map>
    </incident>

    <incident
        id="EllipsizeMaxLines"
        severity="error"
        message="Combining `ellipsize=marquee` and `maxLines=1` can lead to crashes. Use `singleLine=true` instead.">
        <fix-composite
            description="Replace with singleLine=&quot;true&quot;"
            robot="true"
            independent="true">
            <fix-attribute
                description="Set singleLine=&quot;true&quot;"
                robot="true"
                independent="true"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="singleLine"
                value="true"/>
            <fix-attribute
                description="Delete maxLines"
                robot="true"
                independent="true"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="maxLines"/>
        </fix-composite>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="52"
            column="13"
            startOffset="2162"
            endLine="52"
            endColumn="33"
            endOffset="2182"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="51"
            column="13"
            startOffset="2121"
            endLine="51"
            endColumn="40"
            endOffset="2148"/>
        <map>
            <condition minLT="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="EllipsizeMaxLines"
        severity="error"
        message="Combining `ellipsize=marquee` and `maxLines=1` can lead to crashes. Use `singleLine=true` instead.">
        <fix-composite
            description="Replace with singleLine=&quot;true&quot;"
            robot="true"
            independent="true">
            <fix-attribute
                description="Set singleLine=&quot;true&quot;"
                robot="true"
                independent="true"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="singleLine"
                value="true"/>
            <fix-attribute
                description="Delete maxLines"
                robot="true"
                independent="true"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="maxLines"/>
        </fix-composite>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="60"
            column="13"
            startOffset="2517"
            endLine="60"
            endColumn="33"
            endOffset="2537"/>
        <location
            file="${:lib*debug*sourceProvider*0*resDir*0}/layout/resolve_list_item.xml"
            line="59"
            column="13"
            startOffset="2476"
            endLine="59"
            endColumn="40"
            endOffset="2503"/>
        <map>
            <condition minLT="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="NewApi"
        severity="warning"
        message="">
        <fix-data minSdk="fffffffffff00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/env/DeadServerException.java"
            line="25"
            column="9"
            startOffset="558"
            endLine="25"
            endColumn="14"
            endOffset="563"/>
        <map>
            <entry
                name="owner"
                string="java.lang.RuntimeException"/>
            <api-levels id="minSdk"
                value="fffffffffff00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="name"
                string="&lt;init>"/>
            <entry
                name="message"
                string="Call requires API level 24 (current min is %1$s): `new java.lang.RuntimeException`"/>
        </map>
    </incident>

    <incident
        id="MissingPermission"
        severity="error"
        message="">
        <fix-data missing="android.permission.READ_WALLPAPER_INTERNAL, android.permission.MANAGE_EXTERNAL_STORAGE" message="Missing permissions required by WallpaperManager.getDrawable: %1$s" lastApi="**********"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/fixer/ActivityFixer.java"
            line="35"
            column="27"
            startOffset="1017"
            endLine="35"
            endColumn="101"
            endOffset="1091"/>
        <map>
            <entry
                name="requirement"
                string="|android.permission.MANAGE_EXTERNAL_STORAGE,android.permission.READ_WALLPAPER_INTERNAL"/>
            <entry
                name="message"
                string="Missing permissions required by WallpaperManager.getDrawable: %1$s"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 13">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/base/BinderInvocationStub.java"
                startOffset="3180"
                endOffset="3225"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/base/BinderInvocationStub.java"
            line="113"
            column="5"
            startOffset="3180"
            endLine="113"
            endColumn="50"
            endOffset="3225"/>
        <map>
            <condition minGE="fffffffffffff000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
                startOffset="5722"
                endOffset="5762"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
            line="247"
            column="2"
            startOffset="5722"
            endLine="247"
            endColumn="42"
            endOffset="5762"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
                startOffset="6243"
                endOffset="6283"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
            line="263"
            column="2"
            startOffset="6243"
            endLine="263"
            endColumn="42"
            endOffset="6283"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
                startOffset="6664"
                endOffset="6704"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
            line="275"
            column="2"
            startOffset="6664"
            endLine="275"
            endColumn="42"
            endOffset="6704"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
                startOffset="7627"
                endOffset="7667"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
            line="312"
            column="2"
            startOffset="7627"
            endLine="312"
            endColumn="42"
            endOffset="7667"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 18">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
                startOffset="8279"
                endOffset="8325"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java"
            line="340"
            column="2"
            startOffset="8279"
            endLine="340"
            endColumn="48"
            endOffset="8325"/>
        <map>
            <condition minGE="fffffffffffe0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
                startOffset="50960"
                endOffset="51000"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1422"
            column="5"
            startOffset="50960"
            endLine="1422"
            endColumn="45"
            endOffset="51000"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1576"
            column="52"
            startOffset="56648"
            endLine="1576"
            endColumn="67"
            endOffset="56663"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/am/MethodProxies.java"
            line="1614"
            column="56"
            startOffset="58910"
            endLine="1614"
            endColumn="71"
            endOffset="58925"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/appops/AppOpsManagerStub.java"
                startOffset="594"
                endOffset="632"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/appops/AppOpsManagerStub.java"
            line="22"
            column="1"
            startOffset="594"
            endLine="22"
            endColumn="39"
            endOffset="632"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/appwidget/AppWidgetManagerStub.java"
                startOffset="418"
                endOffset="458"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/appwidget/AppWidgetManagerStub.java"
            line="17"
            column="1"
            startOffset="418"
            endLine="17"
            endColumn="41"
            endOffset="458"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/display/DisplayStub.java"
                startOffset="433"
                endOffset="479"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/display/DisplayStub.java"
            line="16"
            column="1"
            startOffset="433"
            endLine="16"
            endColumn="47"
            endOffset="479"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/fingerprint/FingerprintManagerStub.java"
                startOffset="398"
                endOffset="431"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/fingerprint/FingerprintManagerStub.java"
            line="16"
            column="1"
            startOffset="398"
            endLine="16"
            endColumn="34"
            endOffset="431"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 16">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/input/InputMethodManagerStub.java"
                startOffset="437"
                endOffset="479"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/input/InputMethodManagerStub.java"
            line="17"
            column="1"
            startOffset="437"
            endLine="17"
            endColumn="43"
            endOffset="479"/>
        <map>
            <condition minGE="ffffffffffff8000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/job/JobServiceStub.java"
                startOffset="648"
                endOffset="688"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/job/JobServiceStub.java"
            line="24"
            column="1"
            startOffset="648"
            endLine="24"
            endColumn="41"
            endOffset="688"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 16">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/media/router/MediaRouterServiceStub.java"
                startOffset="399"
                endOffset="441"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/media/router/MediaRouterServiceStub.java"
            line="16"
            column="1"
            startOffset="399"
            endLine="16"
            endColumn="43"
            endOffset="441"/>
        <map>
            <condition minGE="ffffffffffff8000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/media/session/SessionManagerStub.java"
                startOffset="370"
                endOffset="410"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/media/session/SessionManagerStub.java"
            line="15"
            column="1"
            startOffset="370"
            endLine="15"
            endColumn="41"
            endOffset="410"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/network/NetworkManagementStub.java"
                startOffset="300"
                endOffset="333"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/network/NetworkManagementStub.java"
            line="11"
            column="1"
            startOffset="300"
            endLine="11"
            endColumn="34"
            endOffset="333"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="As of Android 11, this method no longer returns information about all apps; \&#xA;see https://g.co/dev/packagevisibility for details">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/pm/MethodProxies.java"
            line="1077"
            column="76"
            startOffset="35865"
            endLine="1077"
            endColumn="96"
            endOffset="35885"/>
        <map>
            <entry
                name="queryAll"
                boolean="true"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/pm/MethodProxies.java"
                startOffset="38709"
                endOffset="38755"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/pm/MethodProxies.java"
            line="1154"
            column="5"
            startOffset="38709"
            endLine="1154"
            endColumn="51"
            endOffset="38755"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/pm/MethodProxies.java"
                startOffset="39661"
                endOffset="39699"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/pm/MethodProxies.java"
            line="1192"
            column="5"
            startOffset="39661"
            endLine="1192"
            endColumn="43"
            endOffset="39699"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/restriction/RestrictionStub.java"
                startOffset="367"
                endOffset="407"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/restriction/RestrictionStub.java"
            line="15"
            column="1"
            startOffset="367"
            endLine="15"
            endColumn="41"
            endOffset="407"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/search/SearchManagerStub.java"
                startOffset="556"
                endOffset="602"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/search/SearchManagerStub.java"
            line="21"
            column="1"
            startOffset="556"
            endLine="21"
            endColumn="47"
            endOffset="602"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 22">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/usage/UsageStatsManagerStub.java"
                startOffset="370"
                endOffset="414"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/usage/UsageStatsManagerStub.java"
            line="15"
            column="1"
            startOffset="370"
            endLine="15"
            endColumn="45"
            endOffset="414"/>
        <map>
            <condition minGE="ffffffffffe00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/user/UserManagerStub.java"
                startOffset="487"
                endOffset="533"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/hook/proxies/user/UserManagerStub.java"
            line="19"
            column="1"
            startOffset="487"
            endLine="19"
            endColumn="47"
            endOffset="533"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 15">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
                startOffset="6365"
                endOffset="6419"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="177"
            column="5"
            startOffset="6365"
            endLine="177"
            endColumn="59"
            endOffset="6419"/>
        <map>
            <condition minGE="ffffffffffffc000"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="492"
            column="61"
            startOffset="19425"
            endLine="492"
            endColumn="82"
            endOffset="19446"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="QueryPermissionsNeeded"
        severity="warning"
        message="Consider adding a `&lt;queries>` declaration to your manifest when calling this \&#xA;method; see https://g.co/dev/packagevisibility for details">
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/ResolverActivity.java"
            line="552"
            column="46"
            startOffset="22816"
            endLine="552"
            endColumn="65"
            endOffset="22835"/>
        <map>
            <entry
                name="queryAll"
                boolean="false"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/StubJob.java"
                startOffset="1072"
                endOffset="1112"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/client/stub/StubJob.java"
            line="34"
            column="1"
            startOffset="1072"
            endLine="34"
            endColumn="41"
            endOffset="1112"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/DelegateApplication64Bit.java"
                startOffset="636"
                endOffset="669"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/DelegateApplication64Bit.java"
            line="28"
            column="1"
            startOffset="636"
            endLine="28"
            endColumn="34"
            endOffset="669"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/NativeLibraryHelperCompat.java"
                startOffset="1155"
                endOffset="1195"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/NativeLibraryHelperCompat.java"
            line="41"
            column="2"
            startOffset="1155"
            endLine="41"
            endColumn="42"
            endOffset="1195"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/NativeLibraryHelperCompat.java"
                startOffset="2506"
                endOffset="2546"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/helper/compat/NativeLibraryHelperCompat.java"
            line="84"
            column="2"
            startOffset="2506"
            endLine="84"
            endColumn="42"
            endOffset="2546"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="NotificationPermission"
        severity="error"
        message="When targeting Android 13 or higher, posting a permission requires holding the `POST_NOTIFICATIONS` permission">
        <fix-data missing="android.permission.POST_NOTIFICATIONS"/>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/am/VActivityManagerService.java"
            line="632"
            column="13"
            startOffset="23952"
            endLine="632"
            endColumn="45"
            endOffset="23984"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
                startOffset="1019"
                endOffset="1059"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
            line="38"
            column="1"
            startOffset="1019"
            endLine="38"
            endColumn="41"
            endOffset="1059"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 24">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
                startOffset="11382"
                endOffset="11396"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
            line="350"
            column="5"
            startOffset="11382"
            endLine="350"
            endColumn="19"
            endOffset="11396"/>
        <map>
            <condition minGE="ffffffffff800000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
                startOffset="11941"
                endOffset="11955"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/job/VJobSchedulerService.java"
            line="366"
            column="5"
            startOffset="11941"
            endLine="366"
            endColumn="19"
            endOffset="11955"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV21.java"
                startOffset="497"
                endOffset="537"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationCompatCompatV21.java"
            line="20"
            column="1"
            startOffset="497"
            endLine="20"
            endColumn="41"
            endOffset="537"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationFixer.java"
                startOffset="2989"
                endOffset="3022"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationFixer.java"
            line="80"
            column="5"
            startOffset="2989"
            endLine="80"
            endColumn="38"
            endOffset="3022"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationFixer.java"
                startOffset="3993"
                endOffset="4033"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/notification/NotificationFixer.java"
            line="100"
            column="5"
            startOffset="3993"
            endLine="100"
            endColumn="45"
            endOffset="4033"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/FileBridge.java"
                startOffset="770"
                endOffset="810"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/FileBridge.java"
            line="26"
            column="1"
            startOffset="770"
            endLine="26"
            endColumn="41"
            endOffset="810"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/PackageHelper.java"
                startOffset="934"
                endOffset="974"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/PackageHelper.java"
            line="28"
            column="1"
            startOffset="934"
            endLine="28"
            endColumn="41"
            endOffset="974"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/PackageInstallerSession.java"
                startOffset="1076"
                endOffset="1116"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/PackageInstallerSession.java"
            line="38"
            column="1"
            startOffset="1076"
            endLine="38"
            endColumn="41"
            endOffset="1116"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/SessionParams.java"
                startOffset="457"
                endOffset="497"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/SessionParams.java"
            line="17"
            column="1"
            startOffset="457"
            endLine="17"
            endColumn="41"
            endOffset="497"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/VPackageInstallerService.java"
                startOffset="1437"
                endOffset="1477"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/installer/VPackageInstallerService.java"
            line="44"
            column="1"
            startOffset="1437"
            endLine="44"
            endColumn="41"
            endOffset="1477"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/ProviderIntentResolver.java"
                startOffset="3378"
                endOffset="3416"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/ProviderIntentResolver.java"
            line="86"
            column="5"
            startOffset="3378"
            endLine="86"
            endColumn="43"
            endOffset="3416"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/ProviderIntentResolver.java"
                startOffset="4385"
                endOffset="4423"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/ProviderIntentResolver.java"
            line="115"
            column="5"
            startOffset="4385"
            endLine="115"
            endColumn="43"
            endOffset="4423"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
                startOffset="21935"
                endOffset="21973"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/com/lody/virtual/server/pm/VPackageManagerService.java"
            line="562"
            column="5"
            startOffset="21935"
            endLine="562"
            endColumn="43"
            endOffset="21973"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobInfo.java"
                startOffset="231"
                endOffset="271"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobInfo.java"
            line="15"
            column="1"
            startOffset="231"
            endLine="15"
            endColumn="41"
            endOffset="271"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 21">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobParameters.java"
                startOffset="315"
                endOffset="355"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobParameters.java"
            line="18"
            column="1"
            startOffset="315"
            endLine="18"
            endColumn="41"
            endOffset="355"/>
        <map>
            <condition minGE="fffffffffff00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 26">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobWorkItem.java"
                startOffset="256"
                endOffset="270"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/app/job/JobWorkItem.java"
            line="12"
            column="1"
            startOffset="256"
            endLine="12"
            endColumn="15"
            endOffset="270"/>
        <map>
            <condition minGE="fffffffffe000000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/content/SyncRequest.java"
                startOffset="255"
                endOffset="293"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/content/SyncRequest.java"
            line="14"
            column="1"
            startOffset="255"
            endLine="14"
            endColumn="39"
            endOffset="293"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/graphics/drawable/Icon.java"
                startOffset="156"
                endOffset="189"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/graphics/drawable/Icon.java"
            line="9"
            column="1"
            startOffset="156"
            endLine="9"
            endColumn="34"
            endOffset="189"/>
        <map>
            <condition minGE="ffffffffffc00000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 19">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/net/wifi/WifiSsid.java"
                startOffset="178"
                endOffset="216"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/net/wifi/WifiSsid.java"
            line="13"
            column="1"
            startOffset="178"
            endLine="13"
            endColumn="39"
            endOffset="216"/>
        <map>
            <condition minGE="fffffffffffc0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/providers/Settings.java"
                startOffset="361"
                endOffset="407"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/providers/Settings.java"
            line="18"
            column="5"
            startOffset="361"
            endLine="18"
            endColumn="51"
            endOffset="407"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellIdentityCdma.java"
                startOffset="200"
                endOffset="246"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellIdentityCdma.java"
            line="14"
            column="1"
            startOffset="200"
            endLine="14"
            endColumn="47"
            endOffset="246"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellIdentityGsm.java"
                startOffset="200"
                endOffset="246"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellIdentityGsm.java"
            line="14"
            column="1"
            startOffset="200"
            endLine="14"
            endColumn="47"
            endOffset="246"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellInfoCdma.java"
                startOffset="252"
                endOffset="298"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellInfoCdma.java"
            line="15"
            column="1"
            startOffset="252"
            endLine="15"
            endColumn="47"
            endOffset="298"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellInfoGsm.java"
                startOffset="203"
                endOffset="249"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellInfoGsm.java"
            line="14"
            column="1"
            startOffset="203"
            endLine="14"
            endColumn="47"
            endOffset="249"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellSignalStrengthCdma.java"
                startOffset="200"
                endOffset="246"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellSignalStrengthCdma.java"
            line="14"
            column="1"
            startOffset="200"
            endLine="14"
            endColumn="47"
            endOffset="246"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 17">
        <fix-replace
            description="Delete @TargetApi"
            replacement="">
            <range
                file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellSignalStrengthGsm.java"
                startOffset="200"
                endOffset="246"/>
        </fix-replace>
        <location
            file="${:lib*debug*sourceProvider*0*javaDir*0}/mirror/android/telephony/CellSignalStrengthGsm.java"
            line="14"
            column="1"
            startOffset="200"
            endLine="14"
            endColumn="47"
            endOffset="246"/>
        <map>
            <condition minGE="ffffffffffff0000"/>
        </map>
    </incident>

    <incident
        id="RtlEnabled"
        severity="warning"
        message="The project references RTL attributes, but does not explicitly enable or disable RTL support with `android:supportsRtl` in the manifest">
        <location
            file="${:lib*debug*sourceProvider*0*manifest*0}"/>
        <map>
            <entry
                name="applies"
                int="2"/>
        </map>
    </incident>

</incidents>
