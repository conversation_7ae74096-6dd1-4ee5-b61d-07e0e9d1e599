<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
.options {
   padding-left: 16px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 16 errors and 218 warnings</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">Check performed at Sun Aug 03 11:34:24 CST 2025 by AGP (8.1.4)</nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#LintError"><i class="material-icons error-icon">error</i>Lint Failure (1)</a>
      <a class="mdl-navigation__link" href="#MissingPermission"><i class="material-icons error-icon">error</i>Missing Permissions (1)</a>
      <a class="mdl-navigation__link" href="#MissingSuperCall"><i class="material-icons error-icon">error</i>Missing Super Call (1)</a>
      <a class="mdl-navigation__link" href="#ScopedStorage"><i class="material-icons warning-icon">warning</i>Affected by scoped storage (2)</a>
      <a class="mdl-navigation__link" href="#AnnotateVersionCheck"><i class="material-icons warning-icon">warning</i>Annotate SDK_INT checks (1)</a>
      <a class="mdl-navigation__link" href="#DefaultLocale"><i class="material-icons warning-icon">warning</i>Implied default locale in case conversion (4)</a>
      <a class="mdl-navigation__link" href="#DiscouragedPrivateApi"><i class="material-icons warning-icon">warning</i>Using Discouraged Private API (1)</a>
      <a class="mdl-navigation__link" href="#NewApi"><i class="material-icons warning-icon">warning</i>Calling new methods on older versions (1)</a>
      <a class="mdl-navigation__link" href="#OldTargetApi"><i class="material-icons warning-icon">warning</i>Target SDK attribute is not targeting latest version (1)</a>
      <a class="mdl-navigation__link" href="#PrivateApi"><i class="material-icons warning-icon">warning</i>Using Private APIs (4)</a>
      <a class="mdl-navigation__link" href="#SdCardPath"><i class="material-icons warning-icon">warning</i>Hardcoded reference to <code>/sdcard</code> (1)</a>
      <a class="mdl-navigation__link" href="#InflateParams"><i class="material-icons warning-icon">warning</i>Layout Inflation without a Parent (1)</a>
      <a class="mdl-navigation__link" href="#ProtectedPermissions"><i class="material-icons error-icon">error</i>Using system app permission (11)</a>
      <a class="mdl-navigation__link" href="#EllipsizeMaxLines"><i class="material-icons error-icon">error</i>Combining Ellipsize and Maxlines (2)</a>
      <a class="mdl-navigation__link" href="#ParcelClassLoader"><i class="material-icons warning-icon">warning</i>Default Parcel Class Loader (1)</a>
      <a class="mdl-navigation__link" href="#DiscouragedApi"><i class="material-icons warning-icon">warning</i>Using discouraged APIs (3)</a>
      <a class="mdl-navigation__link" href="#UnsafeIntentLaunch"><i class="material-icons warning-icon">warning</i>Launched Unsafe Intent (1)</a>
      <a class="mdl-navigation__link" href="#SystemPermissionTypo"><i class="material-icons warning-icon">warning</i>Permission appears to be a standard permission with a typo (5)</a>
      <a class="mdl-navigation__link" href="#ObsoleteSdkInt"><i class="material-icons warning-icon">warning</i>Obsolete SDK_INT Version Check (136)</a>
      <a class="mdl-navigation__link" href="#StaticFieldLeak"><i class="material-icons warning-icon">warning</i>Static Field Leaks (3)</a>
      <a class="mdl-navigation__link" href="#UseCompoundDrawables"><i class="material-icons warning-icon">warning</i>Node can be replaced by a <code>TextView</code> with compound drawables (1)</a>
      <a class="mdl-navigation__link" href="#HandlerLeak"><i class="material-icons warning-icon">warning</i>Handler reference leaks (1)</a>
      <a class="mdl-navigation__link" href="#InefficientWeight"><i class="material-icons warning-icon">warning</i>Inefficient layout weight (1)</a>
      <a class="mdl-navigation__link" href="#RedundantNamespace"><i class="material-icons warning-icon">warning</i>Redundant namespace (2)</a>
      <a class="mdl-navigation__link" href="#UnusedNamespace"><i class="material-icons warning-icon">warning</i>Unused namespace (2)</a>
      <a class="mdl-navigation__link" href="#ButtonCase"><i class="material-icons warning-icon">warning</i>Cancel/OK dialog button capitalization (3)</a>
      <a class="mdl-navigation__link" href="#ContentDescription"><i class="material-icons warning-icon">warning</i>Image without <code>contentDescription</code> (36)</a>
      <a class="mdl-navigation__link" href="#HardcodedText"><i class="material-icons warning-icon">warning</i>Hardcoded text (1)</a>
      <a class="mdl-navigation__link" href="#RtlSymmetry"><i class="material-icons warning-icon">warning</i>Padding and margin symmetry (2)</a>
      <a class="mdl-navigation__link" href="#RtlHardcoded"><i class="material-icons warning-icon">warning</i>Using left/right instead of start/end attributes (3)</a>
      <a class="mdl-navigation__link" href="#RtlEnabled"><i class="material-icons warning-icon">warning</i>Using RTL attributes without enabling RTL support (1)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Lint">Lint</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#LintError">LintError</a>: Lint Failure</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Correctness">Correctness</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingPermission">MissingPermission</a>: Missing Permissions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#MissingSuperCall">MissingSuperCall</a>: Missing Super Call</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ScopedStorage">ScopedStorage</a>: Affected by scoped storage</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#AnnotateVersionCheck">AnnotateVersionCheck</a>: Annotate SDK_INT checks</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DefaultLocale">DefaultLocale</a>: Implied default locale in case conversion</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DiscouragedPrivateApi">DiscouragedPrivateApi</a>: Using Discouraged Private API</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#NewApi">NewApi</a>: Calling new methods on older versions</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#OldTargetApi">OldTargetApi</a>: Target SDK attribute is not targeting latest version</td></tr>
<tr>
<td class="countColumn">4</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#PrivateApi">PrivateApi</a>: Using Private APIs</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SdCardPath">SdCardPath</a>: Hardcoded reference to <code>/sdcard</code></td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InflateParams">InflateParams</a>: Layout Inflation without a Parent</td></tr>
<tr>
<td class="countColumn">11</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#ProtectedPermissions">ProtectedPermissions</a>: Using system app permission</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#EllipsizeMaxLines">EllipsizeMaxLines</a>: Combining Ellipsize and Maxlines</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ParcelClassLoader">ParcelClassLoader</a>: Default Parcel Class Loader</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#DiscouragedApi">DiscouragedApi</a>: Using discouraged APIs</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Security">Security</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnsafeIntentLaunch">UnsafeIntentLaunch</a>: Launched Unsafe Intent</td></tr>
<tr>
<td class="countColumn">5</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#SystemPermissionTypo">SystemPermissionTypo</a>: Permission appears to be a standard permission with a typo</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Performance">Performance</a>
</td></tr>
<tr>
<td class="countColumn">136</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ObsoleteSdkInt">ObsoleteSdkInt</a>: Obsolete SDK_INT Version Check</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#StaticFieldLeak">StaticFieldLeak</a>: Static Field Leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UseCompoundDrawables">UseCompoundDrawables</a>: Node can be replaced by a <code>TextView</code> with compound drawables</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HandlerLeak">HandlerLeak</a>: Handler reference leaks</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#InefficientWeight">InefficientWeight</a>: Inefficient layout weight</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RedundantNamespace">RedundantNamespace</a>: Redundant namespace</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#UnusedNamespace">UnusedNamespace</a>: Unused namespace</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Usability">Usability</a>
</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ButtonCase">ButtonCase</a>: Cancel/OK dialog button capitalization</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Accessibility">Accessibility</a>
</td></tr>
<tr>
<td class="countColumn">36</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#ContentDescription">ContentDescription</a>: Image without <code>contentDescription</code></td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization">Internationalization</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#HardcodedText">HardcodedText</a>: Hardcoded text</td></tr>
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Internationalization:Bidirectional Text">Internationalization:Bidirectional Text</a>
</td></tr>
<tr>
<td class="countColumn">2</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlSymmetry">RtlSymmetry</a>: Padding and margin symmetry</td></tr>
<tr>
<td class="countColumn">3</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlHardcoded">RtlHardcoded</a>: Using left/right instead of start/end attributes</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons warning-icon">warning</i>
<a href="#RtlEnabled">RtlEnabled</a>: Using RTL attributes without enabling RTL support</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (40)</a>
</td></tr>
</table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Lint"></a>
<a name="LintError"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="LintErrorCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Lint Failure</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/BroadcastSystem.java">../../src/main/java/com/lody/virtual/server/am/BroadcastSystem.java</a></span>: <span class="message">Unexpected failure during lint analysis of BroadcastSystem.java (this is a bug in lint or one of the libraries it depends on)<br/>
<br/>
<br/>
The crash seems to involve the detector <code>com.android.tools.lint.checks.UnsafeIntentLaunchDetector</code>.<br/>
You can try disabling it with something like this:<br/>
&nbsp;&nbsp;&nbsp;&nbsp;android {<br/>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lint {<br/>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;disable "UnsafeIntentLaunch"<br/>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}<br/>
&nbsp;&nbsp;&nbsp;&nbsp;}<br/>
<br/>
Stack: <code>NullPointerException:BroadcastReceiverUtils$ActionCollectorVisitor.argument(BroadcastReceiverUtils.kt:124)<br/>
&#8592;DataFlowAnalyzer.afterVisitCallExpression(DataFlowAnalyzer.kt:355)<br/>
&#8592;UCallExpression.accept(UCallExpression.kt:90)<br/>
&#8592;UVariableKt.visitContents(UVariable.kt:68)<br/>
&#8592;UVariableKt.access$visitContents(UVariable.kt:1)<br/>
&#8592;ULocalVariable.accept(UVariable.kt:123)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UDeclarationsExpression.accept(UDeclarationsExpression.kt:22)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UBlockExpression.accept(UBlockExpression.kt:21)<br/>
&#8592;UForEachExpression.accept(UForEachExpression.kt:38)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UBlockExpression.accept(UBlockExpression.kt:21)<br/>
&#8592;UMethod.accept(UMethod.kt:45)<br/>
&#8592;BroadcastReceiverUtils.checkIsProtectedReceiverAndReturnUnprotectedActions(BroadcastReceiverUtils.kt:73)<br/>
&#8592;UnsafeIntentLaunchDetector.isRuntimeReceiverProtected(UnsafeIntentLaunchDetector.kt:304)<br/>
&#8592;UnsafeIntentLaunchDetector.processRuntimeReceiver(UnsafeIntentLaunchDetector.kt:276)<br/>
&#8592;UnsafeIntentLaunchDetector.visitMethodCall(UnsafeIntentLaunchDetector.kt:206)<br/>
&#8592;UElementVisitor$DelegatingPsiVisitor.visitMethodCallExpression(UElementVisitor.kt:1082)<br/>
&#8592;UElementVisitor$DelegatingPsiVisitor.visitCallExpression(UElementVisitor.kt:1062)<br/>
&#8592;UCallExpression.accept(UCallExpression.kt:85)<br/>
&#8592;UQualifiedReferenceExpression.accept(UQualifiedReferenceExpression.kt:34)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UBlockExpression.accept(UBlockExpression.kt:21)<br/>
&#8592;UForEachExpression.accept(UForEachExpression.kt:38)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UBlockExpression.accept(UBlockExpression.kt:21)<br/>
&#8592;UMethod.accept(UMethod.kt:45)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UClass.accept(UClass.kt:64)<br/>
&#8592;ImplementationUtilsKt.acceptList(implementationUtils.kt:14)<br/>
&#8592;UFile.accept(UFile.kt:89)<br/>
&#8592;UastLintUtilsKt.acceptSourceFile(UastLintUtils.kt:826)<br/>
&#8592;UElementVisitor$visitFile$3.run(UElementVisitor.kt:267)<br/>
&#8592;LintClient.runReadAction(LintClient.kt:1700)<br/>
&#8592;LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)<br/>
&#8592;UElementVisitor.visitFile(UElementVisitor.kt:264)<br/>
&#8592;LintDriver$visitUastDetectors$1.run(LintDriver.kt:2165)<br/>
&#8592;LintClient.runReadAction(LintClient.kt:1700)<br/>
&#8592;LintDriver$LintClientWrapper.runReadAction(LintDriver.kt:2867)<br/>
&#8592;LintDriver.visitUastDetectors(LintDriver.kt:2165)<br/>
&#8592;LintDriver.visitUast(LintDriver.kt:2127)<br/>
&#8592;LintDriver.runFileDetectors(LintDriver.kt:1379)<br/>
&#8592;LintDriver.checkProject(LintDriver.kt:1144)<br/>
&#8592;LintDriver.checkProjectRoot(LintDriver.kt:615)<br/>
&#8592;LintDriver.access$checkProjectRoot(LintDriver.kt:170)<br/>
&#8592;LintDriver$analyzeOnly$1.invoke(LintDriver.kt:441)<br/>
&#8592;LintDriver$analyzeOnly$1.invoke(LintDriver.kt:438)<br/>
&#8592;LintDriver.doAnalyze(LintDriver.kt:497)<br/>
&#8592;LintDriver.analyzeOnly(LintDriver.kt:438)<br/>
&#8592;LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)<br/>
&#8592;LintCliClient$analyzeOnly$1.invoke(LintCliClient.kt:237)<br/>
&#8592;LintCliClient.run(LintCliClient.kt:279)<br/>
&#8592;LintCliClient.run$default(LintCliClient.kt:262)<br/>
&#8592;LintCliClient.analyzeOnly(LintCliClient.kt:237)<br/>
&#8592;Main.run(Main.java:1689)<br/>
&#8592;Main.run(Main.java:275)<br/>
&#8592;DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)<br/>
&#8592;Method.invoke(Method.java:580)<br/>
&#8592;AndroidLintWorkAction.invokeLintMainRunMethod(AndroidLintWorkAction.kt:98)<br/>
&#8592;AndroidLintWorkAction.runLint(AndroidLintWorkAction.kt:87)<br/>
&#8592;AndroidLintWorkAction.execute(AndroidLintWorkAction.kt:62)<br/>
&#8592;DefaultWorkerServer.execute(DefaultWorkerServer.java:63)<br/>
&#8592;NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)<br/>
&#8592;NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)<br/>
&#8592;ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)<br/>
&#8592;NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)<br/>
&#8592;AbstractWorker$1.call(AbstractWorker.java:44)<br/>
&#8592;AbstractWorker$1.call(AbstractWorker.java:41)<br/>
&#8592;DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)<br/>
&#8592;DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:199)<br/>
&#8592;DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)<br/>
&#8592;DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)<br/>
&#8592;DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:157)<br/>
&#8592;DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)<br/>
&#8592;DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)<br/>
&#8592;DefaultBuildOperationExecutor.call(DefaultBuildOperationExecutor.java:73)<br/>
&#8592;AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)<br/>
&#8592;NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)<br/>
&#8592;DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:170)<br/>
&#8592;FutureTask.run(FutureTask.java:317)<br/>
&#8592;DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:187)<br/>
&#8592;DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:120)<br/>
&#8592;DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:162)<br/>
&#8592;Factories$1.create(Factories.java:31)<br/>
&#8592;DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:264)<br/>
&#8592;DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:128)<br/>
&#8592;DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:133)<br/>
&#8592;DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:157)<br/>
&#8592;DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:126)<br/>
&#8592;Executors$RunnableAdapter.call(Executors.java:572)<br/>
&#8592;FutureTask.run(FutureTask.java:317)<br/>
&#8592;ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)<br/>
&#8592;AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:47)<br/>
&#8592;ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)<br/>
&#8592;ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)<br/>
&#8592;Thread.run(Thread.java:1583)</code><br/>
<br/>
You can run with --stacktrace or set environment variable <code>LINT_PRINT_STACKTRACE=true</code> to dump a full stacktrace to stdout.</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationLintError" style="display: none;">
This issue type represents a problem running lint itself. Examples include failure to find bytecode for source files (which means certain detectors could not be run), parsing errors in lint configuration files, etc.<br/>
<br/>
These errors are not errors in your own code, but they are shown to make it clear that some checks were not completed.<br/>To suppress this error, use the issue id "LintError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">LintError</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 10/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationLintErrorLink" onclick="reveal('explanationLintError');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="LintErrorCardLink" onclick="hideid('LintErrorCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Correctness"></a>
<a name="MissingPermission"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingPermissionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing Permissions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/fixer/ActivityFixer.java">../../src/main/java/com/lody/virtual/client/fixer/ActivityFixer.java</a>:35</span>: <span class="message">Missing permissions required by WallpaperManager.getDrawable: android.permission.MANAGE_EXTERNAL_STORAGE or android.permission.READ_WALLPAPER_INTERNAL</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>                <span class="keyword">boolean</span> showWallpaper = typedArray.getBoolean(R_Hide.styleable.Window_windowShowWallpaper.get(),
<span class="lineno"> 33 </span>                        <span class="keyword">false</span>);
<span class="lineno"> 34 </span>                <span class="keyword">if</span> (showWallpaper) {
<span class="caretline"><span class="lineno"> 35 </span>                    activity.getWindow().<span class="error">setBackgroundDrawable(WallpaperManager.getInstance(activity).getDrawable()</span>);</span>
<span class="lineno"> 36 </span>                }
<span class="lineno"> 37 </span>                typedArray.recycle();
<span class="lineno"> 38 </span>            }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingPermission" style="display: none;">
This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.<br/>
<br/>
Furthermore, for permissions that are revocable (with <code>targetSdkVersion</code> 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingPermission</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingPermissionLink" onclick="reveal('explanationMissingPermission');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingPermissionCardLink" onclick="hideid('MissingPermissionCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="MissingSuperCall"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingSuperCallCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Missing Super Call</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/StubActivity.java">../../src/main/java/com/lody/virtual/client/stub/StubActivity.java</a>:29</span>: <span class="message">Calling <code>super.onCreate</code> more than once can lead to crashes</span><br /><pre class="errorlines">
<span class="lineno">  26 </span>            <span class="keyword">super</span>.onCreate(savedInstanceState);
<span class="lineno">  27 </span>        } <span class="keyword">catch</span> (Exception e) {
<span class="lineno">  28 </span>            android.util.Log.w(<span class="string">"StubActivity"</span>, <span class="string">"Failed to create with savedInstanceState, trying with null"</span>, e);
<span class="caretline"><span class="lineno">  29 </span>            <span class="error"><span class="keyword">super</span></span>.onCreate(<span class="keyword">null</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  30 </span>        }
<span class="lineno">  31 </span>        finish();
<span class="lineno">  32 </span>  <span class="comment">// It seems that we have conflict with the other Android-Plugin-Framework.</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationMissingSuperCall" style="display: none;">
Some methods, such as <code>View#onDetachedFromWindow</code>, require that you also call the super implementation as part of your method.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingSuperCall" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">MissingSuperCall</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 9/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationMissingSuperCallLink" onclick="reveal('explanationMissingSuperCall');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingSuperCallCardLink" onclick="hideid('MissingSuperCallCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ScopedStorage"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ScopedStorageCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Affected by scoped storage</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:77</span>: <span class="message">READ_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to query or interact with MediaStore or media files on the shared storage, you should instead use one or more new storage permissions: <code>READ_MEDIA_IMAGES</code>, <code>READ_MEDIA_VIDEO</code> or <code>READ_MEDIA_AUDIO</code>.</span><br /><pre class="errorlines">
<span class="lineno">   74 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CELL_BROADCASTS"</span> />
<span class="lineno">   75 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CLIPS"</span> />
<span class="lineno">   76 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CONTACTS"</span> />
<span class="caretline"><span class="lineno">   77 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_EXTERNAL_STORAGE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   78 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_INSTALL_SESSIONS"</span> />
<span class="lineno">   79 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_PHONE_STATE"</span> />
<span class="lineno">   80 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_PROFILE"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:108</span>: <span class="message">WRITE_EXTERNAL_STORAGE is deprecated (and is not granted) when targeting Android 13+. If you need to write to shared storage, use the <code>MediaStore.createWriteRequest</code> intent.</span><br /><pre class="errorlines">
<span class="lineno">  105 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_CALL_LOG"</span> />
<span class="lineno">  106 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_CLIPS"</span> />
<span class="lineno">  107 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_CONTACTS"</span> />
<span class="caretline"><span class="lineno">  108 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_EXTERNAL_STORAGE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  109 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_PROFILE"</span> />
<span class="lineno">  110 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SETTINGS"</span> />
<span class="lineno">  111 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SMS"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationScopedStorage" style="display: none;">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
As of Android 13, if you need to query or interact with MediaStore or media files on the shared storage, you should be using instead one or more new storage permissions:<br/>
* <code>android.permission.READ_MEDIA_IMAGES</code><br/>
* <code>android.permission.READ_MEDIA_VIDEO</code><br/>
* <code>android.permission.READ_MEDIA_AUDIO</code><br/>
<br/>
and then add <code>maxSdkVersion="33"</code> to the older permission. See the developer guide for how to do this: <a href="https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions">https://developer.android.com/about/versions/13/behavior-changes-13#granular-media-permissions</a><br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://goo.gle/android-mediastore-createwriterequest">https://goo.gle/android-mediastore-createwriterequest</a>.<br/>
<br/>
To learn more, read these resources: Play policy: <a href="https://goo.gle/policy-storage-help">https://goo.gle/policy-storage-help</a> Allowable use cases: <a href="https://goo.gle/policy-storage-usecases">https://goo.gle/policy-storage-usecases</a><br/><div class="moreinfo">More info: <a href="https://goo.gle/android-storage-usecases">https://goo.gle/android-storage-usecases</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ScopedStorage</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 8/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationScopedStorageLink" onclick="reveal('explanationScopedStorage');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ScopedStorageCardLink" onclick="hideid('ScopedStorageCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="AnnotateVersionCheck"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="AnnotateVersionCheckCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Annotate SDK_INT checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java">../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java</a>:44</span>: <span class="message">This field should be annotated with <code>ChecksSdkIntAtLeast(extension=0)</code></span><br /><pre class="errorlines">
<span class="lineno">  41 </span><span class="keyword">public</span> <span class="keyword">class</span> PackageParserCompat {
<span class="lineno">  42 </span>
<span class="lineno">  43 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">int</span>[] GIDS = VirtualCore.get().getGids();
<span class="caretline"><span class="lineno">  44 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">int</span> <span class="warning">API_LEVEL</span> = Build.VERSION.SDK_INT;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  45 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">int</span> myUserId = VUserHandle.getUserId(Process.myUid());
<span class="lineno">  46 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> Object sUserState = API_LEVEL >= JELLY_BEAN_MR1 ? PackageUserState.ctor.newInstance() : <span class="keyword">null</span>;
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationAnnotateVersionCheck" style="display: none;">
Methods which perform <code>SDK_INT</code> version checks (or field constants which reflect the result of a version check) in libraries should be annotated with <code>@ChecksSdkIntAtLeast</code>. This makes it possible for lint to correctly check calls into the library later to correctly understand that problematic code which is wrapped within a call into this library is safe after all.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AnnotateVersionCheck" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">AnnotateVersionCheck</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationAnnotateVersionCheckLink" onclick="reveal('explanationAnnotateVersionCheck');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="AnnotateVersionCheckCardLink" onclick="hideid('AnnotateVersionCheckCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DefaultLocale"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DefaultLocaleCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Implied default locale in case conversion</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/location/MockLocationHelper.java">../../src/main/java/com/lody/virtual/client/hook/proxies/location/MockLocationHelper.java</a>:205</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 202 </span>        <span class="keyword">for</span> (<span class="keyword">int</span> i = <span class="number">0</span>; i &lt; checkStr.length(); i++) {
<span class="lineno"> 203 </span>            sum ^= (<span class="keyword">byte</span>) checkStr.charAt(i);
<span class="lineno"> 204 </span>        }
<span class="caretline"><span class="lineno"> 205 </span>        <span class="keyword">return</span> nema + <span class="string">"*"</span> + String.format(<span class="string">"%02X"</span>, sum).<span class="warning">toLowerCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 206 </span>    }
<span class="lineno"> 207 </span>}</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/utils/Reflect.java">../../src/main/java/com/lody/virtual/helper/utils/Reflect.java</a>:133</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 130 </span>        <span class="keyword">if</span> (length == <span class="number">0</span>) {
<span class="lineno"> 131 </span>            <span class="keyword">return</span> <span class="string">""</span>;
<span class="lineno"> 132 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (length == <span class="number">1</span>) {
<span class="caretline"><span class="lineno"> 133 </span>            <span class="keyword">return</span> string.<span class="warning">toLowerCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 134 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 135 </span>            <span class="keyword">return</span> string.substring(<span class="number">0</span>, <span class="number">1</span>).toLowerCase() + string.substring(<span class="number">1</span>);
<span class="lineno"> 136 </span>        }
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/utils/Reflect.java">../../src/main/java/com/lody/virtual/helper/utils/Reflect.java</a>:135</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toLowerCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 132 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (length == <span class="number">1</span>) {
<span class="lineno"> 133 </span>            <span class="keyword">return</span> string.toLowerCase();
<span class="lineno"> 134 </span>        } <span class="keyword">else</span> {
<span class="caretline"><span class="lineno"> 135 </span>            <span class="keyword">return</span> string.substring(<span class="number">0</span>, <span class="number">1</span>).<span class="warning">toLowerCase</span>() + string.substring(<span class="number">1</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 136 </span>        }
<span class="lineno"> 137 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/wifi/WifiManagerStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/wifi/WifiManagerStub.java</a>:194</span>: <span class="message">Implicitly using the default locale is a common source of bugs: Use <code>toUpperCase(Locale)</code> instead. For strings meant to be internal use <code>Locale.ROOT</code>, otherwise <code>Locale.getDefault()</code>.</span><br /><pre class="errorlines">
<span class="lineno"> 191 </span>              List&lt;InetAddress> addrs = Collections.list(intf.getInetAddresses());
<span class="lineno"> 192 </span>              <span class="keyword">for</span> (InetAddress addr : addrs) {
<span class="lineno"> 193 </span>                  <span class="keyword">if</span> (!addr.isLoopbackAddress()) {
<span class="caretline"><span class="lineno"> 194 </span>                      String sAddr = addr.getHostAddress().<span class="warning">toUpperCase</span>();&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 195 </span>                      <span class="keyword">boolean</span> isIPv4 = isIPv4Address(sAddr);
<span class="lineno"> 196 </span>                      <span class="keyword">if</span> (isIPv4) {
<span class="lineno"> 197 </span>                          IPInfo info = <span class="keyword">new</span> IPInfo();
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDefaultLocale" style="display: none;">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.US)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DefaultLocale</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDefaultLocaleLink" onclick="reveal('explanationDefaultLocale');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DefaultLocaleCardLink" onclick="hideid('DefaultLocaleCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DiscouragedPrivateApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DiscouragedPrivateApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using Discouraged Private API</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/natives/NativeMethods.java">../../src/main/java/com/lody/virtual/client/natives/NativeMethods.java</a>:39</span>: <span class="message">Reflective access to native_setup, which is not part of the public SDK and therefore likely to change in future Android releases</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>
<span class="lineno"> 37 </span>  gCameraMethodType = -<span class="number">1</span>;
<span class="lineno"> 38 </span>  <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 39 </span>      gCameraNativeSetup = <span class="warning">Camera.<span class="keyword">class</span>.getDeclaredMethod(<span class="string">"native_setup"</span>, Object.<span class="keyword">class</span>, <span class="keyword">int</span>.<span class="keyword">class</span>, String.<span class="keyword">class</span>)</span>;</span>
<span class="lineno"> 40 </span>      gCameraMethodType = <span class="number">1</span>;
<span class="lineno"> 41 </span>  } <span class="keyword">catch</span> (NoSuchMethodException e) {
<span class="lineno"> 42 </span>      <span class="comment">// ignore</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDiscouragedPrivateApi" style="display: none;">
Usage of restricted non-SDK interface may throw an exception at runtime. Accessing non-SDK methods or fields through reflection has a high likelihood to break your app between versions, and is being restricted to facilitate future app compatibility.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/restrictions-non-sdk-interfaces">https://developer.android.com/preview/restrictions-non-sdk-interfaces</a>
</div>To suppress this error, use the issue id "DiscouragedPrivateApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DiscouragedPrivateApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDiscouragedPrivateApiLink" onclick="reveal('explanationDiscouragedPrivateApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DiscouragedPrivateApiCardLink" onclick="hideid('DiscouragedPrivateApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="NewApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="NewApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Calling new methods on older versions</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/env/DeadServerException.java">../../src/main/java/com/lody/virtual/client/env/DeadServerException.java</a>:25</span>: <span class="message">Call requires API level 24 (current min is 21): <code>new java.lang.RuntimeException</code></span><br /><pre class="errorlines">
<span class="lineno"> 22 </span>    }
<span class="lineno"> 23 </span>
<span class="lineno"> 24 </span>    <span class="keyword">public</span> DeadServerException(String message, Throwable cause, <span class="keyword">boolean</span> enableSuppression, <span class="keyword">boolean</span> writableStackTrace) {
<span class="caretline"><span class="lineno"> 25 </span>        <span class="warning"><span class="keyword">super</span></span>(message, cause, enableSuppression, writableStackTrace);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 26 </span>    }
<span class="lineno"> 27 </span>}
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationNewApi" style="display: none;">
This check scans through all the Android API calls in the application and warns about any calls that are not available on <b>all</b> versions targeted by this application (according to its minimum SDK attribute in the manifest).<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>
<br/>
If you are deliberately setting <code>android:</code> attributes in style definitions, make sure you place this in a <code>values-v</code><i>NN</i> folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.<br/>
<br/>
Similarly, you can use tools:targetApi="11" in an XML file to indicate that the element will only be inflated in an adequate context.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">NewApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationNewApiLink" onclick="reveal('explanationNewApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="NewApiCardLink" onclick="hideid('NewApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="OldTargetApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OldTargetApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Target SDK attribute is not targeting latest version</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../build.gradle">../../build.gradle</a>:11</span>: <span class="message">Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.</span><br /><pre class="errorlines">
<span class="lineno">  8 </span>
<span class="lineno">  9 </span>    defaultConfig {
<span class="lineno"> 10 </span>        minSdk <span class="number">21</span>
<span class="caretline"><span class="lineno"> 11 </span>        <span class="warning">targetSdk <span class="number">34</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 12 </span>        versionCode <span class="number">1</span>
<span class="lineno"> 13 </span>        versionName <span class="string">"1.0"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationOldTargetApi" style="display: none;">
When your application runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">OldTargetApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationOldTargetApiLink" onclick="reveal('explanationOldTargetApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OldTargetApiCardLink" onclick="hideid('OldTargetApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="PrivateApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="PrivateApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using Private APIs</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/am/HCallbackStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/am/HCallbackStub.java</a>:70</span>: <span class="message">Accessing internal APIs via reflection is not supported and may not work on all devices or in the future</span><br /><pre class="errorlines">
<span class="lineno">  67 </span>  <span class="comment">// 如果mirror反射失败，尝试使用Java反射直接获取</span>
<span class="lineno">  68 </span>  android.util.Log.w(<span class="string">"HCallbackStub"</span>, <span class="string">"Mirror reflection failed for "</span> + constantName + <span class="string">", trying direct reflection"</span>);
<span class="lineno">  69 </span>
<span class="caretline"><span class="lineno">  70 </span>  Class&lt;?> activityThreadClass = <span class="warning">Class.forName(<span class="string">"android.app.ActivityThread"</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  71 </span>  Class&lt;?>[] innerClasses = activityThreadClass.getDeclaredClasses();
<span class="lineno">  72 </span>
<span class="lineno">  73 </span>  <span class="keyword">for</span> (Class&lt;?> innerClass : innerClasses) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java">../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java</a>:223</span>: <span class="message">Accessing internal APIs via reflection is not supported and may not work on all devices or in the future</span><br /><pre class="errorlines">
<span class="lineno"> 220 </span>  <span class="keyword">try</span> {
<span class="lineno"> 221 </span>      <span class="comment">// Android 7.0+ 静态方法签名</span>
<span class="lineno"> 222 </span>      method = packageParserClass.getDeclaredMethod(<span class="string">"collectCertificates"</span>,
<span class="caretline"><span class="lineno"> 223 </span>          <span class="warning">Class.forName(<span class="string">"android.content.pm.PackageParser$Package"</span>)</span>, <span class="keyword">int</span>.<span class="keyword">class</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 224 </span>      method.setAccessible(<span class="keyword">true</span>);
<span class="lineno"> 225 </span>      method.invoke(<span class="keyword">null</span>, p, flags);
<span class="lineno"> 226 </span>      android.util.Log.i(<span class="string">"PackageParserCompat"</span>, <span class="string">"Successfully used static collectCertificates method"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java">../../src/main/java/com/lody/virtual/helper/compat/PackageParserCompat.java</a>:235</span>: <span class="message">Accessing internal APIs via reflection is not supported and may not work on all devices or in the future</span><br /><pre class="errorlines">
<span class="lineno"> 232 </span>  <span class="keyword">try</span> {
<span class="lineno"> 233 </span>      <span class="comment">// Android 6.0 及更早版本的实例方法签名</span>
<span class="lineno"> 234 </span>      method = packageParserClass.getDeclaredMethod(<span class="string">"collectCertificates"</span>,
<span class="caretline"><span class="lineno"> 235 </span>          <span class="warning">Class.forName(<span class="string">"android.content.pm.PackageParser$Package"</span>)</span>, <span class="keyword">int</span>.<span class="keyword">class</span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 236 </span>      method.setAccessible(<span class="keyword">true</span>);
<span class="lineno"> 237 </span>      method.invoke(parser, p, flags);
<span class="lineno"> 238 </span>      android.util.Log.i(<span class="string">"PackageParserCompat"</span>, <span class="string">"Successfully used instance collectCertificates method"</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/SystemPropertiesCompat.java">../../src/main/java/com/lody/virtual/helper/compat/SystemPropertiesCompat.java</a>:16</span>: <span class="message">Accessing internal APIs via reflection is not supported and may not work on all devices or in the future</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>
<span class="lineno"> 14 </span>    <span class="keyword">private</span> <span class="keyword">static</span> Class getSystemPropertiesClass() <span class="keyword">throws</span> ClassNotFoundException {
<span class="lineno"> 15 </span>        <span class="keyword">if</span> (sClass == <span class="keyword">null</span>) {
<span class="caretline"><span class="lineno"> 16 </span>            sClass = <span class="warning">Class.forName(<span class="string">"android.os.SystemProperties"</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>        }
<span class="lineno"> 18 </span>        <span class="keyword">return</span> sClass;
<span class="lineno"> 19 </span>    }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationPrivateApi" style="display: none;">
Using reflection to access hidden/private Android APIs is not safe; it will often not work on devices from other vendors, and it may suddenly stop working (if the API is removed) or crash spectacularly (if the API behavior changes, since there are no guarantees for compatibility).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/restrictions-non-sdk-interfaces">https://developer.android.com/preview/restrictions-non-sdk-interfaces</a>
</div>To suppress this error, use the issue id "PrivateApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">PrivateApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationPrivateApiLink" onclick="reveal('explanationPrivateApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="PrivateApiCardLink" onclick="hideid('PrivateApiCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SdCardPath"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SdCardPathCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded reference to /sdcard</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/NativeEngine.java">../../src/main/java/com/lody/virtual/client/NativeEngine.java</a>:127</span>: <span class="message">Do not hardcode "<code>/data/</code>"; use <code>Context.getFilesDir().getPath()</code> instead</span><br /><pre class="errorlines">
<span class="lineno"> 124 </span>
<span class="lineno"> 125 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> enableIORedirect() {
<span class="lineno"> 126 </span>      <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 127 </span>          String soPath = String.format(<span class="warning"><span class="string">"/data/data/%s/lib/libva++.so"</span></span>, VirtualCore.get().getHostPkg());</span>
<span class="lineno"> 128 </span>          <span class="keyword">if</span> (!<span class="keyword">new</span> File(soPath).exists()) {
<span class="lineno"> 129 </span>              <span class="keyword">throw</span> <span class="keyword">new</span> RuntimeException(<span class="string">"Unable to find the so."</span>);
<span class="lineno"> 130 </span>          }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSdCardPath" style="display: none;">
Your code should not reference the <code>/sdcard</code> path directly; instead use <code>Environment.getExternalStorageDirectory().getPath()</code>.<br/>
<br/>
Similarly, do not reference the <code>/data/data/</code> path directly; it can vary in multi-user scenarios. Instead, use <code>Context.getFilesDir().getPath()</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/data-storage#filesExternal">https://developer.android.com/training/data-storage#filesExternal</a>
</div>To suppress this error, use the issue id "SdCardPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SdCardPath</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSdCardPathLink" onclick="reveal('explanationSdCardPath');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SdCardPathCardLink" onclick="hideid('SdCardPathCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InflateParams"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InflateParamsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Layout Inflation without a Parent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/ChooseAccountTypeActivity.java">../../src/main/java/com/lody/virtual/client/stub/ChooseAccountTypeActivity.java</a>:160</span>: <span class="message">Avoid passing <code>null</code> as the view root (needed to resolve layout parameters on the inflated layout's root element)</span><br /><pre class="errorlines">
<span class="lineno"> 157 </span>        ViewHolder holder;
<span class="lineno"> 158 </span>
<span class="lineno"> 159 </span>        <span class="keyword">if</span> (convertView == <span class="keyword">null</span>) {
<span class="caretline"><span class="lineno"> 160 </span>            convertView = mLayoutInflater.inflate(R.layout.choose_account_row, <span class="warning"><span class="keyword">null</span></span>);&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 161 </span>            holder = <span class="keyword">new</span> ViewHolder();
<span class="lineno"> 162 </span>            holder.text = (TextView) convertView.findViewById(R.id.account_row_text);
<span class="lineno"> 163 </span>            holder.icon = (ImageView) convertView.findViewById(R.id.account_row_icon);
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInflateParams" style="display: none;">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InflateParams</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInflateParamsLink" onclick="reveal('explanationInflateParams');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InflateParamsCardLink" onclick="hideid('InflateParamsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ProtectedPermissions"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ProtectedPermissionsCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using system app permission</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:21</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">   18 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.samsung.android.scloud.backup.lib.read"</span> />
<span class="lineno">   19 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.samsung.android.scloud.backup.lib.write"</span> />
<span class="lineno">   20 </span>
<span class="caretline"><span class="lineno">   21 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BIND_DIRECTORY_SEARCH"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   22 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.UPDATE_APP_OPS_STATS"</span> />
<span class="lineno">   23 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:22</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">   19 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.samsung.android.scloud.backup.lib.write"</span> />
<span class="lineno">   20 </span>
<span class="lineno">   21 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BIND_DIRECTORY_SEARCH"</span> />
<span class="caretline"><span class="lineno">   22 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.UPDATE_APP_OPS_STATS"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   23 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.voicemail.permission.READ_WRITE_ALL_VOICEMAIL"</span> />
<span class="lineno">   24 </span>
<span class="lineno">   25 </span>  <span class="tag">&lt;uses-permission</span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:38</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">   35 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_FINE_LOCATION"</span> />
<span class="lineno">   36 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"</span> />
<span class="lineno">   37 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="caretline"><span class="lineno">   38 </span><span class="attribute">        </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_MOCK_LOCATION"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   39 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"MockLocation"</span> />
<span class="lineno">   40 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_NETWORK_STATE"</span> />
<span class="lineno">   41 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_WIFI_STATE"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:57</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">   54 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_WIFI_MULTICAST_STATE"</span> />
<span class="lineno">   55 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_WIFI_STATE"</span> />
<span class="lineno">   56 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_WIMAX_STATE"</span> />
<span class="caretline"><span class="lineno">   57 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CLEAR_APP_CACHE"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   58 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DISABLE_KEYGUARD"</span> />
<span class="lineno">   59 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"</span> />
<span class="lineno">   60 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.EXPAND_STATUS_BAR"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:94</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">   91 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.REORDER_TASKS"</span> />
<span class="lineno">   92 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.RESTART_PACKAGES"</span> />
<span class="lineno">   93 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SEND_SMS"</span> />
<span class="caretline"><span class="lineno">   94 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SET_TIME_ZONE"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   95 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SET_WALLPAPER"</span> />
<span class="lineno">   96 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SET_WALLPAPER_HINTS"</span> />
<span class="lineno">   97 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.SUBSCRIBED_FEEDS_READ"</span> />
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ProtectedPermissionsDivLink" onclick="reveal('ProtectedPermissionsDiv');" />+ 6 More Occurrences...</button>
<div id="ProtectedPermissionsDiv" style="display: none">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:110</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  107 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_CONTACTS"</span> />
<span class="lineno">  108 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span> />
<span class="lineno">  109 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_PROFILE"</span> />
<span class="caretline"><span class="lineno">  110 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SETTINGS"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  111 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SMS"</span> />
<span class="lineno">  112 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SOCIAL_STREAM"</span> />
<span class="lineno">  113 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SYNC_SETTINGS"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:170</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  167 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.google.android.providers.gsf.permission.READ_GSERVICES"</span> />
<span class="lineno">  168 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.google.android.providers.talk.permission.READ_ONLY"</span> />
<span class="lineno">  169 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.google.android.providers.talk.permission.WRITE_ONLY"</span> />
<span class="caretline"><span class="lineno">  170 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.MOUNT_UNMOUNT_FILESYSTEMS"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  171 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_LOGS"</span> />
<span class="lineno">  172 </span>  <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="lineno">  173 </span><span class="attribute">      </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.INSTALL_PACKAGES"</span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:171</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  168 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.google.android.providers.talk.permission.READ_ONLY"</span> />
<span class="lineno">  169 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.google.android.providers.talk.permission.WRITE_ONLY"</span> />
<span class="lineno">  170 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.MOUNT_UNMOUNT_FILESYSTEMS"</span> />
<span class="caretline"><span class="lineno">  171 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_LOGS"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  172 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="lineno">  173 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.INSTALL_PACKAGES"</span>
<span class="lineno">  174 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"ProtectedPermissions"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:189</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  186 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"ProtectedPermissions"</span> />
<span class="lineno">  187 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_OWNER_DATA"</span> />
<span class="lineno">  188 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_OWNER_DATA"</span> />
<span class="caretline"><span class="lineno">  189 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_CONFIGURATION"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  190 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="lineno">  191 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DEVICE_POWER"</span>
<span class="lineno">  192 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"ProtectedPermissions"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:193</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  190 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="lineno">  191 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DEVICE_POWER"</span>
<span class="lineno">  192 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"ProtectedPermissions"</span> />
<span class="caretline"><span class="lineno">  193 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BATTERY_STATS"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  194 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_DOWNLOAD_MANAGER"</span> />
<span class="lineno">  195 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.launcher.permission.READ_SETTINGS"</span> />
<span class="lineno">  196 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.launcher.permission.WRITE_SETTINGS"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:213</span>: <span class="message">Permission is only granted to system apps</span><br /><pre class="errorlines">
<span class="lineno">  210 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"cn.nubia.launcher.permission.READ_SETTINGS"</span> />
<span class="lineno">  211 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.huawei.android.launcher.permission.READ_SETTINGS"</span> />
<span class="lineno">  212 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.huawei.android.launcher.permission.CHANGE_BADGE"</span> />
<span class="caretline"><span class="lineno">  213 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="error"><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.GET_INTENT_SENDER_INTENT"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  214 </span>    
<span class="lineno">  215 </span>    <span class="comment">&lt;!-- Required for Android 11+ to query other apps --></span>
<span class="lineno">  216 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.QUERY_ALL_PACKAGES"</span></pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationProtectedPermissions" style="display: none;">
Permissions with the protection level <code>signature</code>, <code>privileged</code> or <code>signatureOrSystem</code> are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions.<br/>To suppress this error, use the issue id "ProtectedPermissions" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ProtectedPermissions</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationProtectedPermissionsLink" onclick="reveal('explanationProtectedPermissions');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ProtectedPermissionsCardLink" onclick="hideid('ProtectedPermissionsCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="EllipsizeMaxLines"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="EllipsizeMaxLinesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Combining Ellipsize and Maxlines</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:52</span>: <span class="message">Combining <code>ellipsize=marquee</code> and <code>maxLines=1</code> can lead to crashes. Use <code>singleLine=true</code> instead.</span><br /><pre class="errorlines">
<span class="lineno"> 49 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 50 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 51 </span>            <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"marquee"</span>
<span class="caretline"><span class="lineno"> 52 </span>            <span class="error"><span class="prefix">android:</span><span class="attribute">maxLines</span>=<span class="value">"1"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 53 </span>            <span class="prefix">android:</span><span class="attribute">minLines</span>=<span class="value">"1"</span>/>
<span class="lineno"> 54 </span>        <span class="comment">&lt;!-- Extended activity info to distinguish between duplicate activity names --></span>
<span class="lineno"> 55 </span>        <span class="tag">&lt;TextView</span><span class="attribute">
</span></pre>

<ul></ul><button id="Location1DivLink" onclick="reveal('Location1Div');" />+ 1 Additional Locations...</button>
<div id="Location1Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:51</span>
</ul>
</div><br/><br/>
<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:60</span>: <span class="message">Combining <code>ellipsize=marquee</code> and <code>maxLines=1</code> can lead to crashes. Use <code>singleLine=true</code> instead.</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 58 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 59 </span>            <span class="prefix">android:</span><span class="attribute">ellipsize</span>=<span class="value">"marquee"</span>
<span class="caretline"><span class="lineno"> 60 </span>            <span class="error"><span class="prefix">android:</span><span class="attribute">maxLines</span>=<span class="value">"1"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 61 </span>            <span class="prefix">android:</span><span class="attribute">minLines</span>=<span class="value">"1"</span>
<span class="lineno"> 62 </span>            <span class="prefix">android:</span><span class="attribute">textAppearance</span>=<span class="value">"?android:attr/textAppearanceSmall"</span>/>
<span class="lineno"> 63 </span>    <span class="tag">&lt;/LinearLayout></span>
</pre>

<ul></ul><button id="Location2DivLink" onclick="reveal('Location2Div');" />+ 1 Additional Locations...</button>
<div id="Location2Div" style="display: none">
Additional locations: <ul>
<li> <span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:59</span>
</ul>
</div><br/><br/>
</div>
<div class="metadata"><div class="explanation" id="explanationEllipsizeMaxLines" style="display: none;">
Combining <code>ellipsize</code> and <code>maxLines=1</code> can lead to crashes on some devices. Earlier versions of lint recommended replacing <code>singleLine=true</code> with <code>maxLines=1</code> but that should not be done when using <code>ellipsize</code>.<br/><div class="moreinfo">More info: <a href="https://issuetracker.google.com/issues/36950033">https://issuetracker.google.com/issues/36950033</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "EllipsizeMaxLines" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">EllipsizeMaxLines</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Error</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationEllipsizeMaxLinesLink" onclick="reveal('explanationEllipsizeMaxLines');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="EllipsizeMaxLinesCardLink" onclick="hideid('EllipsizeMaxLinesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="ParcelClassLoader"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ParcelClassLoaderCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Default Parcel Class Loader</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/remote/PendingResultData.java">../../src/main/java/com/lody/virtual/remote/PendingResultData.java</a>:85</span>: <span class="message">Using the default class loader will not work if you are restoring your own classes. Consider using for example <code>readBundle(getClass().getClassLoader())</code> instead.</span><br /><pre class="errorlines">
<span class="lineno">  82 </span>        <span class="keyword">this</span>.mFlags = in.readInt();
<span class="lineno">  83 </span>        <span class="keyword">this</span>.mResultCode = in.readInt();
<span class="lineno">  84 </span>        <span class="keyword">this</span>.mResultData = in.readString();
<span class="caretline"><span class="lineno">  85 </span>        <span class="keyword">this</span>.mResultExtras = in.<span class="warning">readBundle()</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  86 </span>        <span class="keyword">this</span>.mAbortBroadcast = in.readByte() != <span class="number">0</span>;
<span class="lineno">  87 </span>        <span class="keyword">this</span>.mFinished = in.readByte() != <span class="number">0</span>;
<span class="lineno">  88 </span>    }
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationParcelClassLoader" style="display: none;">
The documentation for <code>Parcel#readParcelable(ClassLoader)</code> (and its variations) says that you can pass in <code>null</code> to pick up the default class loader. However, that ClassLoader is a system class loader and is not able to find classes in your own application.<br/>
<br/>
If you are writing your own classes into the <code>Parcel</code> (not just SDK classes like <code>String</code> and so on), then you should supply a <code>ClassLoader</code> for your application instead; a simple way to obtain one is to just call <code>getClass().getClassLoader()</code> from your own class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/os/Parcel.html">https://developer.android.com/reference/android/os/Parcel.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ParcelClassLoader" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ParcelClassLoader</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationParcelClassLoaderLink" onclick="reveal('explanationParcelClassLoader');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ParcelClassLoaderCardLink" onclick="hideid('ParcelClassLoaderCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="DiscouragedApi"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="DiscouragedApiCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using discouraged APIs</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/am/MethodProxies.java">../../src/main/java/com/lody/virtual/client/hook/proxies/am/MethodProxies.java</a>:1592</span>: <span class="message">Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. <code>R.foo.bar</code>) than by name (e.g. <code>getIdentifier("bar", "foo", null)</code>).</span><br /><pre class="errorlines">
<span class="lineno"> 1589 </span>  <span class="keyword">if</span> (icon != <span class="keyword">null</span> &amp;&amp; !TextUtils.equals(icon.packageName, getHostPkg())) {
<span class="lineno"> 1590 </span>      <span class="keyword">try</span> {
<span class="lineno"> 1591 </span>          Resources resources = VirtualCore.get().getResources(pkg);
<span class="caretline"><span class="lineno"> 1592 </span>          <span class="keyword">int</span> resId = resources.<span class="warning">getIdentifier</span>(icon.resourceName, <span class="string">"drawable"</span>, pkg);</span>
<span class="lineno"> 1593 </span>          <span class="keyword">if</span> (resId > <span class="number">0</span>) {
<span class="lineno"> 1594 </span>              <span class="comment">//noinspection deprecation</span>
<span class="lineno"> 1595 </span>              Drawable iconDrawable = resources.getDrawable(resId);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/notification/RemoteViewsFixer.java">../../src/main/java/com/lody/virtual/server/notification/RemoteViewsFixer.java</a>:260</span>: <span class="message">Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. <code>R.foo.bar</code>) than by name (e.g. <code>getIdentifier("bar", "foo", null)</code>).</span><br /><pre class="errorlines">
<span class="lineno"> 257 </span>
<span class="lineno"> 258 </span>  <span class="keyword">private</span> <span class="keyword">int</span> getDimem(Context context, Context sysContext, String name, <span class="keyword">int</span> defId) {
<span class="lineno"> 259 </span>      <span class="keyword">if</span> (sysContext != <span class="keyword">null</span>) {
<span class="caretline"><span class="lineno"> 260 </span>          <span class="keyword">int</span> id = sysContext.getResources().<span class="warning">getIdentifier</span>(name, <span class="string">"dimen"</span>, NotificationCompat.SYSTEM_UI_PKG);</span>
<span class="lineno"> 261 </span>          <span class="keyword">if</span> (id != <span class="number">0</span>) {
<span class="lineno"> 262 </span>              <span class="keyword">try</span> {
<span class="lineno"> 263 </span>                  <span class="keyword">return</span> Math.round(sysContext.getResources().getDimension(id));
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/notification/WidthCompat.java">../../src/main/java/com/lody/virtual/server/notification/WidthCompat.java</a>:131</span>: <span class="message">Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. <code>R.foo.bar</code>) than by name (e.g. <code>getIdentifier("bar", "foo", null)</code>).</span><br /><pre class="errorlines">
<span class="lineno"> 128 </span>  }
<span class="lineno"> 129 </span>
<span class="lineno"> 130 </span>  <span class="keyword">private</span> <span class="keyword">int</span> getSystemId(Context systemUi, String name, String type) {
<span class="caretline"><span class="lineno"> 131 </span>      <span class="keyword">return</span> systemUi.getResources().<span class="warning">getIdentifier</span>(name, type, NotificationCompat.SYSTEM_UI_PKG);&nbsp;</span>
<span class="lineno"> 132 </span>  }
<span class="lineno"> 133 </span>
<span class="lineno"> 134 </span>  <span class="keyword">private</span> ViewGroup createViewGroup(Context context, <span class="keyword">int</span> layoutId) {
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationDiscouragedApi" style="display: none;">
Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior).<br/>To suppress this error, use the issue id "DiscouragedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">DiscouragedApi</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Correctness</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationDiscouragedApiLink" onclick="reveal('explanationDiscouragedApi');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="DiscouragedApiCardLink" onclick="hideid('DiscouragedApiCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Security"></a>
<a name="UnsafeIntentLaunch"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnsafeIntentLaunchCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Launched Unsafe Intent</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/ShortcutHandleActivity.java">../../src/main/java/com/lody/virtual/client/stub/ShortcutHandleActivity.java</a>:34</span>: <span class="message">This intent could be coming from an untrusted source. It is later launched by an unprotected component com.lody.virtual.client.stub.ShortcutHandleActivity. You could either make the component com.lody.virtual.client.stub.ShortcutHandleActivity protected; or sanitize this intent using androidx.core.content.IntentSanitizer.</span><br /><pre class="errorlines">
<span class="lineno"> 31 </span>        Intent targetIntent = <span class="keyword">null</span>;
<span class="lineno"> 32 </span>        <span class="keyword">if</span> (splashUri != <span class="keyword">null</span>) {
<span class="lineno"> 33 </span>            <span class="keyword">try</span> {
<span class="caretline"><span class="lineno"> 34 </span>                splashIntent = <span class="warning">Intent.parseUri(splashUri, <span class="number">0</span>)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 35 </span>            } <span class="keyword">catch</span> (URISyntaxException e) {
<span class="lineno"> 36 </span>                e.printStackTrace();
<span class="lineno"> 37 </span>            }
</pre>

<ul><span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/ShortcutHandleActivity.java">../../src/main/java/com/lody/virtual/client/stub/ShortcutHandleActivity.java</a>:63</span>: <span class="message">The unsafe intent is launched here.</span><br /><pre class="errorlines">
<span class="lineno"> 60 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 61 </span>            splashIntent.putExtra(Intent.EXTRA_INTENT, targetIntent);
<span class="lineno"> 62 </span>            splashIntent.putExtra(Intent.EXTRA_CC, userId);
<span class="caretline"><span class="lineno"> 63 </span>            <span class="warning">startActivity(splashIntent)</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 64 </span>        }
<span class="lineno"> 65 </span>
<span class="lineno"> 66 </span>    }
</pre>
</ul></div>
<div class="metadata"><div class="explanation" id="explanationUnsafeIntentLaunch" style="display: none;">
Intent that potentially could come from an untrusted source should not be launched from an unprotected component without first being sanitized. See this support FAQ for details: <a href="https://support.google.com/faqs/answer/9267555">https://support.google.com/faqs/answer/9267555</a><br/>To suppress this error, use the issue id "UnsafeIntentLaunch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnsafeIntentLaunch</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnsafeIntentLaunchLink" onclick="reveal('explanationUnsafeIntentLaunch');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnsafeIntentLaunchCardLink" onclick="hideid('UnsafeIntentLaunchCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="SystemPermissionTypo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SystemPermissionTypoCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Permission appears to be a standard permission with a typo</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:42</span>: <span class="message">Did you mean <code>android.permission.ACCESS_WIFI_STATE</code>?</span><br /><pre class="errorlines">
<span class="lineno">   39 </span>        <span class="prefix">tools:</span><span class="attribute">ignore</span>=<span class="value">"MockLocation"</span> />
<span class="lineno">   40 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_NETWORK_STATE"</span> />
<span class="lineno">   41 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_WIFI_STATE"</span> />
<span class="caretline"><span class="lineno">   42 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.ACCESS_WIMAX_STATE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   43 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.AUTHENTICATE_ACCOUNTS"</span> />
<span class="lineno">   44 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute">
</span><span class="lineno">   45 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BIND_APPWIDGET"</span></pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:56</span>: <span class="message">Did you mean <code>android.permission.CHANGE_WIFI_STATE</code>?</span><br /><pre class="errorlines">
<span class="lineno">   53 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_NETWORK_STATE"</span> />
<span class="lineno">   54 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_WIFI_MULTICAST_STATE"</span> />
<span class="lineno">   55 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CHANGE_WIFI_STATE"</span> />
<span class="caretline"><span class="lineno">   56 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.CHANGE_WIMAX_STATE</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   57 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.CLEAR_APP_CACHE"</span> />
<span class="lineno">   58 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DISABLE_KEYGUARD"</span> />
<span class="lineno">   59 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.DOWNLOAD_WITHOUT_NOTIFICATION"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:75</span>: <span class="message">Did you mean <code>android.permission.READ_LOGS</code>?</span><br /><pre class="errorlines">
<span class="lineno">   72 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CALENDAR"</span> />
<span class="lineno">   73 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CALL_LOG"</span> />
<span class="lineno">   74 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CELL_BROADCASTS"</span> />
<span class="caretline"><span class="lineno">   75 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.READ_CLIPS</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">   76 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_CONTACTS"</span> />
<span class="lineno">   77 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_EXTERNAL_STORAGE"</span> />
<span class="lineno">   78 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.READ_INSTALL_SESSIONS"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:111</span>: <span class="message">Did you mean <code>android.permission.WRITE_OBB</code>?</span><br /><pre class="errorlines">
<span class="lineno">  108 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_EXTERNAL_STORAGE"</span> />
<span class="lineno">  109 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_PROFILE"</span> />
<span class="lineno">  110 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SETTINGS"</span> />
<span class="caretline"><span class="lineno">  111 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">android.permission.WRITE_SMS</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  112 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SOCIAL_STREAM"</span> />
<span class="lineno">  113 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_SYNC_SETTINGS"</span> />
<span class="lineno">  114 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.WRITE_USER_DICTIONARY"</span> />
</pre>

<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:196</span>: <span class="message">Did you mean <code>android.permission.WRITE_SETTINGS</code>?</span><br /><pre class="errorlines">
<span class="lineno">  193 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.BATTERY_STATS"</span> />
<span class="lineno">  194 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"android.permission.ACCESS_DOWNLOAD_MANAGER"</span> />
<span class="lineno">  195 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.launcher.permission.READ_SETTINGS"</span> />
<span class="caretline"><span class="lineno">  196 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"</span><span class="warning"><span class="value">com.android.launcher.permission.WRITE_SETTINGS</span></span><span class="value">"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  197 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.launcher3.permission.READ_SETTINGS"</span> />
<span class="lineno">  198 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.android.launcher2.permission.READ_SETTINGS"</span> />
<span class="lineno">  199 </span>    <span class="tag">&lt;uses-permission</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">name</span>=<span class="value">"com.teslacoilsw.launcher.permission.READ_SETTINGS"</span> />
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationSystemPermissionTypo" style="display: none;">
This check looks for required permissions that <i>look</i> like well-known system permissions or permissions from the Android SDK, but aren't, and may be typos.<br/>
<br/>
Please double check the permission value you have supplied.<br/>To suppress this error, use the issue id "SystemPermissionTypo" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">SystemPermissionTypo</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationSystemPermissionTypoLink" onclick="reveal('explanationSystemPermissionTypo');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SystemPermissionTypoCardLink" onclick="hideid('SystemPermissionTypoCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Performance"></a>
<a name="ObsoleteSdkInt"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ObsoleteSdkIntCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Obsolete SDK_INT Version Check</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/fixer/ActivityFixer.java">../../src/main/java/com/lody/virtual/client/fixer/ActivityFixer.java</a>:43</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 40 </span>            e.printStackTrace();
<span class="lineno"> 41 </span>        }
<span class="lineno"> 42 </span>
<span class="caretline"><span class="lineno"> 43 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 44 </span>            Intent intent = activity.getIntent();
<span class="lineno"> 45 </span>            ApplicationInfo applicationInfo = baseContext.getApplicationInfo();
<span class="lineno"> 46 </span>            PackageManager pm = activity.getPackageManager();
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/ActivityManagerCompat.java">../../src/main/java/com/lody/virtual/helper/compat/ActivityManagerCompat.java</a>:74</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 71 </span>            <span class="keyword">return</span> IActivityManagerN.finishActivity.call(
<span class="lineno"> 72 </span>                    ActivityManagerNative.getDefault.call(),
<span class="lineno"> 73 </span>                    token, code, data, <span class="number">0</span>);
<span class="caretline"><span class="lineno"> 74 </span>        } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 75 </span>            <span class="keyword">return</span> IActivityManagerL.finishActivity.call(
<span class="lineno"> 76 </span>                        ActivityManagerNative.getDefault.call(),
<span class="lineno"> 77 </span>                        token, code, data, <span class="keyword">false</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/ActivityStack.java">../../src/main/java/com/lody/virtual/server/am/ActivityStack.java</a>:223</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 220 </span>  <span class="keyword">if</span> (containFlags(intent, Intent.FLAG_ACTIVITY_CLEAR_TASK) &amp;&amp; !containFlags(intent, Intent.FLAG_ACTIVITY_NEW_TASK)) {
<span class="lineno"> 221 </span>      removeFlags(intent, Intent.FLAG_ACTIVITY_CLEAR_TASK);
<span class="lineno"> 222 </span>  }
<span class="caretline"><span class="lineno"> 223 </span>  <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 224 </span>      <span class="keyword">switch</span> (info.documentLaunchMode) {
<span class="lineno"> 225 </span>          <span class="keyword">case</span> ActivityInfo.DOCUMENT_LAUNCH_INTO_EXISTING:
<span class="lineno"> 226 </span>              reuseTarget = ReuseTarget.DOCUMENT;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/ActivityStack.java">../../src/main/java/com/lody/virtual/server/am/ActivityStack.java</a>:301</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 298 </span>                removeFlags(intent, Intent.FLAG_ACTIVITY_CLEAR_TASK);
<span class="lineno"> 299 </span>            }
<span class="lineno"> 300 </span>        }
<span class="caretline"><span class="lineno"> 301 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 302 </span>            <span class="keyword">switch</span> (info.documentLaunchMode) {
<span class="lineno"> 303 </span>                <span class="keyword">case</span> ActivityInfo.DOCUMENT_LAUNCH_INTO_EXISTING:
<span class="lineno"> 304 </span>                    clearTarget = ClearTarget.TASK;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/ActivityStack.java">../../src/main/java/com/lody/virtual/server/am/ActivityStack.java</a>:414</span>: <span class="message">Unnecessary; SDK_INT is never &lt; 21</span><br /><pre class="errorlines">
<span class="lineno"> 411 </span>            destIntent.addFlags(Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
<span class="lineno"> 412 </span>            destIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED);
<span class="lineno"> 413 </span>
<span class="caretline"><span class="lineno"> 414 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT &lt; Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 415 </span>                <span class="comment">// noinspection deprecation</span>
<span class="lineno"> 416 </span>                destIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET);
<span class="lineno"> 417 </span>            } <span class="keyword">else</span> {
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ObsoleteSdkIntDivLink" onclick="reveal('ObsoleteSdkIntDiv');" />+ 131 More Occurrences...</button>
<div id="ObsoleteSdkIntDiv" style="display: none">
<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/ActivityStack.java">../../src/main/java/com/lody/virtual/server/am/ActivityStack.java</a>:421</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 418 </span>                destIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_DOCUMENT);
<span class="lineno"> 419 </span>            }
<span class="lineno"> 420 </span>
<span class="caretline"><span class="lineno"> 421 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 422 </span>                VirtualCore.get().getContext().startActivity(destIntent, options);
<span class="lineno"> 423 </span>            } <span class="keyword">else</span> {
<span class="lineno"> 424 </span>                VirtualCore.get().getContext().startActivity(destIntent);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/ActivityStack.java">../../src/main/java/com/lody/virtual/server/am/ActivityStack.java</a>:507</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 504 </span>            args[optionsIndex] = options;
<span class="lineno"> 505 </span>        }
<span class="lineno"> 506 </span>        args[resolvedTypeIndex] = intent.getType();
<span class="caretline"><span class="lineno"> 507 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 508 </span>            args[intentIndex - <span class="number">1</span>] = VirtualCore.get().getHostPkg();
<span class="lineno"> 509 </span>        }
<span class="lineno"> 510 </span>        ClassUtils.fixArgs(types, args);
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/app/ActivityThread.java">../../src/main/java/mirror/android/app/ActivityThread.java</a>:52</span>: <span class="message">Unnecessary; SDK_INT is never &lt; 21</span><br /><pre class="errorlines">
<span class="lineno">  49 </span>  <span class="keyword">public</span> <span class="keyword">static</span> RefMethod&lt;Binder> getApplicationThread;
<span class="lineno">  50 </span>
<span class="lineno">  51 </span>  <span class="keyword">public</span> <span class="keyword">static</span> Object installProvider(Object mainThread, Context context, ProviderInfo providerInfo, Object holder) {
<span class="caretline"><span class="lineno">  52 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT &lt;= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  53 </span>          <span class="keyword">return</span> installProvider.call(mainThread, context, holder, providerInfo, <span class="keyword">false</span>, <span class="keyword">true</span>);
<span class="lineno">  54 </span>      }
<span class="lineno">  55 </span>      <span class="keyword">return</span> installProvider.call(mainThread, context, holder, providerInfo, <span class="keyword">false</span>, <span class="keyword">true</span>, <span class="keyword">true</span>);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/alarm/AlarmManagerStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/alarm/AlarmManagerStub.java</a>:54</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 51 </span>
<span class="lineno"> 52 </span>        <span class="annotation">@Override</span>
<span class="lineno"> 53 </span>        <span class="keyword">public</span> Object call(Object who, Method method, Object... args) <span class="keyword">throws</span> Throwable {
<span class="caretline"><span class="lineno"> 54 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 55 </span>                <span class="keyword">return</span> <span class="keyword">false</span>;
<span class="lineno"> 56 </span>            }
<span class="lineno"> 57 </span>            <span class="keyword">return</span> <span class="keyword">null</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/appops/AppOpsManagerStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/appops/AppOpsManagerStub.java</a>:22</span>: <span class="message">Unnecessary; SDK_INT is always >= 19</span><br /><pre class="errorlines">
<span class="lineno"> 19 </span><span class="javadoc"> *         Fuck the AppOpsService.
</span><span class="lineno"> 20 </span><span class="javadoc"> * @see android.app.AppOpsManager
</span><span class="lineno"> 21 </span><span class="javadoc"> */</span>
<span class="caretline"><span class="lineno"> 22 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.KITKAT)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 23 </span><span class="keyword">public</span> <span class="keyword">class</span> AppOpsManagerStub <span class="keyword">extends</span> BinderInvocationProxy {
<span class="lineno"> 24 </span>
<span class="lineno"> 25 </span>    <span class="keyword">public</span> AppOpsManagerStub() {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/appwidget/AppWidgetManagerStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/appwidget/AppWidgetManagerStub.java</a>:17</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span><span class="javadoc"> *
</span><span class="lineno"> 15 </span><span class="javadoc"> * @see android.appwidget.AppWidgetManager
</span><span class="lineno"> 16 </span><span class="javadoc"> */</span>
<span class="caretline"><span class="lineno"> 17 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span><span class="keyword">public</span> <span class="keyword">class</span> AppWidgetManagerStub <span class="keyword">extends</span> BinderInvocationProxy {
<span class="lineno"> 19 </span>
<span class="lineno"> 20 </span>    <span class="keyword">public</span> AppWidgetManagerStub() {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/base/BinderInvocationStub.java">../../src/main/java/com/lody/virtual/client/hook/base/BinderInvocationStub.java</a>:113</span>: <span class="message">Unnecessary; SDK_INT is always >= 13</span><br /><pre class="errorlines">
<span class="lineno"> 110 </span>        mBaseBinder.dump(fd, args);
<span class="lineno"> 111 </span>    }
<span class="lineno"> 112 </span>
<span class="caretline"><span class="lineno"> 113 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.HONEYCOMB_MR2)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 114 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 115 </span>    <span class="keyword">public</span> <span class="keyword">void</span> dumpAsync(FileDescriptor fd, String[] args) <span class="keyword">throws</span> RemoteException {
<span class="lineno"> 116 </span>        mBaseBinder.dumpAsync(fd, args);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/BinderProvider.java">../../src/main/java/com/lody/virtual/server/BinderProvider.java</a>:64</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno">  61 </span>        VAppManagerService.systemReady();
<span class="lineno">  62 </span>        IPCBus.register(IAppManager.<span class="keyword">class</span>, VAppManagerService.get());
<span class="lineno">  63 </span>        BroadcastSystem.attach(VActivityManagerService.get(), VAppManagerService.get());
<span class="caretline"><span class="lineno">  64 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  65 </span>            IPCBus.register(IJobService.<span class="keyword">class</span>, VJobSchedulerService.get());
<span class="lineno">  66 </span>        }
<span class="lineno">  67 </span>        VNotificationManagerService.systemReady(context);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java</a>:18</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span><span class="javadoc"> * @see android.bluetooth.BluetoothManager
</span><span class="lineno"> 16 </span><span class="javadoc"> */</span>
<span class="lineno"> 17 </span><span class="keyword">public</span> <span class="keyword">class</span> BluetoothStub <span class="keyword">extends</span> BinderInvocationProxy {
<span class="caretline"><span class="lineno"> 18 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">final</span> String SERVICE_NAME = <span class="warning">Build.VERSION.SDK_INT >= <span class="number">17</span></span> ?&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>            <span class="string">"bluetooth_manager"</span> :
<span class="lineno"> 20 </span>            <span class="string">"bluetooth"</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/bluetooth/BluetoothStub.java</a>:23</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>          <span class="string">"bluetooth"</span>;
<span class="lineno"> 21 </span>
<span class="lineno"> 22 </span>  <span class="keyword">public</span> BluetoothStub() {
<span class="caretline"><span class="lineno"> 23 </span>      <span class="keyword">super</span>(<span class="warning">Build.VERSION.SDK_INT >= <span class="number">17</span></span> ? IBluetoothManager.Stub.asInterface : IBluetooth.Stub.asInterface,</span>
<span class="lineno"> 24 </span>              SERVICE_NAME);
<span class="lineno"> 25 </span>  }
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/BundleCompat.java">../../src/main/java/com/lody/virtual/helper/compat/BundleCompat.java</a>:16</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span><span class="javadoc"> */</span>
<span class="lineno"> 14 </span><span class="keyword">public</span> <span class="keyword">class</span> BundleCompat {
<span class="lineno"> 15 </span>    <span class="keyword">public</span> <span class="keyword">static</span> IBinder getBinder(Bundle bundle, String key) {
<span class="caretline"><span class="lineno"> 16 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= <span class="number">18</span></span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>            <span class="keyword">return</span> bundle.getBinder(key);
<span class="lineno"> 18 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 19 </span>            <span class="keyword">return</span> mirror.android.os.Bundle.getIBinder.call(bundle, key);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/BundleCompat.java">../../src/main/java/com/lody/virtual/helper/compat/BundleCompat.java</a>:24</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    }
<span class="lineno"> 22 </span>
<span class="lineno"> 23 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> putBinder(Bundle bundle, String key, IBinder value) {
<span class="caretline"><span class="lineno"> 24 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= <span class="number">18</span></span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>            bundle.putBinder(key, value);
<span class="lineno"> 26 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 27 </span>            mirror.android.os.Bundle.putIBinder.call(bundle, key, value);
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellIdentityCdma.java">../../src/main/java/mirror/android/telephony/CellIdentityCdma.java</a>:14</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 12 </span><span class="javadoc"> */</span>
<span class="lineno"> 13 </span>
<span class="caretline"><span class="lineno"> 14 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> CellIdentityCdma {
<span class="lineno"> 16 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellIdentityCdma.<span class="keyword">class</span>, android.telephony.CellIdentityCdma.<span class="keyword">class</span>);
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellIdentityCdma> ctor;
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellIdentityGsm.java">../../src/main/java/mirror/android/telephony/CellIdentityGsm.java</a>:14</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 12 </span><span class="javadoc"> */</span>
<span class="lineno"> 13 </span>
<span class="caretline"><span class="lineno"> 14 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> CellIdentityGsm {
<span class="lineno"> 16 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellIdentityGsm.<span class="keyword">class</span>, android.telephony.CellIdentityGsm.<span class="keyword">class</span>);
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellIdentityGsm> ctor;
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellInfoCdma.java">../../src/main/java/mirror/android/telephony/CellInfoCdma.java</a>:15</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 13 </span><span class="javadoc"> */</span>
<span class="lineno"> 14 </span>
<span class="caretline"><span class="lineno"> 15 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span><span class="keyword">public</span> <span class="keyword">class</span> CellInfoCdma {
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellInfoCdma.<span class="keyword">class</span>, android.telephony.CellInfoCdma.<span class="keyword">class</span>);
<span class="lineno"> 18 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellInfoCdma> ctor;
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellInfoGsm.java">../../src/main/java/mirror/android/telephony/CellInfoGsm.java</a>:14</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 12 </span><span class="javadoc"> */</span>
<span class="lineno"> 13 </span>
<span class="caretline"><span class="lineno"> 14 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> CellInfoGsm {
<span class="lineno"> 16 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellInfoGsm.<span class="keyword">class</span>, android.telephony.CellInfoGsm.<span class="keyword">class</span>);
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellInfoGsm> ctor;
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellSignalStrengthCdma.java">../../src/main/java/mirror/android/telephony/CellSignalStrengthCdma.java</a>:14</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 12 </span><span class="javadoc"> */</span>
<span class="lineno"> 13 </span>
<span class="caretline"><span class="lineno"> 14 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> CellSignalStrengthCdma {
<span class="lineno"> 16 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellSignalStrengthCdma.<span class="keyword">class</span>, android.telephony.CellSignalStrengthCdma.<span class="keyword">class</span>);
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellSignalStrengthCdma> ctor;
</pre>

<span class="location"><a href="../../src/main/java/mirror/android/telephony/CellSignalStrengthGsm.java">../../src/main/java/mirror/android/telephony/CellSignalStrengthGsm.java</a>:14</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 11 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 12 </span><span class="javadoc"> */</span>
<span class="lineno"> 13 </span>
<span class="caretline"><span class="lineno"> 14 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> CellSignalStrengthGsm {
<span class="lineno"> 16 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Class&lt;?> TYPE = RefClass.load(CellSignalStrengthGsm.<span class="keyword">class</span>, android.telephony.CellSignalStrengthGsm.<span class="keyword">class</span>);
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> RefConstructor&lt;android.telephony.CellSignalStrengthGsm> ctor;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/ChooseTypeAndAccountActivity.java">../../src/main/java/com/lody/virtual/client/stub/ChooseTypeAndAccountActivity.java</a>:354</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 351 </span><span class="javadoc">     * useless.
</span><span class="lineno"> 352 </span><span class="javadoc">     */</span>
<span class="lineno"> 353 </span>    <span class="keyword">private</span> <span class="keyword">void</span> setNonLabelThemeAndCallSuperCreate(Bundle savedInstanceState) {
<span class="caretline"><span class="lineno"> 354 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 355 </span>            setTheme(android.R.style.Theme_Material_Light_Dialog_NoActionBar);
<span class="lineno"> 356 </span>        } <span class="keyword">else</span> {
<span class="lineno"> 357 </span>            setTheme(android.R.style.Theme_Holo_Light_Dialog_NoActionBar);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/clipboard/ClipBoardStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/clipboard/ClipBoardStub.java</a>:39</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>    <span class="keyword">protected</span> <span class="keyword">void</span> onBindMethods() {
<span class="lineno"> 37 </span>        <span class="keyword">super</span>.onBindMethods();
<span class="lineno"> 38 </span>        addMethodProxy(<span class="keyword">new</span> ReplaceLastPkgMethodProxy(<span class="string">"getPrimaryClip"</span>));
<span class="caretline"><span class="lineno"> 39 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT > Build.VERSION_CODES.JELLY_BEAN_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>            addMethodProxy(<span class="keyword">new</span> ReplaceLastPkgMethodProxy(<span class="string">"setPrimaryClip"</span>));
<span class="lineno"> 41 </span>            addMethodProxy(<span class="keyword">new</span> ReplaceLastPkgMethodProxy(<span class="string">"getPrimaryClipDescription"</span>));
<span class="lineno"> 42 </span>            addMethodProxy(<span class="keyword">new</span> ReplaceLastPkgMethodProxy(<span class="string">"hasPrimaryClip"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java">../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java</a>:18</span>: <span class="message">Unnecessary; SDK_INT is never &lt; 21</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span><span class="keyword">public</span> <span class="keyword">class</span> ContentProviderCompat {
<span class="lineno"> 16 </span>
<span class="lineno"> 17 </span>    <span class="keyword">public</span> <span class="keyword">static</span> Bundle call(Context context, Uri uri, String method, String arg, Bundle extras) {
<span class="caretline"><span class="lineno"> 18 </span>        <span class="keyword">if</span> (<span class="warning">VERSION.SDK_INT &lt; Build.VERSION_CODES.JELLY_BEAN_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>            <span class="keyword">return</span> context.getContentResolver().call(uri, method, arg, extras);
<span class="lineno"> 20 </span>        }
<span class="lineno"> 21 </span>        ContentProviderClient client = crazyAcquireContentProvider(context, uri);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java">../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java</a>:35</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span><span class="lineno"> 33 </span>
<span class="lineno"> 34 </span>  <span class="keyword">private</span> <span class="keyword">static</span> ContentProviderClient acquireContentProviderClient(Context context, Uri uri) {
<span class="caretline"><span class="lineno"> 35 </span>      <span class="keyword">if</span> (<span class="warning">VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>          <span class="keyword">return</span> context.getContentResolver().acquireUnstableContentProviderClient(uri);
<span class="lineno"> 37 </span>      }
<span class="lineno"> 38 </span>      <span class="keyword">return</span> context.getContentResolver().acquireContentProviderClient(uri);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java">../../src/main/java/com/lody/virtual/helper/compat/ContentProviderCompat.java</a>:68</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 65 </span>  }
<span class="lineno"> 66 </span>
<span class="lineno"> 67 </span>  <span class="keyword">private</span> <span class="keyword">static</span> ContentProviderClient acquireContentProviderClient(Context context, String name) {
<span class="caretline"><span class="lineno"> 68 </span>      <span class="keyword">if</span> (<span class="warning">VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 69 </span>          <span class="keyword">return</span> context.getContentResolver().acquireUnstableContentProviderClient(name);
<span class="lineno"> 70 </span>      }
<span class="lineno"> 71 </span>      <span class="keyword">return</span> context.getContentResolver().acquireContentProviderClient(name);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/fixer/ContextFixer.java">../../src/main/java/com/lody/virtual/client/fixer/ContextFixer.java</a>:67</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 64 </span>        }
<span class="lineno"> 65 </span>        String hostPkg = VirtualCore.get().getHostPkg();
<span class="lineno"> 66 </span>        ContextImpl.mBasePackageName.set(context, hostPkg);
<span class="caretline"><span class="lineno"> 67 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 68 </span>            ContextImplKitkat.mOpPackageName.set(context, hostPkg);
<span class="lineno"> 69 </span>        }
<span class="lineno"> 70 </span>        <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/fixer/ContextFixer.java">../../src/main/java/com/lody/virtual/client/fixer/ContextFixer.java</a>:70</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 67 </span>        <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
<span class="lineno"> 68 </span>            ContextImplKitkat.mOpPackageName.set(context, hostPkg);
<span class="lineno"> 69 </span>        }
<span class="caretline"><span class="lineno"> 70 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 71 </span>            ContentResolverJBMR2.mPackageName.set(context.getContentResolver(), hostPkg);
<span class="lineno"> 72 </span>        }
<span class="lineno"> 73 </span>    }
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/display/DisplayStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/display/DisplayStub.java</a>:16</span>: <span class="message">Unnecessary; SDK_INT is always >= 17</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span><span class="javadoc">/**
</span><span class="lineno"> 14 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 15 </span><span class="javadoc"> */</span>
<span class="caretline"><span class="lineno"> 16 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR1)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span><span class="keyword">public</span> <span class="keyword">class</span> DisplayStub <span class="keyword">extends</span> MethodInvocationProxy&lt;MethodInvocationStub&lt;IInterface>> {
<span class="lineno"> 18 </span>    <span class="keyword">public</span> DisplayStub() {
<span class="lineno"> 19 </span>        <span class="keyword">super</span>(<span class="keyword">new</span> MethodInvocationStub&lt;IInterface>(
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/pm/installer/FileBridge.java">../../src/main/java/com/lody/virtual/server/pm/installer/FileBridge.java</a>:26</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno">  23 </span><span class="javadoc"> * server side needs to strongly assert that a client side is completely
</span><span class="lineno">  24 </span><span class="javadoc"> * hands-off.
</span><span class="lineno">  25 </span><span class="javadoc"> */</span>
<span class="caretline"><span class="lineno">  26 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  27 </span><span class="keyword">public</span> <span class="keyword">class</span> FileBridge <span class="keyword">extends</span> Thread {
<span class="lineno">  28 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> String TAG = <span class="string">"FileBridge"</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/utils/FileUtils.java">../../src/main/java/com/lody/virtual/helper/utils/FileUtils.java</a>:36</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno">  33 </span><span class="javadoc">     * @throws Exception
</span><span class="lineno">  34 </span><span class="javadoc">     */</span>
<span class="lineno">  35 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> chmod(String path, <span class="keyword">int</span> mode) <span class="keyword">throws</span> Exception {
<span class="caretline"><span class="lineno">  36 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>            <span class="keyword">try</span> {
<span class="lineno">  38 </span>                Os.chmod(path, mode);
<span class="lineno">  39 </span>                <span class="keyword">return</span>;
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/utils/FileUtils.java">../../src/main/java/com/lody/virtual/helper/utils/FileUtils.java</a>:55</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno">  52 </span>    }
<span class="lineno">  53 </span>
<span class="lineno">  54 </span>    <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> createSymlink(String oldPath, String newPath) <span class="keyword">throws</span> Exception {
<span class="caretline"><span class="lineno">  55 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  56 </span>            Os.symlink(oldPath, newPath);
<span class="lineno">  57 </span>        } <span class="keyword">else</span> {
<span class="lineno">  58 </span>            Runtime.getRuntime().exec(<span class="string">"ln -s "</span> + oldPath + <span class="string">" "</span> + newPath).waitFor();
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java">../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java</a>:29</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> scheduleCreateService(IInterface appThread, IBinder token, ServiceInfo info,
<span class="lineno"> 27 </span>                                           <span class="keyword">int</span> processState) <span class="keyword">throws</span> RemoteException {
<span class="lineno"> 28 </span>
<span class="caretline"><span class="lineno"> 29 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 30 </span>          IApplicationThreadKitkat.scheduleCreateService.call(appThread, token, info, CompatibilityInfo.DEFAULT_COMPATIBILITY_INFO.get(),
<span class="lineno"> 31 </span>                  processState);
<span class="lineno"> 32 </span>      } <span class="keyword">else</span> <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java">../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java</a>:32</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 29 </span>  <span class="keyword">if</span> (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
<span class="lineno"> 30 </span>      IApplicationThreadKitkat.scheduleCreateService.call(appThread, token, info, CompatibilityInfo.DEFAULT_COMPATIBILITY_INFO.get(),
<span class="lineno"> 31 </span>              processState);
<span class="caretline"><span class="lineno"> 32 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 33 </span>      IApplicationThreadICSMR1.scheduleCreateService.call(appThread, token, info, CompatibilityInfo.DEFAULT_COMPATIBILITY_INFO.get());
<span class="lineno"> 34 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 35 </span>      IApplicationThread.scheduleCreateService.call(appThread, token, info);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java">../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java</a>:42</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>
<span class="lineno"> 40 </span>  <span class="keyword">public</span> <span class="keyword">static</span> <span class="keyword">void</span> scheduleBindService(IInterface appThread, IBinder token, Intent intent, <span class="keyword">boolean</span> rebind,
<span class="lineno"> 41 </span>                                         <span class="keyword">int</span> processState) <span class="keyword">throws</span> RemoteException {
<span class="caretline"><span class="lineno"> 42 </span>      <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>          IApplicationThreadKitkat.scheduleBindService.call(appThread, token, intent, rebind, processState);
<span class="lineno"> 44 </span>      } <span class="keyword">else</span> {
<span class="lineno"> 45 </span>          IApplicationThread.scheduleBindService.call(appThread, token, intent, rebind);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java">../../src/main/java/com/lody/virtual/helper/compat/IApplicationThreadCompat.java</a>:61</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 58 </span>      Object serviceStartArg = ServiceStartArgs.ctor.newInstance(taskRemoved, startId, flags, args);
<span class="lineno"> 59 </span>      list.add(serviceStartArg);
<span class="lineno"> 60 </span>      IApplicationThreadOreo.scheduleServiceArgs.call(appThread, token, ParceledListSliceCompat.create(list));
<span class="caretline"><span class="lineno"> 61 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 62 </span>      IApplicationThreadICSMR1.scheduleServiceArgs.call(appThread, token, taskRemoved, startId, flags, args);
<span class="lineno"> 63 </span>  } <span class="keyword">else</span> {
<span class="lineno"> 64 </span>      IApplicationThread.scheduleServiceArgs.call(appThread, token, startId, flags, args);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java</a>:35</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendMultipartTextForSubscriber"</span>, <span class="number">1</span>));
<span class="lineno"> 33 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendStoredText"</span>, <span class="number">1</span>));
<span class="lineno"> 34 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendStoredMultipartText"</span>, <span class="number">1</span>));
<span class="caretline"><span class="lineno"> 35 </span>  } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceCallingPkgMethodProxy(<span class="string">"getAllMessagesFromIccEf"</span>));
<span class="lineno"> 37 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"getAllMessagesFromIccEfForSubscriber"</span>, <span class="number">1</span>));
<span class="lineno"> 38 </span>      addMethodProxy(<span class="keyword">new</span> ReplaceCallingPkgMethodProxy(<span class="string">"updateMessageOnIccEf"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/isms/ISmsStub.java</a>:50</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 47 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendMultipartTextForSubscriber"</span>, <span class="number">1</span>));
<span class="lineno"> 48 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendStoredText"</span>, <span class="number">1</span>));
<span class="lineno"> 49 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceSpecPkgMethodProxy(<span class="string">"sendStoredMultipartText"</span>, <span class="number">1</span>));
<span class="caretline"><span class="lineno"> 50 </span>   } <span class="keyword">else</span> <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 51 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceCallingPkgMethodProxy(<span class="string">"getAllMessagesFromIccEf"</span>));
<span class="lineno"> 52 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceCallingPkgMethodProxy(<span class="string">"updateMessageOnIccEf"</span>));
<span class="lineno"> 53 </span>       addMethodProxy(<span class="keyword">new</span> ReplaceCallingPkgMethodProxy(<span class="string">"copyMessageToIccEf"</span>));
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/proxies/input/InputMethodManagerStub.java">../../src/main/java/com/lody/virtual/client/hook/proxies/input/InputMethodManagerStub.java</a>:17</span>: <span class="message">Unnecessary; SDK_INT is always >= 16</span><br /><pre class="errorlines">
<span class="lineno"> 14 </span><span class="javadoc"> * <AUTHOR>
</span><span class="lineno"> 15 </span><span class="javadoc"> */</span>
<span class="lineno"> 16 </span><span class="annotation">@Inject</span>(MethodProxies.<span class="keyword">class</span>)
<span class="caretline"><span class="lineno"> 17 </span><span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 18 </span>
<span class="lineno"> 19 </span><span class="keyword">public</span> <span class="keyword">class</span> InputMethodManagerStub <span class="keyword">extends</span> BinderInvocationProxy {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java">../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java</a>:247</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 244 </span>        base.callActivityOnCreate(activity, icicle);
<span class="lineno"> 245 </span>    }
<span class="lineno"> 246 </span>
<span class="caretline"><span class="lineno"> 247 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 248 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 249 </span>    <span class="keyword">public</span> <span class="keyword">void</span> callActivityOnCreate(Activity activity, Bundle icicle, PersistableBundle persistentState) {
<span class="lineno"> 250 </span>        base.callActivityOnCreate(activity, icicle, persistentState);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java">../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java</a>:263</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 260 </span>        base.callActivityOnRestoreInstanceState(activity, savedInstanceState);
<span class="lineno"> 261 </span>    }
<span class="lineno"> 262 </span>
<span class="caretline"><span class="lineno"> 263 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 264 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 265 </span>    <span class="keyword">public</span> <span class="keyword">void</span> callActivityOnRestoreInstanceState(Activity activity, Bundle savedInstanceState,
<span class="lineno"> 266 </span>            PersistableBundle persistentState) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java">../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java</a>:275</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 272 </span>        base.callActivityOnPostCreate(activity, icicle);
<span class="lineno"> 273 </span>    }
<span class="lineno"> 274 </span>
<span class="caretline"><span class="lineno"> 275 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 276 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 277 </span>    <span class="keyword">public</span> <span class="keyword">void</span> callActivityOnPostCreate(Activity activity, Bundle icicle, PersistableBundle persistentState) {
<span class="lineno"> 278 </span>        base.callActivityOnPostCreate(activity, icicle, persistentState);
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java">../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java</a>:312</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 309 </span>        base.callActivityOnSaveInstanceState(activity, outState);
<span class="lineno"> 310 </span>    }
<span class="lineno"> 311 </span>
<span class="caretline"><span class="lineno"> 312 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.LOLLIPOP)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 313 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 314 </span>    <span class="keyword">public</span> <span class="keyword">void</span> callActivityOnSaveInstanceState(Activity activity, Bundle outState,
<span class="lineno"> 315 </span>            PersistableBundle outPersistentState) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java">../../src/main/java/com/lody/virtual/client/hook/delegate/InstrumentationDelegate.java</a>:340</span>: <span class="message">Unnecessary; SDK_INT is always >= 18</span><br /><pre class="errorlines">
<span class="lineno"> 337 </span>    }
<span class="lineno"> 338 </span>
<span class="lineno"> 339 </span>
<span class="caretline"><span class="lineno"> 340 </span>    <span class="warning"><span class="annotation">@TargetApi</span>(Build.VERSION_CODES.JELLY_BEAN_MR2)</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 341 </span>    <span class="annotation">@Override</span>
<span class="lineno"> 342 </span>    <span class="keyword">public</span> UiAutomation getUiAutomation() {
<span class="lineno"> 343 </span>        <span class="keyword">return</span> base.getUiAutomation();
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/pm/IntentResolver.java">../../src/main/java/com/lody/virtual/server/pm/IntentResolver.java</a>:149</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 146 </span>        <span class="keyword">if</span> (s1 != s2) {
<span class="lineno"> 147 </span>            <span class="keyword">return</span> <span class="keyword">false</span>;
<span class="lineno"> 148 </span>        }
<span class="caretline"><span class="lineno"> 149 </span>        <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 150 </span>            s1 = f1.countDataSchemeSpecificParts();
<span class="lineno"> 151 </span>            s2 = f2.countDataSchemeSpecificParts();
<span class="lineno"> 152 </span>            <span class="keyword">if</span> (s1 != s2) {
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java">../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java</a>:146</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 143 </span>            addInjector(<span class="keyword">new</span> ContentServiceStub());
<span class="lineno"> 144 </span>            addInjector(<span class="keyword">new</span> ConnectivityStub());
<span class="lineno"> 145 </span>
<span class="caretline"><span class="lineno"> 146 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= JELLY_BEAN_MR2</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 147 </span>                addInjector(<span class="keyword">new</span> VibratorStub());
<span class="lineno"> 148 </span>                addInjector(<span class="keyword">new</span> WifiManagerStub());
<span class="lineno"> 149 </span>                addInjector(<span class="keyword">new</span> BluetoothStub());
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java">../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java</a>:152</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 149 </span>                addInjector(<span class="keyword">new</span> BluetoothStub());
<span class="lineno"> 150 </span>                addInjector(<span class="keyword">new</span> ContextHubServiceStub());
<span class="lineno"> 151 </span>            }
<span class="caretline"><span class="lineno"> 152 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= JELLY_BEAN_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 153 </span>                addInjector(<span class="keyword">new</span> UserManagerStub());
<span class="lineno"> 154 </span>            }
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java">../../src/main/java/com/lody/virtual/client/core/InvocationStubManager.java</a>:156</span>: <span class="message">Unnecessary; SDK_INT is always >= 21</span><br /><pre class="errorlines">
<span class="lineno"> 153 </span>                addInjector(<span class="keyword">new</span> UserManagerStub());
<span class="lineno"> 154 </span>            }
<span class="lineno"> 155 </span>
<span class="caretline"><span class="lineno"> 156 </span>            <span class="keyword">if</span> (<span class="warning">Build.VERSION.SDK_INT >= JELLY_BEAN_MR1</span>) {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 157 </span>                addInjector(<span class="keyword">new</span> DisplayStub());
<span class="lineno"> 158 </span>            }
<span class="lineno"> 159 </span>            <span class="keyword">if</span> (Build.VERSION.SDK_INT >= LOLLIPOP) {
</pre>

<br/><b>NOTE: 86 results omitted.</b><br/><br/></div>
</div>
<div class="metadata"><div class="explanation" id="explanationObsoleteSdkInt" style="display: none;">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ObsoleteSdkInt</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationObsoleteSdkIntLink" onclick="reveal('explanationObsoleteSdkInt');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ObsoleteSdkIntCardLink" onclick="hideid('ObsoleteSdkIntCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="StaticFieldLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="StaticFieldLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Static Field Leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/AttributeCache.java">../../src/main/java/com/lody/virtual/server/am/AttributeCache.java</a>:36</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>AttributeCache</code> which has field <code>mContext</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno">  33 </span><span class="javadoc"> * special calls from the activity manager to clear it.
</span><span class="lineno">  34 </span><span class="javadoc"> */</span>
<span class="lineno">  35 </span><span class="keyword">public</span> <span class="keyword">final</span> <span class="keyword">class</span> AttributeCache {
<span class="caretline"><span class="lineno">  36 </span>    <span class="keyword">private</span> <span class="warning"><span class="keyword">static</span></span> AttributeCache sInstance = <span class="keyword">null</span>;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  37 </span>
<span class="lineno">  38 </span>    <span class="keyword">private</span> <span class="keyword">final</span> Context mContext;
<span class="lineno">  39 </span>    <span class="keyword">private</span> <span class="keyword">final</span> WeakHashMap&lt;String, Package> mPackages = <span class="keyword">new</span> WeakHashMap&lt;String, Package>();
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/server/am/BroadcastSystem.java">../../src/main/java/com/lody/virtual/server/am/BroadcastSystem.java</a>:54</span>: <span class="message">Do not place Android context classes in static fields (static reference to <code>BroadcastSystem</code> which has field <code>mContext</code> pointing to <code>Context</code>); this is a memory leak</span><br /><pre class="errorlines">
<span class="lineno">  51 </span><span class="javadoc">     * MUST &lt; 10000.
</span><span class="lineno">  52 </span><span class="javadoc">     */</span>
<span class="lineno">  53 </span>    <span class="keyword">private</span> <span class="keyword">static</span> <span class="keyword">final</span> <span class="keyword">int</span> BROADCAST_TIME_OUT = <span class="number">8500</span>;
<span class="caretline"><span class="lineno">  54 </span>    <span class="keyword">private</span> <span class="warning"><span class="keyword">static</span></span> BroadcastSystem gDefault;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  55 </span>
<span class="lineno">  56 </span>    <span class="keyword">private</span> <span class="keyword">final</span> ArrayMap&lt;String, List&lt;BroadcastReceiver>> mReceivers = <span class="keyword">new</span> ArrayMap&lt;>();
<span class="lineno">  57 </span>    <span class="keyword">private</span> <span class="keyword">final</span> Map&lt;IBinder, BroadcastRecord> mBroadcastRecords = <span class="keyword">new</span> HashMap&lt;>();
</pre>

<span class="location"><a href="../../src/main/java/com/lody/virtual/client/stub/ResolverActivity.java">../../src/main/java/com/lody/virtual/client/stub/ResolverActivity.java</a>:734</span>: <span class="message">This <code>AsyncTask</code> class should be static or leaks might occur (com.lody.virtual.client.stub.ResolverActivity.LoadIconTask)</span><br /><pre class="errorlines">
<span class="lineno"> 731 </span>
<span class="lineno"> 732 </span>    }
<span class="lineno"> 733 </span>
<span class="caretline"><span class="lineno"> 734 </span>    <span class="keyword">class</span> <span class="warning">LoadIconTask</span> <span class="keyword">extends</span> AsyncTask&lt;DisplayResolveInfo, Void, DisplayResolveInfo> {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 735 </span>        <span class="annotation">@Override</span>
<span class="lineno"> 736 </span>        <span class="keyword">protected</span> DisplayResolveInfo doInBackground(DisplayResolveInfo... params) {
<span class="lineno"> 737 </span>            <span class="keyword">final</span> DisplayResolveInfo info = params[<span class="number">0</span>];
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationStaticFieldLeak" style="display: none;">
A static field will leak contexts.<br/>
<br/>
Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a <code>Fragment</code> or <code>Activity</code>, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.<br/>
<br/>
Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.<br/>
<br/>
ViewModel classes should never point to Views or non-application Contexts.<br/>To suppress this error, use the issue id "StaticFieldLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">StaticFieldLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationStaticFieldLeakLink" onclick="reveal('explanationStaticFieldLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="StaticFieldLeakCardLink" onclick="hideid('StaticFieldLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UseCompoundDrawables"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UseCompoundDrawablesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Node can be replaced by a TextView with compound drawables</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:3</span>: <span class="message">This tag and its children can be replaced by one <code>&lt;TextView/></code> and a compound drawable</span><br /><pre class="errorlines">
<span class="lineno">  1 </span><span class="prologue">&lt;?xml version="1.0" encoding="utf-8"?></span>
<span class="lineno">  2 </span>
<span class="caretline"><span class="lineno">  3 </span><span class="tag">&lt;</span><span class="warning"><span class="tag">LinearLayout</span></span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno">  4 </span>    <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  5 </span>    <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno">  6 </span>    <span class="prefix">android:</span><span class="attribute">paddingLeft</span>=<span class="value">"16dp"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUseCompoundDrawables" style="display: none;">
A <code>LinearLayout</code> which contains an <code>ImageView</code> and a <code>TextView</code> can be more efficiently handled as a compound drawable (a single TextView, using the <code>drawableTop</code>, <code>drawableLeft</code>, <code>drawableRight</code> and/or <code>drawableBottom</code> attributes to draw one or more images adjacent to the text).<br/>
<br/>
If the two widgets are offset from each other with margins, this can be replaced with a <code>drawablePadding</code> attribute.<br/>
<br/>
There's a lint quickfix to perform this conversion in the Eclipse plugin.<br/>To suppress this error, use the issue id "UseCompoundDrawables" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UseCompoundDrawables</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUseCompoundDrawablesLink" onclick="reveal('explanationUseCompoundDrawables');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UseCompoundDrawablesCardLink" onclick="hideid('UseCompoundDrawablesCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="HandlerLeak"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HandlerLeakCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Handler reference leaks</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/java/com/lody/virtual/client/VClientImpl.java">../../src/main/java/com/lody/virtual/client/VClientImpl.java</a>:689</span>: <span class="message">This <code>Handler</code> class should be static or leaks might occur (com.lody.virtual.client.VClientImpl.H)</span><br /><pre class="errorlines">
<span class="lineno"> 686 </span>        String processName;
<span class="lineno"> 687 </span>    }
<span class="lineno"> 688 </span>
<span class="caretline"><span class="lineno"> 689 </span>    <span class="keyword">private</span> <span class="keyword">class</span> <span class="warning">H</span> <span class="keyword">extends</span> Handler {&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 690 </span>
<span class="lineno"> 691 </span>        <span class="keyword">private</span> H() {
<span class="lineno"> 692 </span>            <span class="keyword">super</span>(Looper.getMainLooper());
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHandlerLeak" style="display: none;">
Since this Handler is declared as an inner class, it may prevent the outer class from being garbage collected. If the Handler is using a <code>Looper</code> or <code>MessageQueue</code> for a thread other than the main thread, then there is no issue. If the <code>Handler</code> is using the <code>Looper</code> or <code>MessageQueue</code> of the main thread, you need to fix your <code>Handler</code> declaration, as follows: Declare the <code>Handler</code> as a static class; In the outer class, instantiate a <code>WeakReference</code> to the outer class and pass this object to your <code>Handler</code> when you instantiate the <code>Handler</code>; Make all references to members of the outer class using the <code>WeakReference</code> object.<br/>To suppress this error, use the issue id "HandlerLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HandlerLeak</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 4/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHandlerLeakLink" onclick="reveal('explanationHandlerLeak');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HandlerLeakCardLink" onclick="hideid('HandlerLeakCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="InefficientWeight"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="InefficientWeightCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Inefficient layout weight</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_type_and_account.xml">../../src/main/res/layout/choose_type_and_account.xml</a>:24</span>: <span class="message">Use a <code>layout_height</code> of <code>0dp</code> instead of <code>wrap_content</code> for better performance</span><br /><pre class="errorlines">
<span class="lineno"> 21 </span>    <span class="tag">&lt;ListView</span><span class="attribute">
</span><span class="lineno"> 22 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@android:id/list"</span>
<span class="lineno"> 23 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="caretline"><span class="lineno"> 24 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 26 </span>        <span class="prefix">android:</span><span class="attribute">choiceMode</span>=<span class="value">"singleChoice"</span>
<span class="lineno"> 27 </span>        <span class="prefix">android:</span><span class="attribute">drawSelectorOnTop</span>=<span class="value">"false"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationInefficientWeight" style="display: none;">
When only a single widget in a <code>LinearLayout</code> defines a weight, it is more efficient to assign a width/height of <code>0dp</code> to it since it will absorb all the remaining space anyway. With a declared width/height of <code>0dp</code> it does not have to measure its own size first.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InefficientWeight" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">InefficientWeight</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationInefficientWeightLink" onclick="reveal('explanationInefficientWeight');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="InefficientWeightCardLink" onclick="hideid('InefficientWeightCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RedundantNamespace"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RedundantNamespaceCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Redundant namespace</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:18</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"8dip"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dip"</span> />
<span class="lineno"> 17 </span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_text"</span>
<span class="lineno"> 20 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span></pre>

<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:39</span>: <span class="message">This namespace declaration is redundant</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"12dp"</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">scaleType</span>=<span class="value">"fitCenter"</span>/>
<span class="lineno"> 38 </span>
<span class="caretline"><span class="lineno"> 39 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>                  <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 41 </span>                  <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 42 </span>                  <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRedundantNamespace" style="display: none;">
In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RedundantNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RedundantNamespace</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRedundantNamespaceLink" onclick="reveal('explanationRedundantNamespace');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RedundantNamespaceCardLink" onclick="hideid('RedundantNamespaceCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="UnusedNamespace"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="UnusedNamespaceCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Unused namespace</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:18</span>: <span class="message">Unused namespace declaration xmlns:android; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"8dip"</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dip"</span> />
<span class="lineno"> 17 </span>
<span class="caretline"><span class="lineno"> 18 </span>    <span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_text"</span>
<span class="lineno"> 20 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span></pre>

<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:39</span>: <span class="message">Unused namespace declaration xmlns:android; already declared on the root element</span><br /><pre class="errorlines">
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"12dp"</span>
<span class="lineno"> 37 </span>        <span class="prefix">android:</span><span class="attribute">scaleType</span>=<span class="value">"fitCenter"</span>/>
<span class="lineno"> 38 </span>
<span class="caretline"><span class="lineno"> 39 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="warning"><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 40 </span>                  <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 41 </span>                  <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 42 </span>                  <span class="prefix">android:</span><span class="attribute">layout_marginLeft</span>=<span class="value">"8dp"</span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationUnusedNamespace" style="display: none;">
Unused namespace declarations take up space and require processing that is not necessary<br/>To suppress this error, use the issue id "UnusedNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">UnusedNamespace</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Performance</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 1/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationUnusedNamespaceLink" onclick="reveal('explanationUnusedNamespace');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="UnusedNamespaceCardLink" onclick="hideid('UnusedNamespaceCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Usability"></a>
<a name="ButtonCase"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ButtonCaseCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Cancel/OK dialog button capitalization</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/app_not_authorized.xml">../../src/main/res/layout/app_not_authorized.xml</a>:53</span>: <span class="message">@android:string/yes actually returns "OK", not "Yes"; use @android:string/ok instead or create a local string resource for Yes</span><br /><pre class="errorlines">
<span class="lineno"> 50 </span>            <span class="attribute">style</span>=<span class="value">"?android:attr/buttonBarButtonStyle"</span>
<span class="lineno"> 51 </span>            <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"0dp"</span> <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 52 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="caretline"><span class="lineno"> 53 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@android:string/yes"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 54 </span>            <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"onCancelButtonClicked"</span> />
<span class="lineno"> 55 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 56 </span><span class="tag">&lt;/LinearLayout></span></pre>

<span class="location"><a href="../../src/main/res/layout/choose_type_and_account.xml">../../src/main/res/layout/choose_type_and_account.xml</a>:51</span>: <span class="message">@android:string/no actually returns "Cancel", not "No"; use @android:string/cancel instead or create a local string resource for No</span><br /><pre class="errorlines">
<span class="lineno"> 48 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 49 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 50 </span>            <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"onCancelButtonClicked"</span>
<span class="caretline"><span class="lineno"> 51 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@android:string/no"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 52 </span>
<span class="lineno"> 53 </span>        <span class="tag">&lt;Button</span><span class="attribute">
</span><span class="lineno"> 54 </span><span class="attribute">            </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@android:id/button2"</span></pre>

<span class="location"><a href="../../src/main/res/layout/choose_type_and_account.xml">../../src/main/res/layout/choose_type_and_account.xml</a>:60</span>: <span class="message">@android:string/yes actually returns "OK", not "Yes"; use @android:string/ok instead or create a local string resource for Yes</span><br /><pre class="errorlines">
<span class="lineno"> 57 </span>            <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 58 </span>            <span class="prefix">android:</span><span class="attribute">layout_weight</span>=<span class="value">"1"</span>
<span class="lineno"> 59 </span>            <span class="prefix">android:</span><span class="attribute">onClick</span>=<span class="value">"onOkButtonClicked"</span>
<span class="caretline"><span class="lineno"> 60 </span>            <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"@android:string/yes"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 61 </span>    <span class="tag">&lt;/LinearLayout></span>
<span class="lineno"> 62 </span><span class="tag">&lt;/LinearLayout></span>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationButtonCase" style="display: none;">
The standard capitalization for OK/Cancel dialogs is "OK" and "Cancel". To ensure that your dialogs use the standard strings, you can use the resource strings @android:string/ok and @android:string/cancel.<br/>To suppress this error, use the issue id "ButtonCase" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ButtonCase</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Usability</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 2/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationButtonCaseLink" onclick="reveal('explanationButtonCase');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ButtonCaseCardLink" onclick="hideid('ButtonCaseCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Accessibility"></a>
<a name="ContentDescription"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="ContentDescriptionCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Image without contentDescription</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:12</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  9 </span>    <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"16dp"</span>
<span class="lineno"> 10 </span>    <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span> >
<span class="lineno"> 11 </span>
<span class="caretline"><span class="lineno"> 12 </span>   <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_icon"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"fill_parent"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"8dip"</span></pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:6</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">   3 </span>             <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   4 </span>             <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span>>
<span class="lineno">   5 </span>
<span class="caretline"><span class="lineno">   6 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">   7 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/im_main"</span>
<span class="lineno">   8 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">   9 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span></pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:20</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  17 </span><span class="attribute">            style</span>=<span class="value">"@style/notification_layout"</span>
<span class="lineno">  18 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  19 </span>
<span class="caretline"><span class="lineno">  20 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  21 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_1"</span>
<span class="lineno">  22 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:24</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  21 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_1"</span>
<span class="lineno">  22 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  23 </span>
<span class="caretline"><span class="lineno">  24 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  25 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_2"</span>
<span class="lineno">  26 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:28</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  25 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_2"</span>
<span class="lineno">  26 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  27 </span>
<span class="caretline"><span class="lineno">  28 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  29 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_3"</span>
<span class="lineno">  30 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<button class="mdl-button mdl-js-button mdl-button--primary" id="ContentDescriptionDivLink" onclick="reveal('ContentDescriptionDiv');" />+ 31 More Occurrences...</button>
<div id="ContentDescriptionDiv" style="display: none">
<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:32</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  29 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_3"</span>
<span class="lineno">  30 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  31 </span>
<span class="caretline"><span class="lineno">  32 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  33 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_4"</span>
<span class="lineno">  34 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:36</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  33 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_4"</span>
<span class="lineno">  34 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  35 </span>
<span class="caretline"><span class="lineno">  36 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_5"</span>
<span class="lineno">  38 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:40</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  37 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_5"</span>
<span class="lineno">  38 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  39 </span>
<span class="caretline"><span class="lineno">  40 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  41 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_6"</span>
<span class="lineno">  42 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:44</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  41 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_6"</span>
<span class="lineno">  42 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  43 </span>
<span class="caretline"><span class="lineno">  44 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  45 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_7"</span>
<span class="lineno">  46 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:48</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  45 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_7"</span>
<span class="lineno">  46 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  47 </span>
<span class="caretline"><span class="lineno">  48 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  49 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_8"</span>
<span class="lineno">  50 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:58</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  55 </span><span class="attribute">            style</span>=<span class="value">"@style/notification_layout"</span>
<span class="lineno">  56 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  57 </span>
<span class="caretline"><span class="lineno">  58 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  59 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_9"</span>
<span class="lineno">  60 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:62</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  59 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_9"</span>
<span class="lineno">  60 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  61 </span>
<span class="caretline"><span class="lineno">  62 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  63 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_10"</span>
<span class="lineno">  64 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:66</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  63 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_10"</span>
<span class="lineno">  64 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  65 </span>
<span class="caretline"><span class="lineno">  66 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  67 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_11"</span>
<span class="lineno">  68 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:70</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  67 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_11"</span>
<span class="lineno">  68 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  69 </span>
<span class="caretline"><span class="lineno">  70 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  71 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_12"</span>
<span class="lineno">  72 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:74</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  71 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_12"</span>
<span class="lineno">  72 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  73 </span>
<span class="caretline"><span class="lineno">  74 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  75 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_13"</span>
<span class="lineno">  76 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:78</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  75 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_13"</span>
<span class="lineno">  76 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  77 </span>
<span class="caretline"><span class="lineno">  78 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  79 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_14"</span>
<span class="lineno">  80 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:82</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  79 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_14"</span>
<span class="lineno">  80 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  81 </span>
<span class="caretline"><span class="lineno">  82 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  83 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_15"</span>
<span class="lineno">  84 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:86</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  83 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_15"</span>
<span class="lineno">  84 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  85 </span>
<span class="caretline"><span class="lineno">  86 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  87 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_16"</span>
<span class="lineno">  88 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:96</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  93 </span><span class="attribute">            style</span>=<span class="value">"@style/notification_layout"</span>
<span class="lineno">  94 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno">  95 </span>
<span class="caretline"><span class="lineno">  96 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  97 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_17"</span>
<span class="lineno">  98 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:100</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  97 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_17"</span>
<span class="lineno">  98 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno">  99 </span>
<span class="caretline"><span class="lineno"> 100 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 101 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_18"</span>
<span class="lineno"> 102 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:104</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 101 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_18"</span>
<span class="lineno"> 102 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 103 </span>
<span class="caretline"><span class="lineno"> 104 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 105 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_19"</span>
<span class="lineno"> 106 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:108</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 105 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_19"</span>
<span class="lineno"> 106 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 107 </span>
<span class="caretline"><span class="lineno"> 108 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 109 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_20"</span>
<span class="lineno"> 110 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:112</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 109 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_20"</span>
<span class="lineno"> 110 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 111 </span>
<span class="caretline"><span class="lineno"> 112 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 113 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_21"</span>
<span class="lineno"> 114 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:116</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 113 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_21"</span>
<span class="lineno"> 114 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 115 </span>
<span class="caretline"><span class="lineno"> 116 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 117 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_22"</span>
<span class="lineno"> 118 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:120</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 117 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_22"</span>
<span class="lineno"> 118 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 119 </span>
<span class="caretline"><span class="lineno"> 120 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 121 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_23"</span>
<span class="lineno"> 122 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:124</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 121 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_23"</span>
<span class="lineno"> 122 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 123 </span>
<span class="caretline"><span class="lineno"> 124 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 125 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_24"</span>
<span class="lineno"> 126 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:134</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 131 </span><span class="attribute">            style</span>=<span class="value">"@style/notification_layout"</span>
<span class="lineno"> 132 </span>            <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"horizontal"</span>>
<span class="lineno"> 133 </span>
<span class="caretline"><span class="lineno"> 134 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 135 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_25"</span>
<span class="lineno"> 136 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:138</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 135 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_25"</span>
<span class="lineno"> 136 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 137 </span>
<span class="caretline"><span class="lineno"> 138 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 139 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_26"</span>
<span class="lineno"> 140 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:142</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 139 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_26"</span>
<span class="lineno"> 140 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 141 </span>
<span class="caretline"><span class="lineno"> 142 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 143 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_27"</span>
<span class="lineno"> 144 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:146</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 143 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_27"</span>
<span class="lineno"> 144 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 145 </span>
<span class="caretline"><span class="lineno"> 146 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 147 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_28"</span>
<span class="lineno"> 148 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:150</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 147 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_28"</span>
<span class="lineno"> 148 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 149 </span>
<span class="caretline"><span class="lineno"> 150 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 151 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_29"</span>
<span class="lineno"> 152 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:154</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 151 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_29"</span>
<span class="lineno"> 152 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 153 </span>
<span class="caretline"><span class="lineno"> 154 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 155 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_30"</span>
<span class="lineno"> 156 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:158</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 155 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_30"</span>
<span class="lineno"> 156 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 157 </span>
<span class="caretline"><span class="lineno"> 158 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 159 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_31"</span>
<span class="lineno"> 160 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification.xml">../../src/main/res/layout/custom_notification.xml</a>:162</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 159 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_31"</span>
<span class="lineno"> 160 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
<span class="lineno"> 161 </span>
<span class="caretline"><span class="lineno"> 162 </span>            <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 163 </span><span class="attribute">                </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/btn_32"</span>
<span class="lineno"> 164 </span>                <span class="attribute">style</span>=<span class="value">"@style/notification_button"</span>/>
</pre>

<span class="location"><a href="../../src/main/res/layout/custom_notification_lite.xml">../../src/main/res/layout/custom_notification_lite.xml</a>:6</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno">  3 </span>             <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  4 </span>             <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span> >
<span class="lineno">  5 </span>
<span class="caretline"><span class="lineno">  6 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno">  7 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/im_main"</span>
<span class="lineno">  8 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"match_parent"</span>
<span class="lineno">  9 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"match_parent"</span></pre>

<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:29</span>: <span class="message">Missing <code>contentDescription</code> attribute on image</span><br /><pre class="errorlines">
<span class="lineno"> 26 </span>
<span class="lineno"> 27 </span>    <span class="comment">&lt;!-- Activity icon when presenting dialog
</span><span class="lineno"> 28 </span><span class="comment">         Size will be filled in by ResolverActivity --></span>
<span class="caretline"><span class="lineno"> 29 </span>    <span class="tag">&lt;</span><span class="warning"><span class="tag">ImageView</span></span><span class="attribute">
</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="attribute">
</span><span class="lineno"> 30 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/icon"</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"24dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"24dp"</span>
</pre>

</div>
</div>
<div class="metadata"><div class="explanation" id="explanationContentDescription" style="display: none;">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to <code>@null</code>. If your app's minSdkVersion is 16 or higher, you can instead set these graphical elements' <code>android:importantForAccessibility</code> attributes to <code>no</code>.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">ContentDescription</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Accessibility</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationContentDescriptionLink" onclick="reveal('explanationContentDescription');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="ContentDescriptionCardLink" onclick="hideid('ContentDescriptionCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization"></a>
<a name="HardcodedText"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedTextCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded text</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/app_not_authorized.xml">../../src/main/res/layout/app_not_authorized.xml</a>:35</span>: <span class="message">Hardcoded string "Change not allowed", should use <code>@string</code> resource</span><br /><pre class="errorlines">
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">paddingBottom</span>=<span class="value">"16dip"</span>
<span class="lineno"> 33 </span>        <span class="prefix">android:</span><span class="attribute">paddingStart</span>=<span class="value">"16dip"</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"16dip"</span>
<span class="caretline"><span class="lineno"> 35 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">text</span>=<span class="value">"Change not allowed"</span></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 36 </span>    />
<span class="lineno"> 37 </span>
<span class="lineno"> 38 </span>    <span class="comment">&lt;!-- Horizontal divider line --></span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedText" style="display: none;">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedText</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedTextLink" onclick="reveal('explanationHardcodedText');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedTextCardLink" onclick="hideid('HardcodedTextCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Internationalization:Bidirectional Text"></a>
<a name="RtlSymmetry"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlSymmetryCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Padding and margin symmetry</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:15</span>: <span class="message">When you define <code>paddingRight</code> you should probably also define <code>paddingLeft</code> for right-to-left symmetry</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>   <span class="tag">&lt;ImageView</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_icon"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"fill_parent"</span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingRight</span></span>=<span class="value">"8dip"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dip"</span> />
<span class="lineno"> 17 </span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></pre>

<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:16</span>: <span class="message">When you define <code>paddingEnd</code> you should probably also define <code>paddingStart</code> for right-to-left symmetry</span><br /><pre class="errorlines">
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"fill_parent"</span>
<span class="lineno"> 15 </span>        <span class="prefix">android:</span><span class="attribute">paddingRight</span>=<span class="value">"8dip"</span>
<span class="caretline"><span class="lineno"> 16 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingEnd</span></span>=<span class="value">"8dip"</span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 17 </span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 19 </span>        <span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_text"</span></pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRtlSymmetry" style="display: none;">
If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry.<br/>To suppress this error, use the issue id "RtlSymmetry" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlSymmetry</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 6/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlSymmetryLink" onclick="reveal('explanationRtlSymmetry');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlSymmetryCardLink" onclick="hideid('RtlSymmetryCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RtlHardcoded"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlHardcodedCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using left/right instead of start/end attributes</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/res/layout/choose_account_row.xml">../../src/main/res/layout/choose_account_row.xml</a>:15</span>: <span class="message">Redundant attribute <code>paddingRight</code>; already defining <code>paddingEnd</code> with <code>targetSdkVersion</code> 34</span><br /><pre class="errorlines">
<span class="lineno"> 12 </span>   <span class="tag">&lt;ImageView</span><span class="attribute"> </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/account_row_icon"</span>
<span class="lineno"> 13 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 14 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"fill_parent"</span>
<span class="caretline"><span class="lineno"> 15 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">paddingRight</span></span>=<span class="value">"8dip"</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 16 </span>        <span class="prefix">android:</span><span class="attribute">paddingEnd</span>=<span class="value">"8dip"</span> />
<span class="lineno"> 17 </span>
<span class="lineno"> 18 </span>    <span class="tag">&lt;TextView</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span></pre>

<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:33</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 30 </span><span class="attribute">        </span><span class="prefix">android:</span><span class="attribute">id</span>=<span class="value">"@+id/icon"</span>
<span class="lineno"> 31 </span>        <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"24dp"</span>
<span class="lineno"> 32 </span>        <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"24dp"</span>
<span class="caretline"><span class="lineno"> 33 </span>        <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 34 </span>        <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"start|center_vertical"</span>
<span class="lineno"> 35 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginBottom</span>=<span class="value">"12dp"</span>
<span class="lineno"> 36 </span>        <span class="prefix">android:</span><span class="attribute">layout_marginTop</span>=<span class="value">"12dp"</span>
</pre>

<span class="location"><a href="../../src/main/res/layout/resolve_list_item.xml">../../src/main/res/layout/resolve_list_item.xml</a>:42</span>: <span class="message">Consider replacing <code>android:layout_marginLeft</code> with <code>android:layout_marginStart="8dp"</code> to better support right-to-left layouts</span><br /><pre class="errorlines">
<span class="lineno"> 39 </span>    <span class="tag">&lt;LinearLayout</span><span class="attribute"> </span><span class="prefix">xmlns:</span><span class="attribute">android</span>=<span class="value">"http://schemas.android.com/apk/res/android"</span>
<span class="lineno"> 40 </span>                  <span class="prefix">android:</span><span class="attribute">layout_width</span>=<span class="value">"wrap_content"</span>
<span class="lineno"> 41 </span>                  <span class="prefix">android:</span><span class="attribute">layout_height</span>=<span class="value">"wrap_content"</span>
<span class="caretline"><span class="lineno"> 42 </span>                  <span class="warning"><span class="prefix">android:</span><span class="attribute">layout_marginLeft</span></span>=<span class="value">"8dp"</span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 43 </span>                  <span class="prefix">android:</span><span class="attribute">layout_gravity</span>=<span class="value">"start|center_vertical"</span>
<span class="lineno"> 44 </span>                  <span class="prefix">android:</span><span class="attribute">gravity</span>=<span class="value">"start|center_vertical"</span>
<span class="lineno"> 45 </span>                  <span class="prefix">android:</span><span class="attribute">orientation</span>=<span class="value">"vertical"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationRtlHardcoded" style="display: none;">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlHardcoded</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlHardcodedLink" onclick="reveal('explanationRtlHardcoded');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlHardcodedCardLink" onclick="hideid('RtlHardcodedCard');">
Dismiss</button>            </div>
            </div>
          </section><a name="RtlEnabled"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="RtlEnabledCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Using RTL attributes without enabling RTL support</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a></span>: <span class="message">The project references RTL attributes, but does not explicitly enable or disable RTL support with <code>android:supportsRtl</code> in the manifest</span><br />
</div>
<div class="metadata"><div class="explanation" id="explanationRtlEnabled" style="display: none;">
To enable right-to-left support, when running on API 17 and higher, you must set the <code>android:supportsRtl</code> attribute in the manifest <code>&lt;application></code> element.<br/>
<br/>
If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.<br/>To suppress this error, use the issue id "RtlEnabled" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">RtlEnabled</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Bidirectional Text</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Internationalization</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Warning</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 3/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationRtlEnabledLink" onclick="reveal('explanationRtlEnabled');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="RtlEnabledCardLink" onclick="hideid('RtlEnabledCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerify<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://material.io/design/">https://material.io/design/</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Previously, launcher icons were required to be in the PNG format but that restriction is no longer there, so lint now flags these.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultEncoding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Some APIs will implicitly use the default system character encoding instead of UTF-8 when converting to or from bytes, such as when creating a default <code>FileReader</code>.<br/>
<br/>
This is <i>usually</i> not correct; you only want to do this if you need to read files created by other programs where they have deliberately written in the same encoding. The default encoding varies from platform to platform and can vary from locale to locale, so this makes it difficult to interpret files containing non-ASCII characters.<br/>
<br/>
We recommend using UTF-8 everywhere.<br/>
<br/>
Note that on Android, the default file encoding is always UTF-8 (see <a href="https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(">https://developer.android.com/reference/java/nio/charset/Charset#defaultCharset(</a>) for more), so this lint check deliberately does not flag any problems in Android code, since it is always safe to rely on the default character encoding there.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a <code>get</code> prefix or for boolean-returning methods an <code>is</code> prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlincFE10<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
K2, the new version of Kotlin compiler, which encompasses the new frontend, is coming. Try to avoid using internal APIs from the old frontend if possible.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin, parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintDocExample<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Lint's tool for generating documentation for each issue has special support for including a code example which shows how to trigger the report. It will pick the first unit test it can find and pick out the source file referenced from the error message, but you can instead designate a unit test to be the documentation example, and in that case, all the files are included.<br/>
<br/>
To designate a unit test as the documentation example for an issue, name the test <code>testDocumentationExample</code>, or if your detector reports multiple issues, <code>testDocumentationExample</code>&lt;Id>, such as <code>testDocumentationExampleMyId</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>; use <code>isEquivalentTo(PsiElement)</code> instead.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields. These require the use of backticks to escape when calling from Kotlin. Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, ActionEvent's <code>getWhen()</code> method requires backticks when used from Kotlin:
<pre>
val timestamp = event.`when`
</pre>
<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoOp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check looks for code which looks like it's a no-op -- usually leftover expressions from interactive debugging, but in some cases bugs where you had intended to do something with the expression such as assign it to a field.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>pure-getters</b> (default is false):<br/>
Whether to assume methods with getter-names have no side effects.<br/>
<br/>
Getter methods (where names start with <code>get</code> or <code>is</code>, and have non-void return types, and no arguments) should not have side effects. With this option turned on, lint will assume that is the case and will list any getter calls whose results are ignored as suspicious code.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"NoOp"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"pure-getters"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionNamingConvention<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Permissions should be prefixed with an app's package name, using reverse-domain-style naming. This prefix should be followed by <code>.permission.</code>, and then a description of the capability that the permission represents, in upper SNAKE_CASE. For example, <code>com.example.myapp.permission.ENGAGE_HYPERSPACE</code>.<br/>
<br/>
Following this recommendation avoids naming collisions, and helps clearly identify the owner and intention of a custom permission.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatTrivial<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Every call to <code>String.format</code> creates a new <code>Formatter</code> instance, which will decrease the performance of your app. <code>String.format</code> should only be used when necessary--if the formatted string contains only trivial conversions (e.g. <code>b</code>, <code>s</code>, <code>c</code>) and there are no translation concerns, it will be more efficient to replace them and concatenate with <code>+</code>.<br/><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). This can make the text more readable. Note that you should never use grave accents and apostrophes to quote, `like this'. (Also note that you should not use curvy quotes for code fragments.)<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/><br/>
This check can be configured via the following options:<br/><br/>
<div class="options">
<b>ignore-deprecated</b> (default is false):<br/>
Whether to ignore classes and members that have been annotated with <code>@Deprecated</code>.<br/>
<br/>
Normally this lint check will flag all unannotated elements, but by setting this option to <code>true</code> it will skip any deprecated elements.<br/>
<br/>
To configure this option, use a `lint.xml` file in the project or source folder using an <code>&lt;option&gt;</code> block like the following:
<pre class="errorlines">
<span class="lineno"> 1 </span><span class="tag">&lt;lint></span>
<span class="lineno"> 2 </span>    <span class="tag">&lt;issue</span><span class="attribute"> id</span>=<span class="value">"UnknownNullness"</span>>
<span class="caretline"><span class="lineno"> 3 </span>        <span class="tag">&lt;option</span><span class="attribute"> name</span>=<span class="warning"><span class="value">"ignore-deprecated"</span> <span class="attribute">value</span>=<span class="value">"false"</span></span> />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 4 </span>    <span class="tag">&lt;/issue></span>
<span class="lineno"> 5 </span><span class="tag">&lt;/lint></span>
</pre>
</div><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/interop#nullability_annotations">https://developer.android.com/kotlin/interop#nullability_annotations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeImplicitIntentLaunch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This intent matches a non-exported component within the same app. In many cases, the app developer could instead use an explicit Intent to send messages to their internal components, ensuring that the messages are safely delivered without exposure to malicious apps on the device. Using such implicit intents will result in a crash in an upcoming version of Android.<br/><div class="moreinfo">More info: <a href="https://goo.gle/ImplicitIntentHijack">https://goo.gle/ImplicitIntentHijack</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported large screen hardware feature. Any &lt;uses-feature> not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a ChromeOS, large screen, or foldable device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.<br/>
<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div><br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div><br/>
<br/></div>
</div>
</div>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>