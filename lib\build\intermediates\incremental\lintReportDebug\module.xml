<lint-module
    format="1"
    dir="D:\ypb_xp\VirtualApp-master\VirtualApp-master\VirtualApp\lib"
    name=":lib"
    type="LIBRARY"
    maven="VirtualApp:lib:"
    agpVersion="8.1.4"
    buildFolder="build"
    bootClassPath="D:\test\platforms\android-34\android.jar;D:\test\build-tools\33.0.1\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="NewApi"
        severity="WARNING" />
      <severity
        id="OnClick"
        severity="WARNING" />
    </severities>
  </lintOptions>
  <variant name="debug"/>
</lint-module>
